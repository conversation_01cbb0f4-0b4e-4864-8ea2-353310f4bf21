/**
 * Binary Options Trading Bot - Standalone Interface
 * 
 * This script handles the standalone interface that runs in its own tab.
 * It communicates with the Pocket Option website through messages.
 */

// Global variables
let balance = 1000;
let initialBalance = 1000;
let riskPerTrade = 0.02;
let currentMode = 'quantum'; // Default mode: quantum edge
let payoutPercentage = 85; // Default payout percentage
let currentCurrencyPair = 'EUR/USD';
let isAutoTrading = false;
let activeTrade = false;
let autoTradeInterval;
let autoIntervalTime = 10; // seconds
let tradeTimeout;
let currentTrade = null;
let pocketOptionTab = null;

// Trading statistics
let totalTrades = 0;
let winningTrades = 0;
let losingTrades = 0;
let consecutiveWins = 0;
let consecutiveLosses = 0;

// DOM elements
const balanceElement = document.getElementById('balance');
const balanceChangeElement = document.getElementById('balance-change');
const riskSlider = document.getElementById('riskPerTrade');
const riskValue = document.getElementById('riskValue');
const expirySelect = document.getElementById('expiryTime');
const placeTradeBtn = document.getElementById('placeTrade');
const placeSellTradeBtn = document.getElementById('placeSellTrade');
const autoTradingBtn = document.getElementById('autoTrading');
const tradeInfoElement = document.getElementById('tradeInfo');
const winRateElement = document.getElementById('winRate');
const profitLossElement = document.getElementById('profitLoss');
const totalTradesElement = document.getElementById('totalTrades');
const connectBtn = document.getElementById('connectBtn');
const pocketOptionUrlInput = document.getElementById('pocketOptionUrl');

// Mode buttons
const quantumModeBtn = document.getElementById('mode-quantum');
const phoenixModeBtn = document.getElementById('mode-phoenix');
const neuralModeBtn = document.getElementById('mode-neural');

// Event listeners
riskSlider.addEventListener('input', () => {
    riskPerTrade = parseFloat(riskSlider.value) / 100;
    riskValue.textContent = `${riskSlider.value}%`;
    saveSettings();
});

placeTradeBtn.addEventListener('click', () => placeTrade('BUY'));
placeSellTradeBtn.addEventListener('click', () => placeTrade('SELL'));
autoTradingBtn.addEventListener('click', toggleAutoTrading);
connectBtn.addEventListener('click', connectToPocketOption);

// Mode selection event listeners
quantumModeBtn.addEventListener('click', () => setMode('quantum'));
phoenixModeBtn.addEventListener('click', () => setMode('phoenix'));
neuralModeBtn.addEventListener('click', () => setMode('neural'));

// Functions
function setMode(mode) {
    currentMode = mode;
    
    // Update UI
    quantumModeBtn.classList.remove('active');
    phoenixModeBtn.classList.remove('active');
    neuralModeBtn.classList.remove('active');
    
    if (mode === 'quantum') {
        quantumModeBtn.classList.add('active');
        updateExpiryOptions([60, 120, 300]);
    } 
    else if (mode === 'phoenix') {
        phoenixModeBtn.classList.add('active');
        updateExpiryOptions([60, 120, 300]);
    } 
    else if (mode === 'neural') {
        neuralModeBtn.classList.add('active');
        updateExpiryOptions([300, 900, 1800]);
    }
    
    showNotification(`Switched to ${mode.toUpperCase()} mode`, 'info');
    saveSettings();
}

function updateExpiryOptions(times) {
    // Clear existing options
    expirySelect.innerHTML = '';
    
    // Add new options
    times.forEach(time => {
        const option = document.createElement('option');
        option.value = time;
        
        if (time < 60) {
            option.textContent = `${time} seconds`;
        } else if (time < 3600) {
            option.textContent = `${time / 60} minute${time === 60 ? '' : 's'}`;
        } else {
            option.textContent = `${time / 3600} hour${time === 3600 ? '' : 's'}`;
        }
        
        expirySelect.appendChild(option);
    });
}

function placeTrade(direction) {
    if (activeTrade) {
        showNotification('Cannot place new trade, active trade in progress', 'error');
        return;
    }
    
    if (!pocketOptionTab) {
        showNotification('Please connect to Pocket Option first', 'error');
        return;
    }
    
    // Calculate trade amount based on strategy and mode
    const tradeAmount = calculatePositionSize();
    
    // Get expiry time from select
    const expiry = parseInt(expirySelect.value);
    
    // Set active trade
    activeTrade = true;
    const startTime = new Date();
    const endTime = new Date(startTime.getTime() + expiry * 1000);
    
    // Store current trade details
    currentTrade = {
        direction,
        amount: tradeAmount,
        expiry,
        startTime,
        endTime
    };
    
    // Update trade info
    updateTradeInfo(direction, tradeAmount, expiry, startTime, endTime);
    
    // Send message to Pocket Option tab to execute trade
    chrome.tabs.sendMessage(pocketOptionTab.id, { 
        action: 'executeTrade', 
        data: {
            direction,
            amount: tradeAmount,
            expiry,
            mode: currentMode
        }
    });
    
    showNotification(`Placing ${direction} trade for $${tradeAmount.toFixed(2)}`, 'info');
    
    // Simulate trade result after expiry time (for demo purposes)
    simulateTradeResult(expiry);
}

function calculatePositionSize() {
    let positionSize;
    
    if (currentMode === 'phoenix') {
        // Masaniello system
        const baseSize = balance * riskPerTrade;
        
        if (consecutiveLosses > 0) {
            // Increase size after losses to recover
            positionSize = baseSize * (1 + (consecutiveLosses * 0.5));
        } else {
            positionSize = baseSize;
        }
    } 
    else if (currentMode === 'neural') {
        // AI mode - more aggressive due to higher win rate
        positionSize = balance * (riskPerTrade * 1.5);
    }
    else {
        // Quantum mode - uses Kelly Criterion
        const winRate = getWinRateForCurrentMode();
        const payoutRatio = payoutPercentage / 100;
        
        const p = winRate;
        const b = payoutRatio;
        
        // Kelly percentage
        const kellyPercentage = (p * (1 + b) - 1) / b;
        
        // Apply safety factor (1/4 Kelly)
        const safeKelly = kellyPercentage * 0.25;
        
        // Cap at maximum risk per trade
        positionSize = Math.min(safeKelly, riskPerTrade) * balance;
    }
    
    return Math.round(positionSize * 100) / 100; // Round to 2 decimal places
}

function getWinRateForCurrentMode() {
    switch (currentMode) {
        case 'quantum': return 0.65;  // 65% win rate
        case 'phoenix': return 0.75;  // 75% win rate
        case 'neural': return 0.90;   // 90% win rate
        default: return 0.55;
    }
}

function simulateTradeResult(expiry) {
    // For demo purposes, simulate a trade result
    clearTimeout(tradeTimeout);
    
    tradeTimeout = setTimeout(() => {
        // Determine result based on mode win rate
        const winRate = getWinRateForCurrentMode();
        const result = Math.random() < winRate ? 'WIN' : 'LOSS';
        
        handleTradeResult(result);
    }, expiry * 1000);
}

function handleTradeResult(result) {
    if (!activeTrade) return;
    
    const tradeAmount = currentTrade.amount;
    
    // Update balance
    if (result === 'WIN') {
        const profit = tradeAmount * (payoutPercentage / 100);
        balance += profit;
        winningTrades++;
        consecutiveWins++;
        consecutiveLosses = 0;
        showNotification(`Trade WON: +$${profit.toFixed(2)}`, 'success');
    } else {
        balance -= tradeAmount;
        losingTrades++;
        consecutiveLosses++;
        consecutiveWins = 0;
        showNotification(`Trade LOST: -$${tradeAmount.toFixed(2)}`, 'error');
    }
    
    totalTrades++;
    
    // Reset active trade
    activeTrade = false;
    currentTrade = null;
    
    // Update UI
    updateBalance();
    updateTradeInfo();
    updateStatistics();
    saveSettings();
    
    // Continue auto trading if enabled
    if (isAutoTrading) {
        autoTradeInterval = setTimeout(() => {
            if (!activeTrade) {
                placeTrade(Math.random() < 0.5 ? 'BUY' : 'SELL');
            }
        }, autoIntervalTime * 1000);
    }
}

function toggleAutoTrading() {
    isAutoTrading = !isAutoTrading;
    
    if (isAutoTrading) {
        autoTradingBtn.textContent = 'STOP AUTO';
        autoTradingBtn.classList.add('active');
        
        // Start first trade immediately
        if (!activeTrade) {
            placeTrade(Math.random() < 0.5 ? 'BUY' : 'SELL');
        }
        
        showNotification(`Auto trading started`, 'info');
    } else {
        autoTradingBtn.innerHTML = '<i class="fas fa-robot"></i> AUTO';
        autoTradingBtn.classList.remove('active');
        
        // Clear auto trading interval
        if (autoTradeInterval) {
            clearTimeout(autoTradeInterval);
            autoTradeInterval = null;
        }
        
        showNotification('Auto trading stopped', 'info');
    }
    
    saveSettings();
}

function updateBalance() {
    balanceElement.textContent = `Balance: $${balance.toFixed(2)}`;
    
    // Update balance change percentage
    const changePercentage = ((balance - initialBalance) / initialBalance) * 100;
    balanceChangeElement.textContent = `${changePercentage >= 0 ? '+' : ''}${changePercentage.toFixed(2)}%`;
    
    if (changePercentage >= 0) {
        balanceChangeElement.classList.remove('negative');
    } else {
        balanceChangeElement.classList.add('negative');
    }
}

function updateTradeInfo(direction, amount, expiry, startTime, endTime) {
    if (!activeTrade) {
        tradeInfoElement.textContent = 'No active trade';
        return;
    }
    
    // Calculate remaining time
    const now = new Date();
    const remaining = Math.max(0, Math.round((endTime - now) / 1000));
    
    // Calculate potential profit
    const potentialProfit = amount * (payoutPercentage / 100);
    
    tradeInfoElement.innerHTML = `
        <p><strong>${currentCurrencyPair}</strong> - <span style="color: ${direction === 'BUY' ? '#10b981' : '#ef4444'}">${direction}</span></p>
        <p>Amount: $${amount.toFixed(2)}</p>
        <p>Profit if win: $${potentialProfit.toFixed(2)}</p>
        <p>Time left: ${formatTime(remaining)}</p>
    `;
    
    // Update remaining time every second
    if (remaining > 0) {
        setTimeout(() => {
            if (activeTrade) {
                updateTradeInfo(direction, amount, expiry, startTime, endTime);
            }
        }, 1000);
    }
}

function formatTime(seconds) {
    if (seconds < 60) {
        return `${seconds}s`;
    } else if (seconds < 3600) {
        const minutes = Math.floor(seconds / 60);
        const secs = seconds % 60;
        return `${minutes}m ${secs}s`;
    } else {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        return `${hours}h ${minutes}m`;
    }
}

function updateStatistics() {
    // Calculate win rate
    const winRate = totalTrades > 0 ? (winningTrades / totalTrades) * 100 : 0;
    
    // Calculate profit/loss percentage
    const profitLossPercentage = ((balance - initialBalance) / initialBalance) * 100;
    
    // Update UI
    winRateElement.textContent = `${winRate.toFixed(1)}%`;
    profitLossElement.textContent = `${profitLossPercentage.toFixed(1)}%`;
    profitLossElement.style.color = profitLossPercentage >= 0 ? 'var(--success-color)' : 'var(--danger-color)';
    totalTradesElement.textContent = totalTrades;
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    
    // Add icon based on type
    let icon = 'info-circle';
    if (type === 'success') icon = 'check-circle';
    if (type === 'error') icon = 'exclamation-circle';
    
    notification.innerHTML = `
        <i class="fas fa-${icon}"></i>
        <div>${message}</div>
    `;
    
    // Add to page
    document.body.appendChild(notification);
    
    // Show notification
    setTimeout(() => {
        notification.classList.add('show');
    }, 10);
    
    // Remove after animation completes
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

function saveSettings() {
    chrome.storage.local.set({
        settings: {
            balance,
            initialBalance,
            riskPerTrade,
            currentMode,
            isAutoTrading,
            totalTrades,
            winningTrades,
            losingTrades,
            consecutiveWins,
            consecutiveLosses
        }
    });
}

function loadSettings() {
    chrome.storage.local.get('settings', function(data) {
        if (data.settings) {
            const s = data.settings;
            
            balance = s.balance || balance;
            initialBalance = s.initialBalance || initialBalance;
            riskPerTrade = s.riskPerTrade || riskPerTrade;
            currentMode = s.currentMode || currentMode;
            totalTrades = s.totalTrades || 0;
            winningTrades = s.winningTrades || 0;
            losingTrades = s.losingTrades || 0;
            consecutiveWins = s.consecutiveWins || 0;
            consecutiveLosses = s.consecutiveLosses || 0;
            
            // Update UI
            riskSlider.value = riskPerTrade * 100;
            riskValue.textContent = `${(riskPerTrade * 100).toFixed(1)}%`;
            
            updateBalance();
            updateStatistics();
            setMode(currentMode);
            
            showNotification('Settings loaded', 'info');
        }
    });
}

function connectToPocketOption() {
    const url = pocketOptionUrlInput.value;
    
    if (!url.includes('pocketoption.com')) {
        showNotification('Please enter a valid Pocket Option URL', 'error');
        return;
    }
    
    // Open Pocket Option in a new tab
    chrome.tabs.create({ url }, function(tab) {
        pocketOptionTab = tab;
        showNotification('Connected to Pocket Option', 'success');
        
        // Update button
        connectBtn.textContent = 'Connected';
        connectBtn.style.backgroundColor = 'var(--success-color)';
        connectBtn.disabled = true;
    });
}

// Initialize UI
updateBalance();
updateStatistics();
setMode('quantum'); // Set default mode
loadSettings();

// Add initial notification
showNotification('Binary Options Trading Bot initialized', 'info');
