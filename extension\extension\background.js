/**
 * KOS Trading Bot - Background Script
 *
 * This script runs in the background and handles:
 * 1. Communication between popup and content scripts
 * 2. Monitoring trade outcomes
 * 3. Persisting settings and trade history
 */

// Listen for installation
chrome.runtime.onInstalled.addListener(function() {
    console.log('KOS Trading Bot installed');

    // Initialize default settings
    const defaultSettings = {
        balance: 0, // Will be set from the real balance
        initialBalance: 0, // Will be set from the real balance
        realBalanceDetected: false, // Flag to track if we've received a real balance
        riskPerTrade: 0.02,
        strategy: 'kelly',
        autoInterval: 10,
        currentMode: 'quantum',
        payoutPercentage: 85,
        isAutoTrading: false,
        tradeHistory: [],
        totalTrades: 0,
        winningTrades: 0,
        losingTrades: 0,
        consecutiveWins: 0,
        consecutiveLosses: 0,
        // Neural Pulse specific settings
        neuralPulseSettings: {
            autonomyLevel: 'semi',
            riskLevel: 'medium',
            targetProfit: 0, // Will be calculated based on balance
            aiConfidence: 0.75
        }
    };

    chrome.storage.local.set({ settings: defaultSettings });

    // Register web accessible resources
    console.log('Registering web accessible resources');
    const resources = [
        'popup.html',
        'popup.js',
        'popup.css',
        'floating.html',
        'floating.js',
        'floating.css',
        'standalone.html',
        'standalone.js',
        'pageScript.js',
        'quantum-edge.html',
        'quantum-edge.js',
        'neural-pulse.html',
        'neural-pulse.js'
    ];

    // Log the resources to ensure they're registered
    console.log('Registered resources:', resources);
});

// Listen for messages
chrome.runtime.onMessage.addListener(function(message, sender, sendResponse) {
    console.log('Background script received message:', message);

    // Forward messages between popup and content script
    if (message.action === 'tradeResult' || message.action === 'updateBalance' || message.action === 'connectionEstablished') {
        // Forward from content script to popup
        chrome.runtime.sendMessage(message);

        // If this is a balance update, store it in settings
        if (message.action === 'updateBalance' && message.balance) {
            chrome.storage.local.get('settings', function(data) {
                if (data.settings) {
                    const settings = data.settings;

                    // Update the balance
                    settings.balance = message.balance;

                    // If this is the first time we're getting a real balance, update the initial balance too
                    if (!settings.realBalanceDetected) {
                        settings.initialBalance = message.balance;
                        settings.realBalanceDetected = true;
                    }

                    // Save the updated settings
                    chrome.storage.local.set({ settings: settings });
                    console.log('Updated settings with new balance:', message.balance);
                }
            });
        }
    }

    return true;
});

// Listen for tab updates
chrome.tabs.onUpdated.addListener(function(tabId, changeInfo, tab) {
    // Check if we're on Pocket Option and the page has finished loading
    if (changeInfo.status === 'complete' && tab.url && tab.url.includes('pocketoption.com')) {
        console.log('Pocket Option page loaded');

        // Notify any open standalone interface that Pocket Option is loaded
        chrome.runtime.sendMessage({
            action: 'pocketOptionLoaded',
            tabId: tabId,
            url: tab.url
        });

        // Inject content script if not already injected
        chrome.scripting.executeScript({
            target: { tabId: tabId },
            function: () => {
                // Check if our content script is already running
                if (!window.botContentScriptInjected) {
                    console.log('Content script not detected, injecting manually');

                    // Set flag to prevent multiple injections
                    window.botContentScriptInjected = true;

                    // Dispatch a custom event that our content script can listen for
                    document.dispatchEvent(new CustomEvent('botContentScriptInitialize'));
                }
            }
        }).catch(err => console.error('Error injecting script:', err));
    }

    // Check if we're loading our standalone interface
    if (changeInfo.status === 'complete' && tab.url && tab.url.includes('standalone.html')) {
        console.log('Standalone interface loaded');

        // Find any open Pocket Option tabs to connect with
        chrome.tabs.query({url: "*://pocketoption.com/*"}, function(tabs) {
            if (tabs.length > 0) {
                // Send message to standalone interface with Pocket Option tab info
                chrome.tabs.sendMessage(tabId, {
                    action: 'pocketOptionTabsFound',
                    tabs: tabs
                });
            }
        });
    }

    // Check if we're loading the Neural Pulse interface directly
    if (changeInfo.status === 'complete' && tab.url && tab.url.includes('neural-pulse.html')) {
        console.log('Neural Pulse interface loaded directly');

        // Log success for debugging
        chrome.scripting.executeScript({
            target: { tabId: tabId },
            function: () => {
                console.log('Neural Pulse interface loaded successfully');

                // Add a message to the page for testing
                const testDiv = document.createElement('div');
                testDiv.style.padding = '20px';
                testDiv.style.backgroundColor = 'rgba(0,0,0,0.8)';
                testDiv.style.color = 'white';
                testDiv.style.position = 'fixed';
                testDiv.style.top = '10px';
                testDiv.style.left = '10px';
                testDiv.style.zIndex = '9999';
                testDiv.style.borderRadius = '5px';
                testDiv.innerHTML = '<h3>Neural Pulse Test Mode</h3><p>This is a direct load of the Neural Pulse interface for testing.</p>';
                document.body.appendChild(testDiv);
            }
        }).catch(err => console.error('Error injecting script into Neural Pulse:', err));
    }
});
