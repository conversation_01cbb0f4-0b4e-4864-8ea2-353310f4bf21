"""
Notification System for Binary Options Trading Bot

This module handles alerts and notifications for trade signals and results.
"""

import logging
import winsound
import threading
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('notification')

class NotificationSystem:
    """
    Notification system for the trading bot that provides audio and visual alerts.
    """
    
    def __init__(self, enable_sound=True):
        """
        Initialize the notification system.
        
        Args:
            enable_sound (bool): Whether to enable sound alerts
        """
        self.enable_sound = enable_sound
        self.notification_history = []
        
        # Sound frequencies for different alerts
        self.sounds = {
            'trade_placed': (800, 200),  # (frequency, duration)
            'trade_win': (1200, 300),
            'trade_loss': (400, 300),
            'alert': (600, 500)
        }
        
        logger.info(f"Notification system initialized with sound {'enabled' if enable_sound else 'disabled'}")
    
    def notify(self, message, notification_type='info', play_sound=None):
        """
        Send a notification with optional sound alert.
        
        Args:
            message (str): Notification message
            notification_type (str): Type of notification ('info', 'success', 'warning', 'error')
            play_sound (str): Sound to play (None to use default for notification type)
        """
        timestamp = datetime.now()
        
        # Log the notification
        log_method = getattr(logger, notification_type if notification_type in ('info', 'warning', 'error') else 'info')
        log_method(message)
        
        # Store in history
        self.notification_history.append({
            'timestamp': timestamp,
            'message': message,
            'type': notification_type
        })
        
        # Determine which sound to play
        sound_key = play_sound
        if sound_key is None:
            if notification_type == 'success':
                sound_key = 'trade_win'
            elif notification_type == 'error':
                sound_key = 'trade_loss'
            elif notification_type == 'warning':
                sound_key = 'alert'
            else:
                sound_key = 'trade_placed'
        
        # Play sound in a separate thread to avoid blocking
        if self.enable_sound and sound_key in self.sounds:
            threading.Thread(target=self._play_sound, args=(sound_key,), daemon=True).start()
    
    def _play_sound(self, sound_key):
        """
        Play a sound alert.
        
        Args:
            sound_key (str): Key of the sound to play
        """
        try:
            frequency, duration = self.sounds[sound_key]
            winsound.Beep(frequency, duration)
        except Exception as e:
            logger.error(f"Error playing sound: {e}")
    
    def notify_trade_placed(self, trade_details):
        """
        Send notification for a new trade.
        
        Args:
            trade_details (dict): Details of the placed trade
        """
        direction = trade_details['direction'].upper()
        amount = trade_details['amount']
        expiry = trade_details['expiry']
        
        message = f"New trade: {direction} for {amount:.2f} with {expiry}s expiry"
        self.notify(message, notification_type='info', play_sound='trade_placed')
    
    def notify_trade_result(self, trade_result):
        """
        Send notification for a trade result.
        
        Args:
            trade_result (dict): Result of the completed trade
        """
        direction = trade_result['direction'].upper()
        amount = trade_result['amount']
        result = trade_result['result'].upper()
        balance = trade_result['balance']
        
        if result == 'WIN':
            profit = amount * 0.85  # Assuming 85% payout
            message = f"Trade {result}: {direction} +{profit:.2f} | Balance: {balance:.2f}"
            self.notify(message, notification_type='success', play_sound='trade_win')
        else:
            message = f"Trade {result}: {direction} -{amount:.2f} | Balance: {balance:.2f}"
            self.notify(message, notification_type='error', play_sound='trade_loss')
    
    def get_notification_history(self, limit=10):
        """
        Get recent notification history.
        
        Args:
            limit (int): Maximum number of notifications to return
            
        Returns:
            list: Recent notifications
        """
        return self.notification_history[-limit:]
