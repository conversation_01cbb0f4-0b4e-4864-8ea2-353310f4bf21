iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAA7AAAAOwBeShxvQAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAUBSURBVGiB7ZlraBxVFMd/Z2Z3s9lsNpvdZDfZJE3SpGnSpGltbWyVtlgfVRGLKFoQP/hCEcUHKFjwgYIgKKggIqIo+EkUQa0ffKAo1kdbW9vaJjVt0zyaR5Nmk93NvmbGD0k2u8lOdrJJBT/kfLoz99xz/+fce+65Z4SU0sZNDOVGG3CjsUvgRhuwS+BmxS6BXQKAeqMNuBFQd9qBXQK7BHYJAMqNNuBGYJfALoFdArsEAOVGG3AjsFMEVLvdvqHxdwSBYDC4ofF3DIHe3t4NjZ8XAV3XCYfDhMNhdF3PZ9c5kU6nSSaTJJNJ0ul0PrvOiXwRSCaTnDt3jlQqRSqV4ty5cySTyXx1nxP5IhCLxTh79iyapqFpGmfPniUWi+Wr+5zIFwHDMIhEIhiGgWEYRCIRDMPIV/c5kS8Cfr+ftrY2VFVFVVXa2trw+/356j4n8kVAURRaWlrQNA1N02hpaUFR8rqMrIp8EQDwer309vbi8/nw+Xz09vbi9Xrz2UVW5HUZBfB4PPT09OS7m5zI9xLaUuwS2CWwS2CXAKDcaANuBHaKgLrTDuwS2CWwS2CXAKDcaANuBHaKgLrTDuwS2CWwS2CXAKDcaANuBHaKgLrTDuwS2CWwS2CXAKDcaANuBHaKgLrTDuwS2CWwS2CXAKDcaANuBHaKgLrTDuwS2CWwS2CXAKDcaANuBHaKgLrTDuwS2CWwS2CXAKDcaANuBHaKgLrTDuwS2CWwS2CXAKDcaANuBHaKgLrTDuwS2CWwS2CXAKDcaANuBHaKgLrTDuwS2CWwS2CXAKDcaANuBHaKgLrTDuwS2CWwS2CXAKDcaANuBHaKgLrTDuwS2CWwS2CXAKDcaANuBHaKgLrTDuwS2CWwS2CXAKDcaANuBHaKgLrTDuwS2CWwS2CXAKDcaANuBHaKgLrTDuwS2CWwS2CXAKDcaANuBHaKgLrTDuwS2CWwS2CXAKDcaANuBHaKgLrTDuwS2CWwS2CXAKDcaANuBHaKgLrTDuwS2CWwS2CXAKDcaANuBHaKgLrTDuwS2CWwS2CXAKDcaANuBHaKgLrTDuwS2CWwS2CXAKDcaANuBHaKgLrTDuwS2CWwS2CXAKDcaANuBHaKgLrTDuwS2CWwS2CXAKDcaANuBHaKgLrTDuwS2CWwS2CXAKDcaANuBHaKgLrTDuwS2CWwS2CXAKDcaANuBHaKgLrTDuwS2CWwS2CXAKDcaANuBHaKgLrTDuwS2CWwS2CXAKDcaANuBHaKgLrTDuwS2CWwS2CXAKDcaANuBHaKgLrTDuwS2CWwS2CXAKDcaANuBHaKgLrTDuwS2CWwS2CXAKDcaANuBHaKgLrTDuwS2CWwS2CXAKDcaANuBHaKgLrTDuwS2CWwS2CXAKDcaANuBHaKgLrTDuwS2CWwS2CXAKDcaANuBHaKgLrTDuwS2CWwS2CXAKDcaANuBHaKgLrTDuwS2CWwS2CXAKDcaANuBHaKgLrTDuwS2CWwS2CXAKDcaANuBHaKgLrTDuwS2CWwS2CXAKDcaANuBHaKgLrTDuwS2CWwS2CXAKDcaANuBHaKgLrTDuwS2CWwS2CXAKDcaANuBHaKgLrTDuwS2CWwS2CXAKDcaANuBHaKgLrTDuwS2CWwS2CXAKDcaANuBHaKgLrTDuwS2CWwS2CXAKDcaANuBP4HN0OP9eZ1YrAAAAAASUVORK5CYII=
