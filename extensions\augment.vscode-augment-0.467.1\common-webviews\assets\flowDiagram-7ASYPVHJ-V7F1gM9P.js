import{g as he,s as de}from"./chunk-5HRBRIJM-CITZpD49.js";import{d as V1,_ as i,o as Xt,p as pe,s as Ae,g as ge,b as fe,c as ye,q as be,r as ke,t as me,J as Ee,u as De,l as t1,v as dt,x as Ce,y as Te,j as C1,z as xe,A as Fe,e as Se,B as _e}from"./AugmentMessage-D6u8uU-A.js";import{c as Be}from"./channel-CyrOLmDz.js";import"./SpinnerAugment-BRymMBwV.js";import"./github-DCBOV_oD.js";import"./pen-to-square-DsQhBKje.js";import"./augment-logo-Cb6FLr8P.js";import"./TextTooltipAugment-VmEmcMVL.js";import"./BaseButton-rKFNr-KO.js";import"./IconButtonAugment-5yqT_m78.js";import"./Content-CZt_q_72.js";import"./globals-D0QH3NT1.js";import"./open-in-new-window-_CQmfLgB.js";import"./types-LfaCSdmF.js";import"./chat-types-NgqNgjwU.js";import"./test_service_pb-B6vKXZrG.js";import"./file-paths-BcSg4gks.js";import"./types-DvVg976p.js";import"./folder-Dee44ws-.js";import"./folder-opened-qXv2xhk3.js";import"./types-BSMhNRWH.js";import"./index-C57dba63.js";import"./CardAugment-BpvKVhgc.js";import"./TextAreaAugment-DJ6NCdxI.js";import"./diff-utils-UT8EbVpu.js";import"./toggleHighContrast-CwIv4U26.js";import"./preload-helper-Dv6uf1Os.js";import"./index-C59i2ECO.js";import"./keypress-DD1aQVr0.js";import"./await_block-B6zp5aG7.js";import"./ButtonAugment-B4rD0Iq1.js";import"./expand-DqlmSj23.js";import"./mcp-logo-CaRmgfKF.js";import"./ellipsis-bUrc34Ic.js";import"./IconFilePath-DzNJjZOv.js";import"./LanguageIcon-CLfcbvMk.js";import"./next-edit-types-904A5ehg.js";import"./magnifying-glass-Dmvedn_X.js";import"./MaterialIcon-D3827jJW.js";import"./Filespan-V8rQ2geT.js";import"./chevron-down-B8lwMNrs.js";import"./lodash-xZzKttBF.js";import"./terminal-BinWa3Yp.js";var Q,Z1,Qt=0,M1=V1(),f1=new Map,K=[],W1=new Map,p1=[],pt=new Map,At=new Map,ot=0,ct=!0,tt=[],et=i(t=>Se.sanitizeText(t,M1),"sanitizeText"),st=i(function(t){for(const n of f1.values())if(n.id===t)return n.domId;return t},"lookUpDomId"),ve=i(function(t,n,u,r,o,p,f={},d){var _,U;if(!t||t.trim().length===0)return;let A,l=f1.get(t);if(l===void 0&&(l={id:t,labelType:"text",domId:"flowchart-"+t+"-"+Qt,styles:[],classes:[]},f1.set(t,l)),Qt++,n!==void 0?(M1=V1(),A=et(n.text.trim()),l.labelType=n.type,A.startsWith('"')&&A.endsWith('"')&&(A=A.substring(1,A.length-1)),l.text=A):l.text===void 0&&(l.text=t),u!==void 0&&(l.type=u),r!=null&&r.forEach(function(B){l.styles.push(B)}),o!=null&&o.forEach(function(B){l.classes.push(B)}),p!==void 0&&(l.dir=p),l.props===void 0?l.props=f:f!==void 0&&Object.assign(l.props,f),d!==void 0){let B;B=d.includes(`
`)?d+`
`:`{
`+d+`
}`;const c=me(B,{schema:Ee});if(c.shape){if(c.shape!==c.shape.toLowerCase()||c.shape.includes("_"))throw new Error(`No such shape: ${c.shape}. Shape names should be lowercase.`);if(!De(c.shape))throw new Error(`No such shape: ${c.shape}.`);l.type=c==null?void 0:c.shape}c!=null&&c.label&&(l.text=c==null?void 0:c.label),c!=null&&c.icon&&(l.icon=c==null?void 0:c.icon,(_=c.label)!=null&&_.trim()||l.text!==t||(l.text="")),c!=null&&c.form&&(l.form=c==null?void 0:c.form),c!=null&&c.pos&&(l.pos=c==null?void 0:c.pos),c!=null&&c.img&&(l.img=c==null?void 0:c.img,(U=c.label)!=null&&U.trim()||l.text!==t||(l.text="")),c!=null&&c.constraint&&(l.constraint=c.constraint),c.w&&(l.assetWidth=Number(c.w)),c.h&&(l.assetHeight=Number(c.h))}},"addVertex"),we=i(function(t,n,u){const r={start:t,end:n,type:void 0,text:"",labelType:"text"};t1.info("abc78 Got edge...",r);const o=u.text;if(o!==void 0&&(r.text=et(o.text.trim()),r.text.startsWith('"')&&r.text.endsWith('"')&&(r.text=r.text.substring(1,r.text.length-1)),r.labelType=o.type),u!==void 0&&(r.type=u.type,r.stroke=u.stroke,r.length=u.length>10?10:u.length),!(K.length<(M1.maxEdges??500)))throw new Error(`Edge limit exceeded. ${K.length} edges found, but the limit is ${M1.maxEdges}.

Initialize mermaid with maxEdges set to a higher number to allow more edges.
You cannot set this config via configuration inside the diagram as it is a secure config.
You have to call mermaid.initialize.`);t1.info("Pushing edge..."),K.push(r)},"addSingleLink"),$e=i(function(t,n,u){t1.info("addLink",t,n,u);for(const r of t)for(const o of n)we(r,o,u)},"addLink"),Le=i(function(t,n){t.forEach(function(u){u==="default"?K.defaultInterpolate=n:K[u].interpolate=n})},"updateLinkInterpolate"),Ie=i(function(t,n){t.forEach(function(u){var r,o,p,f,d,A;if(typeof u=="number"&&u>=K.length)throw new Error(`The index ${u} for linkStyle is out of bounds. Valid indices for linkStyle are between 0 and ${K.length-1}. (Help: Ensure that the index is within the range of existing edges.)`);u==="default"?K.defaultStyle=n:(K[u].style=n,(((o=(r=K[u])==null?void 0:r.style)==null?void 0:o.length)??0)>0&&!((f=(p=K[u])==null?void 0:p.style)!=null&&f.some(l=>l==null?void 0:l.startsWith("fill")))&&((A=(d=K[u])==null?void 0:d.style)==null||A.push("fill:none")))})},"updateLink"),Re=i(function(t,n){t.split(",").forEach(function(u){let r=W1.get(u);r===void 0&&(r={id:u,styles:[],textStyles:[]},W1.set(u,r)),n!=null&&n.forEach(function(o){if(/color/.exec(o)){const p=o.replace("fill","bgFill");r.textStyles.push(p)}r.styles.push(o)})})},"addClass"),Ne=i(function(t){/.*</.exec(Q=t)&&(Q="RL"),/.*\^/.exec(Q)&&(Q="BT"),/.*>/.exec(Q)&&(Q="LR"),/.*v/.exec(Q)&&(Q="TB"),Q==="TD"&&(Q="TB")},"setDirection"),gt=i(function(t,n){for(const u of t.split(",")){const r=f1.get(u);r&&r.classes.push(n);const o=pt.get(u);o&&o.classes.push(n)}},"setClass"),Pe=i(function(t,n){if(n!==void 0){n=et(n);for(const u of t.split(","))At.set(Z1==="gen-1"?st(u):u,n)}},"setTooltip"),Oe=i(function(t,n,u){const r=st(t);if(V1().securityLevel!=="loose"||n===void 0)return;let o=[];if(typeof u=="string"){o=u.split(/,(?=(?:(?:[^"]*"){2})*[^"]*$)/);for(let f=0;f<o.length;f++){let d=o[f].trim();d.startsWith('"')&&d.endsWith('"')&&(d=d.substr(1,d.length-2)),o[f]=d}}o.length===0&&o.push(t);const p=f1.get(t);p&&(p.haveCallback=!0,tt.push(function(){const f=document.querySelector(`[id="${r}"]`);f!==null&&f.addEventListener("click",function(){dt.runFunc(n,...o)},!1)}))},"setClickFun"),Me=i(function(t,n,u){t.split(",").forEach(function(r){const o=f1.get(r);o!==void 0&&(o.link=dt.formatUrl(n,M1),o.linkTarget=u)}),gt(t,"clickable")},"setLink"),Jt=i(function(t){return At.get(t)},"getTooltip"),Ve=i(function(t,n,u){t.split(",").forEach(function(r){Oe(r,n,u)}),gt(t,"clickable")},"setClickEvent"),Ue=i(function(t){tt.forEach(function(n){n(t)})},"bindFunctions"),Zt=i(function(){return Q.trim()},"getDirection"),te=i(function(){return f1},"getVertices"),ee=i(function(){return K},"getEdges"),Ge=i(function(){return W1},"getClasses"),se=i(function(t){let n=C1(".mermaidTooltip");(n._groups||n)[0][0]===null&&(n=C1("body").append("div").attr("class","mermaidTooltip").style("opacity",0)),C1(t).select("svg").selectAll("g.node").on("mouseover",function(){const u=C1(this);if(u.attr("title")===null)return;const r=this==null?void 0:this.getBoundingClientRect();n.transition().duration(200).style("opacity",".9"),n.text(u.attr("title")).style("left",window.scrollX+r.left+(r.right-r.left)/2+"px").style("top",window.scrollY+r.bottom+"px"),n.html(n.html().replace(/&lt;br\/&gt;/g,"<br/>")),u.classed("hover",!0)}).on("mouseout",function(){n.transition().duration(500).style("opacity",0),C1(this).classed("hover",!1)})},"setupToolTips");tt.push(se);var We=i(function(t="gen-1"){f1=new Map,W1=new Map,K=[],tt=[se],p1=[],pt=new Map,ot=0,At=new Map,ct=!0,Z1=t,M1=V1(),Ce()},"clear"),Ke=i(t=>{Z1=t||"gen-2"},"setGen"),je=i(function(){return"fill:#ffa;stroke: #f66; stroke-width: 3px; stroke-dasharray: 5, 5;fill:#ffa;stroke: #666;"},"defaultStyle"),ze=i(function(t,n,u){let r=t.text.trim(),o=u.text;function p(l){const _={boolean:{},number:{},string:{}},U=[];let B;return{nodeList:l.filter(function(c){const D=typeof c;return c.stmt&&c.stmt==="dir"?(B=c.value,!1):c.trim()!==""&&(D in _?!_[D].hasOwnProperty(c)&&(_[D][c]=!0):!U.includes(c)&&U.push(c))}),dir:B}}t===u&&/\s/.exec(u.text)&&(r=void 0),i(p,"uniq");const{nodeList:f,dir:d}=p(n.flat());if(Z1==="gen-1")for(let l=0;l<f.length;l++)f[l]=st(f[l]);r=r??"subGraph"+ot,o=o||"",o=et(o),ot+=1;const A={id:r,nodes:f,title:o.trim(),classes:[],dir:d,labelType:u.type};return t1.info("Adding",A.id,A.nodes,A.dir),A.nodes=ie(A,p1).nodes,p1.push(A),pt.set(r,A),r},"addSubGraph"),Ye=i(function(t){for(const[n,u]of p1.entries())if(u.id===t)return n;return-1},"getPosForId"),lt=-1,ne=[],re=i(function(t,n){const u=p1[n].nodes;if((lt+=1)>2e3)return{result:!1,count:0};if(ne[lt]=n,p1[n].id===t)return{result:!0,count:0};let r=0,o=1;for(;r<u.length;){const p=Ye(u[r]);if(p>=0){const f=re(t,p);if(f.result)return{result:!0,count:o+f.count};o+=f.count}r+=1}return{result:!1,count:o}},"indexNodes2"),He=i(function(t){return ne[t]},"getDepthFirstPos"),qe=i(function(){lt=-1,p1.length>0&&re("none",p1.length-1)},"indexNodes"),ue=i(function(){return p1},"getSubGraphs"),Xe=i(()=>!!ct&&(ct=!1,!0),"firstGraph"),Qe=i(t=>{let n=t.trim(),u="arrow_open";switch(n[0]){case"<":u="arrow_point",n=n.slice(1);break;case"x":u="arrow_cross",n=n.slice(1);break;case"o":u="arrow_circle",n=n.slice(1)}let r="normal";return n.includes("=")&&(r="thick"),n.includes(".")&&(r="dotted"),{type:u,stroke:r}},"destructStartLink"),Je=i((t,n)=>{const u=n.length;let r=0;for(let o=0;o<u;++o)n[o]===t&&++r;return r},"countChar"),Ze=i(t=>{const n=t.trim();let u=n.slice(0,-1),r="arrow_open";switch(n.slice(-1)){case"x":r="arrow_cross",n.startsWith("x")&&(r="double_"+r,u=u.slice(1));break;case">":r="arrow_point",n.startsWith("<")&&(r="double_"+r,u=u.slice(1));break;case"o":r="arrow_circle",n.startsWith("o")&&(r="double_"+r,u=u.slice(1))}let o="normal",p=u.length-1;u.startsWith("=")&&(o="thick"),u.startsWith("~")&&(o="invisible");const f=Je(".",u);return f&&(o="dotted",p=f),{type:r,stroke:o,length:p}},"destructEndLink"),ts=i((t,n)=>{const u=Ze(t);let r;if(n){if(r=Qe(n),r.stroke!==u.stroke)return{type:"INVALID",stroke:"INVALID"};if(r.type==="arrow_open")r.type=u.type;else{if(r.type!==u.type)return{type:"INVALID",stroke:"INVALID"};r.type="double_"+r.type}return r.type==="double_arrow"&&(r.type="double_arrow_point"),r.length=u.length,r}return u},"destructLink"),ae=i((t,n)=>{for(const u of t)if(u.nodes.includes(n))return!0;return!1},"exists"),ie=i((t,n)=>{const u=[];return t.nodes.forEach((r,o)=>{ae(n,r)||u.push(t.nodes[o])}),{nodes:u}},"makeUniq"),es={firstGraph:Xe},ss=i(t=>{if(t.img)return"imageSquare";if(t.icon)return t.form==="circle"?"iconCircle":t.form==="square"?"iconSquare":t.form==="rounded"?"iconRounded":"icon";switch(t.type){case"square":case void 0:return"squareRect";case"round":return"roundedRect";case"ellipse":return"ellipse";default:return t.type}},"getTypeFromVertex"),ns=i((t,n)=>t.find(u=>u.id===n),"findNode"),rs=i(t=>{let n="none",u="arrow_point";switch(t){case"arrow_point":case"arrow_circle":case"arrow_cross":u=t;break;case"double_arrow_point":case"double_arrow_circle":case"double_arrow_cross":n=t.replace("double_",""),u=n}return{arrowTypeStart:n,arrowTypeEnd:u}},"destructEdgeType"),us=i((t,n,u,r,o,p)=>{var l;const f=u.get(t.id),d=r.get(t.id)??!1,A=ns(n,t.id);if(A)A.cssStyles=t.styles,A.cssCompiledStyles=J1(t.classes),A.cssClasses=t.classes.join(" ");else{const _={id:t.id,label:t.text,labelStyle:"",parentId:f,padding:((l=o.flowchart)==null?void 0:l.padding)||8,cssStyles:t.styles,cssCompiledStyles:J1(["default","node",...t.classes]),cssClasses:"default "+t.classes.join(" "),dir:t.dir,domId:t.domId,look:p,link:t.link,linkTarget:t.linkTarget,tooltip:Jt(t.id),icon:t.icon,pos:t.pos,img:t.img,assetWidth:t.assetWidth,assetHeight:t.assetHeight,constraint:t.constraint};d?n.push({..._,isGroup:!0,shape:"rect"}):n.push({..._,isGroup:!1,shape:ss(t)})}},"addNodeFromVertex");function J1(t){let n=[];for(const u of t){const r=W1.get(u);r!=null&&r.styles&&(n=[...n,...r.styles??[]].map(o=>o.trim())),r!=null&&r.textStyles&&(n=[...n,...r.textStyles??[]].map(o=>o.trim()))}return n}i(J1,"getCompiledStyles");var as=i(()=>{const t=V1(),n=[],u=[],r=ue(),o=new Map,p=new Map;for(let d=r.length-1;d>=0;d--){const A=r[d];A.nodes.length>0&&p.set(A.id,!0);for(const l of A.nodes)o.set(l,A.id)}for(let d=r.length-1;d>=0;d--){const A=r[d];n.push({id:A.id,label:A.title,labelStyle:"",parentId:o.get(A.id),padding:8,cssCompiledStyles:J1(A.classes),cssClasses:A.classes.join(" "),shape:"rect",dir:A.dir,isGroup:!0,look:t.look})}te().forEach(d=>{us(d,n,o,p,t,t.look||"classic")});const f=ee();return f.forEach((d,A)=>{const{arrowTypeStart:l,arrowTypeEnd:_}=rs(d.type),U=[...f.defaultStyle??[]];d.style&&U.push(...d.style);const B={id:Te(d.start,d.end,{counter:A,prefix:"L"}),start:d.start,end:d.end,type:d.type??"normal",label:d.text,labelpos:"c",thickness:d.stroke,minlen:d.length,classes:(d==null?void 0:d.stroke)==="invisible"?"":"edge-thickness-normal edge-pattern-solid flowchart-link",arrowTypeStart:(d==null?void 0:d.stroke)==="invisible"?"none":l,arrowTypeEnd:(d==null?void 0:d.stroke)==="invisible"?"none":_,arrowheadStyle:"fill: #333",labelStyle:U,style:U,pattern:d.stroke,look:t.look};u.push(B)}),{nodes:n,edges:u,other:{},config:t}},"getData"),it={defaultConfig:i(()=>pe.flowchart,"defaultConfig"),setAccTitle:Ae,getAccTitle:ge,getAccDescription:fe,getData:as,setAccDescription:ye,addVertex:ve,lookUpDomId:st,addLink:$e,updateLinkInterpolate:Le,updateLink:Ie,addClass:Re,setDirection:Ne,setClass:gt,setTooltip:Pe,getTooltip:Jt,setClickEvent:Ve,setLink:Me,bindFunctions:Ue,getDirection:Zt,getVertices:te,getEdges:ee,getClasses:Ge,clear:We,setGen:Ke,defaultStyle:je,addSubGraph:ze,getDepthFirstPos:He,indexNodes:qe,getSubGraphs:ue,destructLink:ts,lex:es,exists:ae,makeUniq:ie,setDiagramTitle:be,getDiagramTitle:ke},is={getClasses:i(function(t,n){return n.db.getClasses()},"getClasses"),draw:i(async function(t,n,u,r){var c;t1.info("REF0:"),t1.info("Drawing state diagram (v2)",n);const{securityLevel:o,flowchart:p,layout:f}=V1();let d;o==="sandbox"&&(d=C1("#i"+n));const A=o==="sandbox"?d.nodes()[0].contentDocument:document;t1.debug("Before getData: ");const l=r.db.getData();t1.debug("Data: ",l);const _=he(n,o),U=Zt();l.type=r.type,l.layoutAlgorithm=xe(f),l.layoutAlgorithm==="dagre"&&f==="elk"&&t1.warn("flowchart-elk was moved to an external package in Mermaid v11. Please refer [release notes](https://github.com/mermaid-js/mermaid/releases/tag/v11.0.0) for more details. This diagram will be rendered using `dagre` layout as a fallback."),l.direction=U,l.nodeSpacing=(p==null?void 0:p.nodeSpacing)||50,l.rankSpacing=(p==null?void 0:p.rankSpacing)||50,l.markers=["point","circle","cross"],l.diagramId=n,t1.debug("REF1:",l),await Fe(l,_);const B=((c=l.config.flowchart)==null?void 0:c.diagramPadding)??8;dt.insertTitle(_,"flowchartTitleText",(p==null?void 0:p.titleTopMargin)||0,r.db.getDiagramTitle()),de(_,B,"flowchart",(p==null?void 0:p.useMaxWidth)||!1);for(const D of l.nodes){const F=C1(`#${n} [id="${D.id}"]`);if(!F||!D.link)continue;const J=A.createElementNS("http://www.w3.org/2000/svg","a");J.setAttributeNS("http://www.w3.org/2000/svg","class",D.cssClasses),J.setAttributeNS("http://www.w3.org/2000/svg","rel","noopener"),o==="sandbox"?J.setAttributeNS("http://www.w3.org/2000/svg","target","_top"):D.linkTarget&&J.setAttributeNS("http://www.w3.org/2000/svg","target",D.linkTarget);const y1=F.insert(function(){return J},":first-child"),b1=F.select(".label-container");b1&&y1.append(function(){return b1.node()});const k1=F.select(".label");k1&&y1.append(function(){return k1.node()})}},"draw")},ht=function(){var t=i(function(h,E,g,a){for(g=g||{},a=h.length;a--;g[h[a]]=E);return g},"o"),n=[1,4],u=[1,3],r=[1,5],o=[1,8,9,10,11,27,34,36,38,44,60,83,84,85,86,87,88,101,104,105,108,110,113,114,115,120,121,122,123],p=[2,2],f=[1,13],d=[1,14],A=[1,15],l=[1,16],_=[1,23],U=[1,25],B=[1,26],c=[1,27],D=[1,49],F=[1,48],J=[1,29],y1=[1,30],b1=[1,31],k1=[1,32],K1=[1,33],v=[1,44],w=[1,46],$=[1,42],L=[1,47],I=[1,43],R=[1,50],N=[1,45],P=[1,51],O=[1,52],j1=[1,34],z1=[1,35],Y1=[1,36],H1=[1,37],A1=[1,57],C=[1,8,9,10,11,27,32,34,36,38,44,60,83,84,85,86,87,88,101,104,105,108,110,113,114,115,120,121,122,123],e1=[1,61],s1=[1,60],n1=[1,62],T1=[8,9,11,75,77],ft=[1,77],x1=[1,90],F1=[1,95],S1=[1,94],_1=[1,91],B1=[1,87],v1=[1,93],w1=[1,89],$1=[1,96],L1=[1,92],I1=[1,97],R1=[1,88],m1=[8,9,10,11,40,75,77],V=[8,9,10,11,40,46,75,77],j=[8,9,10,11,29,40,44,46,48,50,52,54,56,58,60,63,65,67,68,70,75,77,88,101,104,105,108,110,113,114,115],yt=[8,9,11,44,60,75,77,88,101,104,105,108,110,113,114,115],U1=[44,60,88,101,104,105,108,110,113,114,115],bt=[1,123],kt=[1,122],mt=[1,130],Et=[1,144],Dt=[1,145],Ct=[1,146],Tt=[1,147],xt=[1,132],Ft=[1,134],St=[1,138],_t=[1,139],Bt=[1,140],vt=[1,141],wt=[1,142],$t=[1,143],Lt=[1,148],It=[1,149],Rt=[1,128],Nt=[1,129],Pt=[1,136],Ot=[1,131],Mt=[1,135],Vt=[1,133],nt=[8,9,10,11,27,32,34,36,38,44,60,83,84,85,86,87,88,101,104,105,108,110,113,114,115,120,121,122,123],Ut=[1,151],Gt=[1,153],S=[8,9,11],z=[8,9,10,11,14,44,60,88,104,105,108,110,113,114,115],y=[1,173],G=[1,169],W=[1,170],b=[1,174],k=[1,171],m=[1,172],N1=[77,115,118],T=[8,9,10,11,12,14,27,29,32,44,60,75,83,84,85,86,87,88,89,104,108,110,113,114,115],Wt=[10,105],g1=[31,49,51,53,55,57,62,64,66,67,69,71,115,116,117],r1=[1,242],u1=[1,240],a1=[1,244],i1=[1,238],o1=[1,239],c1=[1,241],l1=[1,243],h1=[1,245],P1=[1,263],Kt=[8,9,11,105],Z=[8,9,10,11,60,83,104,105,108,109,110,111],rt={trace:i(function(){},"trace"),yy:{},symbols_:{error:2,start:3,graphConfig:4,document:5,line:6,statement:7,SEMI:8,NEWLINE:9,SPACE:10,EOF:11,GRAPH:12,NODIR:13,DIR:14,FirstStmtSeparator:15,ending:16,endToken:17,spaceList:18,spaceListNewline:19,vertexStatement:20,separator:21,styleStatement:22,linkStyleStatement:23,classDefStatement:24,classStatement:25,clickStatement:26,subgraph:27,textNoTags:28,SQS:29,text:30,SQE:31,end:32,direction:33,acc_title:34,acc_title_value:35,acc_descr:36,acc_descr_value:37,acc_descr_multiline_value:38,shapeData:39,SHAPE_DATA:40,link:41,node:42,styledVertex:43,AMP:44,vertex:45,STYLE_SEPARATOR:46,idString:47,DOUBLECIRCLESTART:48,DOUBLECIRCLEEND:49,PS:50,PE:51,"(-":52,"-)":53,STADIUMSTART:54,STADIUMEND:55,SUBROUTINESTART:56,SUBROUTINEEND:57,VERTEX_WITH_PROPS_START:58,"NODE_STRING[field]":59,COLON:60,"NODE_STRING[value]":61,PIPE:62,CYLINDERSTART:63,CYLINDEREND:64,DIAMOND_START:65,DIAMOND_STOP:66,TAGEND:67,TRAPSTART:68,TRAPEND:69,INVTRAPSTART:70,INVTRAPEND:71,linkStatement:72,arrowText:73,TESTSTR:74,START_LINK:75,edgeText:76,LINK:77,edgeTextToken:78,STR:79,MD_STR:80,textToken:81,keywords:82,STYLE:83,LINKSTYLE:84,CLASSDEF:85,CLASS:86,CLICK:87,DOWN:88,UP:89,textNoTagsToken:90,stylesOpt:91,"idString[vertex]":92,"idString[class]":93,CALLBACKNAME:94,CALLBACKARGS:95,HREF:96,LINK_TARGET:97,"STR[link]":98,"STR[tooltip]":99,alphaNum:100,DEFAULT:101,numList:102,INTERPOLATE:103,NUM:104,COMMA:105,style:106,styleComponent:107,NODE_STRING:108,UNIT:109,BRKT:110,PCT:111,idStringToken:112,MINUS:113,MULT:114,UNICODE_TEXT:115,TEXT:116,TAGSTART:117,EDGE_TEXT:118,alphaNumToken:119,direction_tb:120,direction_bt:121,direction_rl:122,direction_lr:123,$accept:0,$end:1},terminals_:{2:"error",8:"SEMI",9:"NEWLINE",10:"SPACE",11:"EOF",12:"GRAPH",13:"NODIR",14:"DIR",27:"subgraph",29:"SQS",31:"SQE",32:"end",34:"acc_title",35:"acc_title_value",36:"acc_descr",37:"acc_descr_value",38:"acc_descr_multiline_value",40:"SHAPE_DATA",44:"AMP",46:"STYLE_SEPARATOR",48:"DOUBLECIRCLESTART",49:"DOUBLECIRCLEEND",50:"PS",51:"PE",52:"(-",53:"-)",54:"STADIUMSTART",55:"STADIUMEND",56:"SUBROUTINESTART",57:"SUBROUTINEEND",58:"VERTEX_WITH_PROPS_START",59:"NODE_STRING[field]",60:"COLON",61:"NODE_STRING[value]",62:"PIPE",63:"CYLINDERSTART",64:"CYLINDEREND",65:"DIAMOND_START",66:"DIAMOND_STOP",67:"TAGEND",68:"TRAPSTART",69:"TRAPEND",70:"INVTRAPSTART",71:"INVTRAPEND",74:"TESTSTR",75:"START_LINK",77:"LINK",79:"STR",80:"MD_STR",83:"STYLE",84:"LINKSTYLE",85:"CLASSDEF",86:"CLASS",87:"CLICK",88:"DOWN",89:"UP",92:"idString[vertex]",93:"idString[class]",94:"CALLBACKNAME",95:"CALLBACKARGS",96:"HREF",97:"LINK_TARGET",98:"STR[link]",99:"STR[tooltip]",101:"DEFAULT",103:"INTERPOLATE",104:"NUM",105:"COMMA",108:"NODE_STRING",109:"UNIT",110:"BRKT",111:"PCT",113:"MINUS",114:"MULT",115:"UNICODE_TEXT",116:"TEXT",117:"TAGSTART",118:"EDGE_TEXT",120:"direction_tb",121:"direction_bt",122:"direction_rl",123:"direction_lr"},productions_:[0,[3,2],[5,0],[5,2],[6,1],[6,1],[6,1],[6,1],[6,1],[4,2],[4,2],[4,2],[4,3],[16,2],[16,1],[17,1],[17,1],[17,1],[15,1],[15,1],[15,2],[19,2],[19,2],[19,1],[19,1],[18,2],[18,1],[7,2],[7,2],[7,2],[7,2],[7,2],[7,2],[7,9],[7,6],[7,4],[7,1],[7,2],[7,2],[7,1],[21,1],[21,1],[21,1],[39,2],[39,1],[20,4],[20,3],[20,4],[20,2],[20,2],[20,1],[42,1],[42,6],[42,5],[43,1],[43,3],[45,4],[45,4],[45,6],[45,4],[45,4],[45,4],[45,8],[45,4],[45,4],[45,4],[45,6],[45,4],[45,4],[45,4],[45,4],[45,4],[45,1],[41,2],[41,3],[41,3],[41,1],[41,3],[76,1],[76,2],[76,1],[76,1],[72,1],[73,3],[30,1],[30,2],[30,1],[30,1],[82,1],[82,1],[82,1],[82,1],[82,1],[82,1],[82,1],[82,1],[82,1],[82,1],[82,1],[28,1],[28,2],[28,1],[28,1],[24,5],[25,5],[26,2],[26,4],[26,3],[26,5],[26,3],[26,5],[26,5],[26,7],[26,2],[26,4],[26,2],[26,4],[26,4],[26,6],[22,5],[23,5],[23,5],[23,9],[23,9],[23,7],[23,7],[102,1],[102,3],[91,1],[91,3],[106,1],[106,2],[107,1],[107,1],[107,1],[107,1],[107,1],[107,1],[107,1],[107,1],[112,1],[112,1],[112,1],[112,1],[112,1],[112,1],[112,1],[112,1],[112,1],[112,1],[112,1],[81,1],[81,1],[81,1],[81,1],[90,1],[90,1],[90,1],[90,1],[90,1],[90,1],[90,1],[90,1],[90,1],[90,1],[90,1],[78,1],[78,1],[119,1],[119,1],[119,1],[119,1],[119,1],[119,1],[119,1],[119,1],[119,1],[119,1],[119,1],[47,1],[47,2],[100,1],[100,2],[33,1],[33,1],[33,1],[33,1]],performAction:i(function(h,E,g,a,x,e,G1){var s=e.length-1;switch(x){case 2:case 28:case 29:case 30:case 31:case 32:this.$=[];break;case 3:(!Array.isArray(e[s])||e[s].length>0)&&e[s-1].push(e[s]),this.$=e[s-1];break;case 4:case 181:case 44:case 54:case 76:case 179:this.$=e[s];break;case 11:a.setDirection("TB"),this.$="TB";break;case 12:a.setDirection(e[s-1]),this.$=e[s-1];break;case 27:this.$=e[s-1].nodes;break;case 33:this.$=a.addSubGraph(e[s-6],e[s-1],e[s-4]);break;case 34:this.$=a.addSubGraph(e[s-3],e[s-1],e[s-3]);break;case 35:this.$=a.addSubGraph(void 0,e[s-1],void 0);break;case 37:this.$=e[s].trim(),a.setAccTitle(this.$);break;case 38:case 39:this.$=e[s].trim(),a.setAccDescription(this.$);break;case 43:case 131:this.$=e[s-1]+e[s];break;case 45:a.addVertex(e[s-1][0],void 0,void 0,void 0,void 0,void 0,void 0,e[s]),a.addLink(e[s-3].stmt,e[s-1],e[s-2]),this.$={stmt:e[s-1],nodes:e[s-1].concat(e[s-3].nodes)};break;case 46:a.addLink(e[s-2].stmt,e[s],e[s-1]),this.$={stmt:e[s],nodes:e[s].concat(e[s-2].nodes)};break;case 47:a.addLink(e[s-3].stmt,e[s-1],e[s-2]),this.$={stmt:e[s-1],nodes:e[s-1].concat(e[s-3].nodes)};break;case 48:this.$={stmt:e[s-1],nodes:e[s-1]};break;case 49:a.addVertex(e[s-1][0],void 0,void 0,void 0,void 0,void 0,void 0,e[s]),this.$={stmt:e[s-1],nodes:e[s-1],shapeData:e[s]};break;case 50:this.$={stmt:e[s],nodes:e[s]};break;case 51:case 126:case 128:this.$=[e[s]];break;case 52:a.addVertex(e[s-5][0],void 0,void 0,void 0,void 0,void 0,void 0,e[s-4]),this.$=e[s-5].concat(e[s]);break;case 53:this.$=e[s-4].concat(e[s]);break;case 55:this.$=e[s-2],a.setClass(e[s-2],e[s]);break;case 56:this.$=e[s-3],a.addVertex(e[s-3],e[s-1],"square");break;case 57:this.$=e[s-3],a.addVertex(e[s-3],e[s-1],"doublecircle");break;case 58:this.$=e[s-5],a.addVertex(e[s-5],e[s-2],"circle");break;case 59:this.$=e[s-3],a.addVertex(e[s-3],e[s-1],"ellipse");break;case 60:this.$=e[s-3],a.addVertex(e[s-3],e[s-1],"stadium");break;case 61:this.$=e[s-3],a.addVertex(e[s-3],e[s-1],"subroutine");break;case 62:this.$=e[s-7],a.addVertex(e[s-7],e[s-1],"rect",void 0,void 0,void 0,Object.fromEntries([[e[s-5],e[s-3]]]));break;case 63:this.$=e[s-3],a.addVertex(e[s-3],e[s-1],"cylinder");break;case 64:this.$=e[s-3],a.addVertex(e[s-3],e[s-1],"round");break;case 65:this.$=e[s-3],a.addVertex(e[s-3],e[s-1],"diamond");break;case 66:this.$=e[s-5],a.addVertex(e[s-5],e[s-2],"hexagon");break;case 67:this.$=e[s-3],a.addVertex(e[s-3],e[s-1],"odd");break;case 68:this.$=e[s-3],a.addVertex(e[s-3],e[s-1],"trapezoid");break;case 69:this.$=e[s-3],a.addVertex(e[s-3],e[s-1],"inv_trapezoid");break;case 70:this.$=e[s-3],a.addVertex(e[s-3],e[s-1],"lean_right");break;case 71:this.$=e[s-3],a.addVertex(e[s-3],e[s-1],"lean_left");break;case 72:this.$=e[s],a.addVertex(e[s]);break;case 73:e[s-1].text=e[s],this.$=e[s-1];break;case 74:case 75:e[s-2].text=e[s-1],this.$=e[s-2];break;case 77:var H=a.destructLink(e[s],e[s-2]);this.$={type:H.type,stroke:H.stroke,length:H.length,text:e[s-1]};break;case 78:case 84:case 99:case 101:this.$={text:e[s],type:"text"};break;case 79:case 85:case 100:this.$={text:e[s-1].text+""+e[s],type:e[s-1].type};break;case 80:case 86:this.$={text:e[s],type:"string"};break;case 81:case 87:case 102:this.$={text:e[s],type:"markdown"};break;case 82:H=a.destructLink(e[s]),this.$={type:H.type,stroke:H.stroke,length:H.length};break;case 83:this.$=e[s-1];break;case 103:this.$=e[s-4],a.addClass(e[s-2],e[s]);break;case 104:this.$=e[s-4],a.setClass(e[s-2],e[s]);break;case 105:case 113:this.$=e[s-1],a.setClickEvent(e[s-1],e[s]);break;case 106:case 114:this.$=e[s-3],a.setClickEvent(e[s-3],e[s-2]),a.setTooltip(e[s-3],e[s]);break;case 107:this.$=e[s-2],a.setClickEvent(e[s-2],e[s-1],e[s]);break;case 108:this.$=e[s-4],a.setClickEvent(e[s-4],e[s-3],e[s-2]),a.setTooltip(e[s-4],e[s]);break;case 109:this.$=e[s-2],a.setLink(e[s-2],e[s]);break;case 110:this.$=e[s-4],a.setLink(e[s-4],e[s-2]),a.setTooltip(e[s-4],e[s]);break;case 111:this.$=e[s-4],a.setLink(e[s-4],e[s-2],e[s]);break;case 112:this.$=e[s-6],a.setLink(e[s-6],e[s-4],e[s]),a.setTooltip(e[s-6],e[s-2]);break;case 115:this.$=e[s-1],a.setLink(e[s-1],e[s]);break;case 116:this.$=e[s-3],a.setLink(e[s-3],e[s-2]),a.setTooltip(e[s-3],e[s]);break;case 117:this.$=e[s-3],a.setLink(e[s-3],e[s-2],e[s]);break;case 118:this.$=e[s-5],a.setLink(e[s-5],e[s-4],e[s]),a.setTooltip(e[s-5],e[s-2]);break;case 119:this.$=e[s-4],a.addVertex(e[s-2],void 0,void 0,e[s]);break;case 120:this.$=e[s-4],a.updateLink([e[s-2]],e[s]);break;case 121:this.$=e[s-4],a.updateLink(e[s-2],e[s]);break;case 122:this.$=e[s-8],a.updateLinkInterpolate([e[s-6]],e[s-2]),a.updateLink([e[s-6]],e[s]);break;case 123:this.$=e[s-8],a.updateLinkInterpolate(e[s-6],e[s-2]),a.updateLink(e[s-6],e[s]);break;case 124:this.$=e[s-6],a.updateLinkInterpolate([e[s-4]],e[s]);break;case 125:this.$=e[s-6],a.updateLinkInterpolate(e[s-4],e[s]);break;case 127:case 129:e[s-2].push(e[s]),this.$=e[s-2];break;case 180:case 182:this.$=e[s-1]+""+e[s];break;case 183:this.$={stmt:"dir",value:"TB"};break;case 184:this.$={stmt:"dir",value:"BT"};break;case 185:this.$={stmt:"dir",value:"RL"};break;case 186:this.$={stmt:"dir",value:"LR"}}},"anonymous"),table:[{3:1,4:2,9:n,10:u,12:r},{1:[3]},t(o,p,{5:6}),{4:7,9:n,10:u,12:r},{4:8,9:n,10:u,12:r},{13:[1,9],14:[1,10]},{1:[2,1],6:11,7:12,8:f,9:d,10:A,11:l,20:17,22:18,23:19,24:20,25:21,26:22,27:_,33:24,34:U,36:B,38:c,42:28,43:38,44:D,45:39,47:40,60:F,83:J,84:y1,85:b1,86:k1,87:K1,88:v,101:w,104:$,105:L,108:I,110:R,112:41,113:N,114:P,115:O,120:j1,121:z1,122:Y1,123:H1},t(o,[2,9]),t(o,[2,10]),t(o,[2,11]),{8:[1,54],9:[1,55],10:A1,15:53,18:56},t(C,[2,3]),t(C,[2,4]),t(C,[2,5]),t(C,[2,6]),t(C,[2,7]),t(C,[2,8]),{8:e1,9:s1,11:n1,21:58,41:59,72:63,75:[1,64],77:[1,65]},{8:e1,9:s1,11:n1,21:66},{8:e1,9:s1,11:n1,21:67},{8:e1,9:s1,11:n1,21:68},{8:e1,9:s1,11:n1,21:69},{8:e1,9:s1,11:n1,21:70},{8:e1,9:s1,10:[1,71],11:n1,21:72},t(C,[2,36]),{35:[1,73]},{37:[1,74]},t(C,[2,39]),t(T1,[2,50],{18:75,39:76,10:A1,40:ft}),{10:[1,78]},{10:[1,79]},{10:[1,80]},{10:[1,81]},{14:x1,44:F1,60:S1,79:[1,85],88:_1,94:[1,82],96:[1,83],100:84,104:B1,105:v1,108:w1,110:$1,113:L1,114:I1,115:R1,119:86},t(C,[2,183]),t(C,[2,184]),t(C,[2,185]),t(C,[2,186]),t(m1,[2,51]),t(m1,[2,54],{46:[1,98]}),t(V,[2,72],{112:111,29:[1,99],44:D,48:[1,100],50:[1,101],52:[1,102],54:[1,103],56:[1,104],58:[1,105],60:F,63:[1,106],65:[1,107],67:[1,108],68:[1,109],70:[1,110],88:v,101:w,104:$,105:L,108:I,110:R,113:N,114:P,115:O}),t(j,[2,179]),t(j,[2,140]),t(j,[2,141]),t(j,[2,142]),t(j,[2,143]),t(j,[2,144]),t(j,[2,145]),t(j,[2,146]),t(j,[2,147]),t(j,[2,148]),t(j,[2,149]),t(j,[2,150]),t(o,[2,12]),t(o,[2,18]),t(o,[2,19]),{9:[1,112]},t(yt,[2,26],{18:113,10:A1}),t(C,[2,27]),{42:114,43:38,44:D,45:39,47:40,60:F,88:v,101:w,104:$,105:L,108:I,110:R,112:41,113:N,114:P,115:O},t(C,[2,40]),t(C,[2,41]),t(C,[2,42]),t(U1,[2,76],{73:115,62:[1,117],74:[1,116]}),{76:118,78:119,79:[1,120],80:[1,121],115:bt,118:kt},t([44,60,62,74,88,101,104,105,108,110,113,114,115],[2,82]),t(C,[2,28]),t(C,[2,29]),t(C,[2,30]),t(C,[2,31]),t(C,[2,32]),{10:mt,12:Et,14:Dt,27:Ct,28:124,32:Tt,44:xt,60:Ft,75:St,79:[1,126],80:[1,127],82:137,83:_t,84:Bt,85:vt,86:wt,87:$t,88:Lt,89:It,90:125,104:Rt,108:Nt,110:Pt,113:Ot,114:Mt,115:Vt},t(nt,p,{5:150}),t(C,[2,37]),t(C,[2,38]),t(T1,[2,48],{44:Ut}),t(T1,[2,49],{18:152,10:A1,40:Gt}),t(m1,[2,44]),{44:D,47:154,60:F,88:v,101:w,104:$,105:L,108:I,110:R,112:41,113:N,114:P,115:O},{101:[1,155],102:156,104:[1,157]},{44:D,47:158,60:F,88:v,101:w,104:$,105:L,108:I,110:R,112:41,113:N,114:P,115:O},{44:D,47:159,60:F,88:v,101:w,104:$,105:L,108:I,110:R,112:41,113:N,114:P,115:O},t(S,[2,105],{10:[1,160],95:[1,161]}),{79:[1,162]},t(S,[2,113],{119:164,10:[1,163],14:x1,44:F1,60:S1,88:_1,104:B1,105:v1,108:w1,110:$1,113:L1,114:I1,115:R1}),t(S,[2,115],{10:[1,165]}),t(z,[2,181]),t(z,[2,168]),t(z,[2,169]),t(z,[2,170]),t(z,[2,171]),t(z,[2,172]),t(z,[2,173]),t(z,[2,174]),t(z,[2,175]),t(z,[2,176]),t(z,[2,177]),t(z,[2,178]),{44:D,47:166,60:F,88:v,101:w,104:$,105:L,108:I,110:R,112:41,113:N,114:P,115:O},{30:167,67:y,79:G,80:W,81:168,115:b,116:k,117:m},{30:175,67:y,79:G,80:W,81:168,115:b,116:k,117:m},{30:177,50:[1,176],67:y,79:G,80:W,81:168,115:b,116:k,117:m},{30:178,67:y,79:G,80:W,81:168,115:b,116:k,117:m},{30:179,67:y,79:G,80:W,81:168,115:b,116:k,117:m},{30:180,67:y,79:G,80:W,81:168,115:b,116:k,117:m},{108:[1,181]},{30:182,67:y,79:G,80:W,81:168,115:b,116:k,117:m},{30:183,65:[1,184],67:y,79:G,80:W,81:168,115:b,116:k,117:m},{30:185,67:y,79:G,80:W,81:168,115:b,116:k,117:m},{30:186,67:y,79:G,80:W,81:168,115:b,116:k,117:m},{30:187,67:y,79:G,80:W,81:168,115:b,116:k,117:m},t(j,[2,180]),t(o,[2,20]),t(yt,[2,25]),t(T1,[2,46],{39:188,18:189,10:A1,40:ft}),t(U1,[2,73],{10:[1,190]}),{10:[1,191]},{30:192,67:y,79:G,80:W,81:168,115:b,116:k,117:m},{77:[1,193],78:194,115:bt,118:kt},t(N1,[2,78]),t(N1,[2,80]),t(N1,[2,81]),t(N1,[2,166]),t(N1,[2,167]),{8:e1,9:s1,10:mt,11:n1,12:Et,14:Dt,21:196,27:Ct,29:[1,195],32:Tt,44:xt,60:Ft,75:St,82:137,83:_t,84:Bt,85:vt,86:wt,87:$t,88:Lt,89:It,90:197,104:Rt,108:Nt,110:Pt,113:Ot,114:Mt,115:Vt},t(T,[2,99]),t(T,[2,101]),t(T,[2,102]),t(T,[2,155]),t(T,[2,156]),t(T,[2,157]),t(T,[2,158]),t(T,[2,159]),t(T,[2,160]),t(T,[2,161]),t(T,[2,162]),t(T,[2,163]),t(T,[2,164]),t(T,[2,165]),t(T,[2,88]),t(T,[2,89]),t(T,[2,90]),t(T,[2,91]),t(T,[2,92]),t(T,[2,93]),t(T,[2,94]),t(T,[2,95]),t(T,[2,96]),t(T,[2,97]),t(T,[2,98]),{6:11,7:12,8:f,9:d,10:A,11:l,20:17,22:18,23:19,24:20,25:21,26:22,27:_,32:[1,198],33:24,34:U,36:B,38:c,42:28,43:38,44:D,45:39,47:40,60:F,83:J,84:y1,85:b1,86:k1,87:K1,88:v,101:w,104:$,105:L,108:I,110:R,112:41,113:N,114:P,115:O,120:j1,121:z1,122:Y1,123:H1},{10:A1,18:199},{44:[1,200]},t(m1,[2,43]),{10:[1,201],44:D,60:F,88:v,101:w,104:$,105:L,108:I,110:R,112:111,113:N,114:P,115:O},{10:[1,202]},{10:[1,203],105:[1,204]},t(Wt,[2,126]),{10:[1,205],44:D,60:F,88:v,101:w,104:$,105:L,108:I,110:R,112:111,113:N,114:P,115:O},{10:[1,206],44:D,60:F,88:v,101:w,104:$,105:L,108:I,110:R,112:111,113:N,114:P,115:O},{79:[1,207]},t(S,[2,107],{10:[1,208]}),t(S,[2,109],{10:[1,209]}),{79:[1,210]},t(z,[2,182]),{79:[1,211],97:[1,212]},t(m1,[2,55],{112:111,44:D,60:F,88:v,101:w,104:$,105:L,108:I,110:R,113:N,114:P,115:O}),{31:[1,213],67:y,81:214,115:b,116:k,117:m},t(g1,[2,84]),t(g1,[2,86]),t(g1,[2,87]),t(g1,[2,151]),t(g1,[2,152]),t(g1,[2,153]),t(g1,[2,154]),{49:[1,215],67:y,81:214,115:b,116:k,117:m},{30:216,67:y,79:G,80:W,81:168,115:b,116:k,117:m},{51:[1,217],67:y,81:214,115:b,116:k,117:m},{53:[1,218],67:y,81:214,115:b,116:k,117:m},{55:[1,219],67:y,81:214,115:b,116:k,117:m},{57:[1,220],67:y,81:214,115:b,116:k,117:m},{60:[1,221]},{64:[1,222],67:y,81:214,115:b,116:k,117:m},{66:[1,223],67:y,81:214,115:b,116:k,117:m},{30:224,67:y,79:G,80:W,81:168,115:b,116:k,117:m},{31:[1,225],67:y,81:214,115:b,116:k,117:m},{67:y,69:[1,226],71:[1,227],81:214,115:b,116:k,117:m},{67:y,69:[1,229],71:[1,228],81:214,115:b,116:k,117:m},t(T1,[2,45],{18:152,10:A1,40:Gt}),t(T1,[2,47],{44:Ut}),t(U1,[2,75]),t(U1,[2,74]),{62:[1,230],67:y,81:214,115:b,116:k,117:m},t(U1,[2,77]),t(N1,[2,79]),{30:231,67:y,79:G,80:W,81:168,115:b,116:k,117:m},t(nt,p,{5:232}),t(T,[2,100]),t(C,[2,35]),{43:233,44:D,45:39,47:40,60:F,88:v,101:w,104:$,105:L,108:I,110:R,112:41,113:N,114:P,115:O},{10:A1,18:234},{10:r1,60:u1,83:a1,91:235,104:i1,106:236,107:237,108:o1,109:c1,110:l1,111:h1},{10:r1,60:u1,83:a1,91:246,103:[1,247],104:i1,106:236,107:237,108:o1,109:c1,110:l1,111:h1},{10:r1,60:u1,83:a1,91:248,103:[1,249],104:i1,106:236,107:237,108:o1,109:c1,110:l1,111:h1},{104:[1,250]},{10:r1,60:u1,83:a1,91:251,104:i1,106:236,107:237,108:o1,109:c1,110:l1,111:h1},{44:D,47:252,60:F,88:v,101:w,104:$,105:L,108:I,110:R,112:41,113:N,114:P,115:O},t(S,[2,106]),{79:[1,253]},{79:[1,254],97:[1,255]},t(S,[2,114]),t(S,[2,116],{10:[1,256]}),t(S,[2,117]),t(V,[2,56]),t(g1,[2,85]),t(V,[2,57]),{51:[1,257],67:y,81:214,115:b,116:k,117:m},t(V,[2,64]),t(V,[2,59]),t(V,[2,60]),t(V,[2,61]),{108:[1,258]},t(V,[2,63]),t(V,[2,65]),{66:[1,259],67:y,81:214,115:b,116:k,117:m},t(V,[2,67]),t(V,[2,68]),t(V,[2,70]),t(V,[2,69]),t(V,[2,71]),t([10,44,60,88,101,104,105,108,110,113,114,115],[2,83]),{31:[1,260],67:y,81:214,115:b,116:k,117:m},{6:11,7:12,8:f,9:d,10:A,11:l,20:17,22:18,23:19,24:20,25:21,26:22,27:_,32:[1,261],33:24,34:U,36:B,38:c,42:28,43:38,44:D,45:39,47:40,60:F,83:J,84:y1,85:b1,86:k1,87:K1,88:v,101:w,104:$,105:L,108:I,110:R,112:41,113:N,114:P,115:O,120:j1,121:z1,122:Y1,123:H1},t(m1,[2,53]),{43:262,44:D,45:39,47:40,60:F,88:v,101:w,104:$,105:L,108:I,110:R,112:41,113:N,114:P,115:O},t(S,[2,119],{105:P1}),t(Kt,[2,128],{107:264,10:r1,60:u1,83:a1,104:i1,108:o1,109:c1,110:l1,111:h1}),t(Z,[2,130]),t(Z,[2,132]),t(Z,[2,133]),t(Z,[2,134]),t(Z,[2,135]),t(Z,[2,136]),t(Z,[2,137]),t(Z,[2,138]),t(Z,[2,139]),t(S,[2,120],{105:P1}),{10:[1,265]},t(S,[2,121],{105:P1}),{10:[1,266]},t(Wt,[2,127]),t(S,[2,103],{105:P1}),t(S,[2,104],{112:111,44:D,60:F,88:v,101:w,104:$,105:L,108:I,110:R,113:N,114:P,115:O}),t(S,[2,108]),t(S,[2,110],{10:[1,267]}),t(S,[2,111]),{97:[1,268]},{51:[1,269]},{62:[1,270]},{66:[1,271]},{8:e1,9:s1,11:n1,21:272},t(C,[2,34]),t(m1,[2,52]),{10:r1,60:u1,83:a1,104:i1,106:273,107:237,108:o1,109:c1,110:l1,111:h1},t(Z,[2,131]),{14:x1,44:F1,60:S1,88:_1,100:274,104:B1,105:v1,108:w1,110:$1,113:L1,114:I1,115:R1,119:86},{14:x1,44:F1,60:S1,88:_1,100:275,104:B1,105:v1,108:w1,110:$1,113:L1,114:I1,115:R1,119:86},{97:[1,276]},t(S,[2,118]),t(V,[2,58]),{30:277,67:y,79:G,80:W,81:168,115:b,116:k,117:m},t(V,[2,66]),t(nt,p,{5:278}),t(Kt,[2,129],{107:264,10:r1,60:u1,83:a1,104:i1,108:o1,109:c1,110:l1,111:h1}),t(S,[2,124],{119:164,10:[1,279],14:x1,44:F1,60:S1,88:_1,104:B1,105:v1,108:w1,110:$1,113:L1,114:I1,115:R1}),t(S,[2,125],{119:164,10:[1,280],14:x1,44:F1,60:S1,88:_1,104:B1,105:v1,108:w1,110:$1,113:L1,114:I1,115:R1}),t(S,[2,112]),{31:[1,281],67:y,81:214,115:b,116:k,117:m},{6:11,7:12,8:f,9:d,10:A,11:l,20:17,22:18,23:19,24:20,25:21,26:22,27:_,32:[1,282],33:24,34:U,36:B,38:c,42:28,43:38,44:D,45:39,47:40,60:F,83:J,84:y1,85:b1,86:k1,87:K1,88:v,101:w,104:$,105:L,108:I,110:R,112:41,113:N,114:P,115:O,120:j1,121:z1,122:Y1,123:H1},{10:r1,60:u1,83:a1,91:283,104:i1,106:236,107:237,108:o1,109:c1,110:l1,111:h1},{10:r1,60:u1,83:a1,91:284,104:i1,106:236,107:237,108:o1,109:c1,110:l1,111:h1},t(V,[2,62]),t(C,[2,33]),t(S,[2,122],{105:P1}),t(S,[2,123],{105:P1})],defaultActions:{},parseError:i(function(h,E){if(!E.recoverable){var g=new Error(h);throw g.hash=E,g}this.trace(h)},"parseError"),parse:i(function(h){var E=this,g=[0],a=[],x=[null],e=[],G1=this.table,s="",H=0,jt=0,ce=e.slice.call(arguments,1),M=Object.create(this.lexer),E1={yy:{}};for(var ut in this.yy)Object.prototype.hasOwnProperty.call(this.yy,ut)&&(E1.yy[ut]=this.yy[ut]);M.setInput(h,E1.yy),E1.yy.lexer=M,E1.yy.parser=this,M.yylloc===void 0&&(M.yylloc={});var at=M.yylloc;e.push(at);var le=M.options&&M.options.ranges;function zt(){var X;return typeof(X=a.pop()||M.lex()||1)!="number"&&(X instanceof Array&&(X=(a=X).pop()),X=E.symbols_[X]||X),X}typeof E1.yy.parseError=="function"?this.parseError=E1.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError,i(function(X){g.length=g.length-2*X,x.length=x.length-X,e.length=e.length-X},"popStack"),i(zt,"lex");for(var Y,D1,q,Yt,X1,d1,Ht,Q1,O1={};;){if(D1=g[g.length-1],this.defaultActions[D1]?q=this.defaultActions[D1]:(Y==null&&(Y=zt()),q=G1[D1]&&G1[D1][Y]),q===void 0||!q.length||!q[0]){var qt="";for(X1 in Q1=[],G1[D1])this.terminals_[X1]&&X1>2&&Q1.push("'"+this.terminals_[X1]+"'");qt=M.showPosition?"Parse error on line "+(H+1)+`:
`+M.showPosition()+`
Expecting `+Q1.join(", ")+", got '"+(this.terminals_[Y]||Y)+"'":"Parse error on line "+(H+1)+": Unexpected "+(Y==1?"end of input":"'"+(this.terminals_[Y]||Y)+"'"),this.parseError(qt,{text:M.match,token:this.terminals_[Y]||Y,line:M.yylineno,loc:at,expected:Q1})}if(q[0]instanceof Array&&q.length>1)throw new Error("Parse Error: multiple actions possible at state: "+D1+", token: "+Y);switch(q[0]){case 1:g.push(Y),x.push(M.yytext),e.push(M.yylloc),g.push(q[1]),Y=null,jt=M.yyleng,s=M.yytext,H=M.yylineno,at=M.yylloc;break;case 2:if(d1=this.productions_[q[1]][1],O1.$=x[x.length-d1],O1._$={first_line:e[e.length-(d1||1)].first_line,last_line:e[e.length-1].last_line,first_column:e[e.length-(d1||1)].first_column,last_column:e[e.length-1].last_column},le&&(O1._$.range=[e[e.length-(d1||1)].range[0],e[e.length-1].range[1]]),(Yt=this.performAction.apply(O1,[s,jt,H,E1.yy,q[1],x,e].concat(ce)))!==void 0)return Yt;d1&&(g=g.slice(0,-1*d1*2),x=x.slice(0,-1*d1),e=e.slice(0,-1*d1)),g.push(this.productions_[q[1]][0]),x.push(O1.$),e.push(O1._$),Ht=G1[g[g.length-2]][g[g.length-1]],g.push(Ht);break;case 3:return!0}}return!0},"parse")},oe=function(){return{EOF:1,parseError:i(function(h,E){if(!this.yy.parser)throw new Error(h);this.yy.parser.parseError(h,E)},"parseError"),setInput:i(function(h,E){return this.yy=E||this.yy||{},this._input=h,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:i(function(){var h=this._input[0];return this.yytext+=h,this.yyleng++,this.offset++,this.match+=h,this.matched+=h,h.match(/(?:\r\n?|\n).*/g)?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),h},"input"),unput:i(function(h){var E=h.length,g=h.split(/(?:\r\n?|\n)/g);this._input=h+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-E),this.offset-=E;var a=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),g.length-1&&(this.yylineno-=g.length-1);var x=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:g?(g.length===a.length?this.yylloc.first_column:0)+a[a.length-g.length].length-g[0].length:this.yylloc.first_column-E},this.options.ranges&&(this.yylloc.range=[x[0],x[0]+this.yyleng-E]),this.yyleng=this.yytext.length,this},"unput"),more:i(function(){return this._more=!0,this},"more"),reject:i(function(){return this.options.backtrack_lexer?(this._backtrack=!0,this):this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"reject"),less:i(function(h){this.unput(this.match.slice(h))},"less"),pastInput:i(function(){var h=this.matched.substr(0,this.matched.length-this.match.length);return(h.length>20?"...":"")+h.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:i(function(){var h=this.match;return h.length<20&&(h+=this._input.substr(0,20-h.length)),(h.substr(0,20)+(h.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:i(function(){var h=this.pastInput(),E=new Array(h.length+1).join("-");return h+this.upcomingInput()+`
`+E+"^"},"showPosition"),test_match:i(function(h,E){var g,a,x;if(this.options.backtrack_lexer&&(x={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(x.yylloc.range=this.yylloc.range.slice(0))),(a=h[0].match(/(?:\r\n?|\n).*/g))&&(this.yylineno+=a.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:a?a[a.length-1].length-a[a.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+h[0].length},this.yytext+=h[0],this.match+=h[0],this.matches=h,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(h[0].length),this.matched+=h[0],g=this.performAction.call(this,this.yy,this,E,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),g)return g;if(this._backtrack){for(var e in x)this[e]=x[e];return!1}return!1},"test_match"),next:i(function(){if(this.done)return this.EOF;var h,E,g,a;this._input||(this.done=!0),this._more||(this.yytext="",this.match="");for(var x=this._currentRules(),e=0;e<x.length;e++)if((g=this._input.match(this.rules[x[e]]))&&(!E||g[0].length>E[0].length)){if(E=g,a=e,this.options.backtrack_lexer){if((h=this.test_match(g,x[e]))!==!1)return h;if(this._backtrack){E=!1;continue}return!1}if(!this.options.flex)break}return E?(h=this.test_match(E,x[a]))!==!1&&h:this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:i(function(){var h=this.next();return h||this.lex()},"lex"),begin:i(function(h){this.conditionStack.push(h)},"begin"),popState:i(function(){return this.conditionStack.length-1>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:i(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:i(function(h){return(h=this.conditionStack.length-1-Math.abs(h||0))>=0?this.conditionStack[h]:"INITIAL"},"topState"),pushState:i(function(h){this.begin(h)},"pushState"),stateStackSize:i(function(){return this.conditionStack.length},"stateStackSize"),options:{},performAction:i(function(h,E,g,a){switch(g){case 0:return this.begin("acc_title"),34;case 1:return this.popState(),"acc_title_value";case 2:return this.begin("acc_descr"),36;case 3:return this.popState(),"acc_descr_value";case 4:this.begin("acc_descr_multiline");break;case 5:case 12:case 14:case 17:case 20:case 23:case 33:this.popState();break;case 6:return"acc_descr_multiline_value";case 7:return this.pushState("shapeData"),E.yytext="",40;case 8:return this.pushState("shapeDataStr"),40;case 9:return this.popState(),40;case 10:const x=/\n\s*/g;return E.yytext=E.yytext.replace(x,"<br/>"),40;case 11:return 40;case 13:this.begin("callbackname");break;case 15:this.popState(),this.begin("callbackargs");break;case 16:return 94;case 18:return 95;case 19:return"MD_STR";case 21:this.begin("md_string");break;case 22:return"STR";case 24:this.pushState("string");break;case 25:return 83;case 26:return 101;case 27:return 84;case 28:return 103;case 29:return 85;case 30:return 86;case 31:return 96;case 32:this.begin("click");break;case 34:return 87;case 35:case 36:case 37:return h.lex.firstGraph()&&this.begin("dir"),12;case 38:return 27;case 39:return 32;case 40:case 41:case 42:case 43:return 97;case 44:return this.popState(),13;case 45:case 46:case 47:case 48:case 49:case 50:case 51:case 52:case 53:case 54:return this.popState(),14;case 55:return 120;case 56:return 121;case 57:return 122;case 58:return 123;case 59:return 104;case 60:case 101:return 110;case 61:return 46;case 62:return 60;case 63:case 102:return 44;case 64:return 8;case 65:return 105;case 66:case 100:return 114;case 67:case 70:case 73:return this.popState(),77;case 68:return this.pushState("edgeText"),75;case 69:case 72:case 75:return 118;case 71:return this.pushState("thickEdgeText"),75;case 74:return this.pushState("dottedEdgeText"),75;case 76:return 77;case 77:return this.popState(),53;case 78:case 114:return"TEXT";case 79:return this.pushState("ellipseText"),52;case 80:return this.popState(),55;case 81:return this.pushState("text"),54;case 82:return this.popState(),57;case 83:return this.pushState("text"),56;case 84:return 58;case 85:return this.pushState("text"),67;case 86:return this.popState(),64;case 87:return this.pushState("text"),63;case 88:return this.popState(),49;case 89:return this.pushState("text"),48;case 90:return this.popState(),69;case 91:return this.popState(),71;case 92:return 116;case 93:return this.pushState("trapText"),68;case 94:return this.pushState("trapText"),70;case 95:return 117;case 96:return 67;case 97:return 89;case 98:return"SEP";case 99:return 88;case 103:return 108;case 104:return 113;case 105:return 115;case 106:return this.popState(),62;case 107:return this.pushState("text"),62;case 108:return this.popState(),51;case 109:return this.pushState("text"),50;case 110:return this.popState(),31;case 111:return this.pushState("text"),29;case 112:return this.popState(),66;case 113:return this.pushState("text"),65;case 115:return"QUOTE";case 116:return 9;case 117:return 10;case 118:return 11}},"anonymous"),rules:[/^(?:accTitle\s*:\s*)/,/^(?:(?!\n||)*[^\n]*)/,/^(?:accDescr\s*:\s*)/,/^(?:(?!\n||)*[^\n]*)/,/^(?:accDescr\s*\{\s*)/,/^(?:[\}])/,/^(?:[^\}]*)/,/^(?:@\{)/,/^(?:["])/,/^(?:["])/,/^(?:[^\"]+)/,/^(?:[^}^"]+)/,/^(?:\})/,/^(?:call[\s]+)/,/^(?:\([\s]*\))/,/^(?:\()/,/^(?:[^(]*)/,/^(?:\))/,/^(?:[^)]*)/,/^(?:[^`"]+)/,/^(?:[`]["])/,/^(?:["][`])/,/^(?:[^"]+)/,/^(?:["])/,/^(?:["])/,/^(?:style\b)/,/^(?:default\b)/,/^(?:linkStyle\b)/,/^(?:interpolate\b)/,/^(?:classDef\b)/,/^(?:class\b)/,/^(?:href[\s])/,/^(?:click[\s]+)/,/^(?:[\s\n])/,/^(?:[^\s\n]*)/,/^(?:flowchart-elk\b)/,/^(?:graph\b)/,/^(?:flowchart\b)/,/^(?:subgraph\b)/,/^(?:end\b\s*)/,/^(?:_self\b)/,/^(?:_blank\b)/,/^(?:_parent\b)/,/^(?:_top\b)/,/^(?:(\r?\n)*\s*\n)/,/^(?:\s*LR\b)/,/^(?:\s*RL\b)/,/^(?:\s*TB\b)/,/^(?:\s*BT\b)/,/^(?:\s*TD\b)/,/^(?:\s*BR\b)/,/^(?:\s*<)/,/^(?:\s*>)/,/^(?:\s*\^)/,/^(?:\s*v\b)/,/^(?:.*direction\s+TB[^\n]*)/,/^(?:.*direction\s+BT[^\n]*)/,/^(?:.*direction\s+RL[^\n]*)/,/^(?:.*direction\s+LR[^\n]*)/,/^(?:[0-9]+)/,/^(?:#)/,/^(?::::)/,/^(?::)/,/^(?:&)/,/^(?:;)/,/^(?:,)/,/^(?:\*)/,/^(?:\s*[xo<]?--+[-xo>]\s*)/,/^(?:\s*[xo<]?--\s*)/,/^(?:[^-]|-(?!-)+)/,/^(?:\s*[xo<]?==+[=xo>]\s*)/,/^(?:\s*[xo<]?==\s*)/,/^(?:[^=]|=(?!))/,/^(?:\s*[xo<]?-?\.+-[xo>]?\s*)/,/^(?:\s*[xo<]?-\.\s*)/,/^(?:[^\.]|\.(?!))/,/^(?:\s*~~[\~]+\s*)/,/^(?:[-/\)][\)])/,/^(?:[^\(\)\[\]\{\}]|!\)+)/,/^(?:\(-)/,/^(?:\]\))/,/^(?:\(\[)/,/^(?:\]\])/,/^(?:\[\[)/,/^(?:\[\|)/,/^(?:>)/,/^(?:\)\])/,/^(?:\[\()/,/^(?:\)\)\))/,/^(?:\(\(\()/,/^(?:[\\(?=\])][\]])/,/^(?:\/(?=\])\])/,/^(?:\/(?!\])|\\(?!\])|[^\\\[\]\(\)\{\}\/]+)/,/^(?:\[\/)/,/^(?:\[\\)/,/^(?:<)/,/^(?:>)/,/^(?:\^)/,/^(?:\\\|)/,/^(?:v\b)/,/^(?:\*)/,/^(?:#)/,/^(?:&)/,/^(?:([A-Za-z0-9!"\#$%&'*+\.`?\\_\/]|-(?=[^\>\-\.])|(?!))+)/,/^(?:-)/,/^(?:[\u00AA\u00B5\u00BA\u00C0-\u00D6\u00D8-\u00F6]|[\u00F8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377]|[\u037A-\u037D\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5]|[\u03F7-\u0481\u048A-\u0527\u0531-\u0556\u0559\u0561-\u0587\u05D0-\u05EA]|[\u05F0-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE]|[\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA]|[\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u08A0]|[\u08A2-\u08AC\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0977]|[\u0979-\u097F\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2]|[\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u0A05-\u0A0A]|[\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39]|[\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8]|[\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0B05-\u0B0C]|[\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C]|[\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99]|[\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0]|[\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C33\u0C35-\u0C39\u0C3D]|[\u0C58\u0C59\u0C60\u0C61\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3]|[\u0CB5-\u0CB9\u0CBD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D05-\u0D0C\u0D0E-\u0D10]|[\u0D12-\u0D3A\u0D3D\u0D4E\u0D60\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1]|[\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81]|[\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3]|[\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6]|[\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A]|[\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081]|[\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D]|[\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0]|[\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310]|[\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F4\u1401-\u166C]|[\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u1700-\u170C\u170E-\u1711]|[\u1720-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7]|[\u17DC\u1820-\u1877\u1880-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191C]|[\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19C1-\u19C7\u1A00-\u1A16]|[\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4B\u1B83-\u1BA0\u1BAE\u1BAF]|[\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1CE9-\u1CEC]|[\u1CEE-\u1CF1\u1CF5\u1CF6\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D]|[\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D]|[\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3]|[\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F]|[\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128]|[\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2183\u2184]|[\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3]|[\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6]|[\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE]|[\u2DD0-\u2DD6\u2DD8-\u2DDE\u2E2F\u3005\u3006\u3031-\u3035\u303B\u303C]|[\u3041-\u3096\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312D]|[\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FCC]|[\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B]|[\uA640-\uA66E\uA67F-\uA697\uA6A0-\uA6E5\uA717-\uA71F\uA722-\uA788]|[\uA78B-\uA78E\uA790-\uA793\uA7A0-\uA7AA\uA7F8-\uA801\uA803-\uA805]|[\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB]|[\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uAA00-\uAA28]|[\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA80-\uAAAF\uAAB1\uAAB5]|[\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4]|[\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E]|[\uABC0-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D]|[\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36]|[\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D]|[\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC]|[\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF]|[\uFFD2-\uFFD7\uFFDA-\uFFDC])/,/^(?:\|)/,/^(?:\|)/,/^(?:\))/,/^(?:\()/,/^(?:\])/,/^(?:\[)/,/^(?:(\}))/,/^(?:\{)/,/^(?:[^\[\]\(\)\{\}\|\"]+)/,/^(?:")/,/^(?:(\r?\n)+)/,/^(?:\s)/,/^(?:$)/],conditions:{shapeDataEndBracket:{rules:[21,24,76,79,81,83,87,89,93,94,107,109,111,113],inclusive:!1},shapeDataStr:{rules:[9,10,21,24,76,79,81,83,87,89,93,94,107,109,111,113],inclusive:!1},shapeData:{rules:[8,11,12,21,24,76,79,81,83,87,89,93,94,107,109,111,113],inclusive:!1},callbackargs:{rules:[17,18,21,24,76,79,81,83,87,89,93,94,107,109,111,113],inclusive:!1},callbackname:{rules:[14,15,16,21,24,76,79,81,83,87,89,93,94,107,109,111,113],inclusive:!1},href:{rules:[21,24,76,79,81,83,87,89,93,94,107,109,111,113],inclusive:!1},click:{rules:[21,24,33,34,76,79,81,83,87,89,93,94,107,109,111,113],inclusive:!1},dottedEdgeText:{rules:[21,24,73,75,76,79,81,83,87,89,93,94,107,109,111,113],inclusive:!1},thickEdgeText:{rules:[21,24,70,72,76,79,81,83,87,89,93,94,107,109,111,113],inclusive:!1},edgeText:{rules:[21,24,67,69,76,79,81,83,87,89,93,94,107,109,111,113],inclusive:!1},trapText:{rules:[21,24,76,79,81,83,87,89,90,91,92,93,94,107,109,111,113],inclusive:!1},ellipseText:{rules:[21,24,76,77,78,79,81,83,87,89,93,94,107,109,111,113],inclusive:!1},text:{rules:[21,24,76,79,80,81,82,83,86,87,88,89,93,94,106,107,108,109,110,111,112,113,114],inclusive:!1},vertex:{rules:[21,24,76,79,81,83,87,89,93,94,107,109,111,113],inclusive:!1},dir:{rules:[21,24,44,45,46,47,48,49,50,51,52,53,54,76,79,81,83,87,89,93,94,107,109,111,113],inclusive:!1},acc_descr_multiline:{rules:[5,6,21,24,76,79,81,83,87,89,93,94,107,109,111,113],inclusive:!1},acc_descr:{rules:[3,21,24,76,79,81,83,87,89,93,94,107,109,111,113],inclusive:!1},acc_title:{rules:[1,21,24,76,79,81,83,87,89,93,94,107,109,111,113],inclusive:!1},md_string:{rules:[19,20,21,24,76,79,81,83,87,89,93,94,107,109,111,113],inclusive:!1},string:{rules:[21,22,23,24,76,79,81,83,87,89,93,94,107,109,111,113],inclusive:!1},INITIAL:{rules:[0,2,4,7,13,21,24,25,26,27,28,29,30,31,32,35,36,37,38,39,40,41,42,43,55,56,57,58,59,60,61,62,63,64,65,66,67,68,70,71,73,74,76,79,81,83,84,85,87,89,93,94,95,96,97,98,99,100,101,102,103,104,105,107,109,111,113,115,116,117,118],inclusive:!0}}}}();function q1(){this.yy={}}return rt.lexer=oe,i(q1,"Parser"),q1.prototype=rt,rt.Parser=q1,new q1}();ht.parser=ht;var os=ht,cs=i((t,n)=>{const u=Be,r=u(t,"r"),o=u(t,"g"),p=u(t,"b");return _e(r,o,p,n)},"fade"),Zs={parser:os,db:it,renderer:is,styles:i(t=>`.label {
    font-family: ${t.fontFamily};
    color: ${t.nodeTextColor||t.textColor};
  }
  .cluster-label text {
    fill: ${t.titleColor};
  }
  .cluster-label span {
    color: ${t.titleColor};
  }
  .cluster-label span p {
    background-color: transparent;
  }

  .label text,span {
    fill: ${t.nodeTextColor||t.textColor};
    color: ${t.nodeTextColor||t.textColor};
  }

  .node rect,
  .node circle,
  .node ellipse,
  .node polygon,
  .node path {
    fill: ${t.mainBkg};
    stroke: ${t.nodeBorder};
    stroke-width: 1px;
  }
  .rough-node .label text , .node .label text, .image-shape .label, .icon-shape .label {
    text-anchor: middle;
  }
  // .flowchart-label .text-outer-tspan {
  //   text-anchor: middle;
  // }
  // .flowchart-label .text-inner-tspan {
  //   text-anchor: start;
  // }

  .node .katex path {
    fill: #000;
    stroke: #000;
    stroke-width: 1px;
  }

  .rough-node .label,.node .label, .image-shape .label, .icon-shape .label {
    text-align: center;
  }
  .node.clickable {
    cursor: pointer;
  }


  .root .anchor path {
    fill: ${t.lineColor} !important;
    stroke-width: 0;
    stroke: ${t.lineColor};
  }

  .arrowheadPath {
    fill: ${t.arrowheadColor};
  }

  .edgePath .path {
    stroke: ${t.lineColor};
    stroke-width: 2.0px;
  }

  .flowchart-link {
    stroke: ${t.lineColor};
    fill: none;
  }

  .edgeLabel {
    background-color: ${t.edgeLabelBackground};
    p {
      background-color: ${t.edgeLabelBackground};
    }
    rect {
      opacity: 0.5;
      background-color: ${t.edgeLabelBackground};
      fill: ${t.edgeLabelBackground};
    }
    text-align: center;
  }

  /* For html labels only */
  .labelBkg {
    background-color: ${cs(t.edgeLabelBackground,.5)};
    // background-color:
  }

  .cluster rect {
    fill: ${t.clusterBkg};
    stroke: ${t.clusterBorder};
    stroke-width: 1px;
  }

  .cluster text {
    fill: ${t.titleColor};
  }

  .cluster span {
    color: ${t.titleColor};
  }
  /* .cluster div {
    color: ${t.titleColor};
  } */

  div.mermaidTooltip {
    position: absolute;
    text-align: center;
    max-width: 200px;
    padding: 2px;
    font-family: ${t.fontFamily};
    font-size: 12px;
    background: ${t.tertiaryColor};
    border: 1px solid ${t.border2};
    border-radius: 2px;
    pointer-events: none;
    z-index: 100;
  }

  .flowchartTitleText {
    text-anchor: middle;
    font-size: 18px;
    fill: ${t.textColor};
  }

  rect.text {
    fill: none;
    stroke-width: 0;
  }

  .icon-shape, .image-shape {
    background-color: ${t.edgeLabelBackground};
    p {
      background-color: ${t.edgeLabelBackground};
      padding: 2px;
    }
    rect {
      opacity: 0.5;
      background-color: ${t.edgeLabelBackground};
      fill: ${t.edgeLabelBackground};
    }
    text-align: center;
  }
`,"getStyles"),init:i(t=>{t.flowchart||(t.flowchart={}),t.layout&&Xt({layout:t.layout}),t.flowchart.arrowMarkerAbsolute=t.arrowMarkerAbsolute,Xt({flowchart:{arrowMarkerAbsolute:t.arrowMarkerAbsolute}}),it.clear(),it.setGen("gen-2")},"init")};export{Zs as diagram};
