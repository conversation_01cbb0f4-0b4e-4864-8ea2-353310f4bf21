<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Augment Autofix</title>
    <script nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
/**
 * Monaco bootstrap script
 *
 * This script is included directly in HTML files to load Monaco editor.
 * It's kept as a simple JS file to avoid any build/transpilation requirements.
 */

// Define the Monaco CDN version
const MONACO_VERSION = "0.52.2";
const MONACO_CDN_BASE = `https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/${MONACO_VERSION}/min`;

// Initialize augmentDeps if it doesn't exist
window.augmentDeps = window.augmentDeps || {};

// Create a promise that will resolve when Monaco is ready
let monacoResolve;
window.augmentDeps.monaco = new Promise((resolve) => {
  monacoResolve = resolve;
});

// If Monaco is already loaded, don't load it again
if (window.monaco) {
  console.log("Monaco already loaded, skipping bootstrap");
  initializeMonacoDeps();
} else {
  // Load the Monaco loader script
  const loaderScript = document.createElement("script");
  loaderScript.src = `${MONACO_CDN_BASE}/vs/loader.min.js`;
  loaderScript.onload = initializeMonaco;
  document.head.appendChild(loaderScript);
}

// Initialize Monaco after the loader script has loaded
function initializeMonaco() {
  // require is provided by loader.min.js
  require.config({
    paths: { vs: `${MONACO_CDN_BASE}/vs` },
  });

  require(["vs/editor/editor.main"], () => {
    initializeMonacoDeps();
  });
}

// Initialize Monaco dependencies after Monaco has loaded
function initializeMonacoDeps() {
  // Resolve the monaco promise
  if (monacoResolve) {
    monacoResolve(window.monaco);
  }
}

</script>
    <meta property="csp-nonce" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <script type="module" crossorigin src="./assets/autofix-BIvNrKEB.js" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ=="></script>
    <link rel="modulepreload" crossorigin href="./assets/SpinnerAugment-BRymMBwV.js" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="modulepreload" crossorigin href="./assets/autofix-state-d-ymFdyn.js" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="modulepreload" crossorigin href="./assets/BaseButton-rKFNr-KO.js" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="modulepreload" crossorigin href="./assets/ButtonAugment-B4rD0Iq1.js" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="modulepreload" crossorigin href="./assets/LanguageIcon-CLfcbvMk.js" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="modulepreload" crossorigin href="./assets/next-edit-types-904A5ehg.js" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="modulepreload" crossorigin href="./assets/IconButtonAugment-5yqT_m78.js" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="modulepreload" crossorigin href="./assets/IconFilePath-DzNJjZOv.js" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="modulepreload" crossorigin href="./assets/VSCodeCodicon-C_4gSY6s.js" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="modulepreload" crossorigin href="./assets/keypress-DD1aQVr0.js" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="modulepreload" crossorigin href="./assets/preload-helper-Dv6uf1Os.js" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="modulepreload" crossorigin href="./assets/toggleHighContrast-CwIv4U26.js" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="modulepreload" crossorigin href="./assets/resize-observer-DdAtcrRr.js" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="modulepreload" crossorigin href="./assets/index-C59i2ECO.js" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="modulepreload" crossorigin href="./assets/isObjectLike-CaDwJzWd.js" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="modulepreload" crossorigin href="./assets/file-reader-DY4e8Fcc.js" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="stylesheet" crossorigin href="./assets/SpinnerAugment-DnPofOlT.css" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="stylesheet" crossorigin href="./assets/BaseButton-DvMdfQ3F.css" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="stylesheet" crossorigin href="./assets/ButtonAugment-CNK8zC8i.css" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="stylesheet" crossorigin href="./assets/LanguageIcon-D78BqCXT.css" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="stylesheet" crossorigin href="./assets/IconButtonAugment-BTu-iglL.css" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="stylesheet" crossorigin href="./assets/IconFilePath-CiKel2Kp.css" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="stylesheet" crossorigin href="./assets/VSCodeCodicon-DVaocTud.css" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="stylesheet" crossorigin href="./assets/toggleHighContrast-D4zjdeIP.css" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="stylesheet" crossorigin href="./assets/index-McRKs1sU.css" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="stylesheet" crossorigin href="./assets/file-reader-B7W_DzJn.css" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="stylesheet" crossorigin href="./assets/autofix-CX5hiNi4.css" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
  </head>
  <body>
    <div id="app"></div>
  </body>
</html>
