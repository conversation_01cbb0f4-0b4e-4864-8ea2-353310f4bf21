  constructor() {
    // Existing properties
    this.balance = 1000;
    this.peakBalance = 1000;
    this.initialBalance = 1000;
    // Add profit target property
    this.profitTarget = 0; // 0 means no target (unlimited)
    // Rest of constructor
  }

  setBalance(amount) {
    this.balance = parseFloat(amount);
    this.peakBalance = this.balance;
    this.initialBalance = this.balance; // Also set initial balance
    this.updateBalance();
    this.updateStats();
  }

  setProfitTarget(target) {
    this.profitTarget = parseFloat(target);
  }

  handleTradeResult(result) {
    this.waitingForResult = false;
    const tradeAmount = this.lastTradeAmount || this.calculateTradeAmount();
    if (result === 'W') {
      const profit = tradeAmount * (this.payoutPercentage / 100);
      this.balance += profit;
      this.totalProfit += profit;
      // Existing code...
      
      // Check if profit target has been reached
      if (this.profitTarget > 0) {
        const currentProfit = this.balance - this.initialBalance;
        if (currentProfit >= this.profitTarget) {
          this.stop();
          this.showProfitTargetReached(currentProfit);
          return;
        }
      }
    } else {
      // Existing loss handling code...
    }
    // Rest of the method...
  }

  showProfitTargetReached(profit) {
    // Create and show modal
    const modal = document.createElement('div');
    modal.className = 'profit-target-modal';
    modal.innerHTML = `
      <div class="profit-target-content">
        <h2>🎉 Congratulations! 🎉</h2>
        <p>You've reached your profit target!</p>
        <div class="profit-details">
          <p>Initial Balance: $${this.initialBalance.toFixed(2)}</p>
          <p>Final Balance: $${this.balance.toFixed(2)}</p>
          <p>Total Profit: $${profit.toFixed(2)}</p>
          <p>Win Rate: ${(this.wins / Math.max(this.totalTrades, 1) * 100).toFixed(1)}%</p>
          <p>Total Trades: ${this.totalTrades}</p>
        </div>
        <button id="closeModal">Continue Trading</button>
        <button id="resetBot">Reset Bot</button>
      </div>
    `;
    document.body.appendChild(modal);
    
    // Add event listeners to buttons
    document.getElementById('closeModal').addEventListener('click', () => {
      document.body.removeChild(modal);
    });
    
    document.getElementById('resetBot').addEventListener('click', () => {
      document.body.removeChild(modal);
      this.resetBot();
    });
    
    // Update status
    this.updateStatus("Bot Status: Target Reached! 🎯");
  }

  resetBot() {
    this.balance = this.initialBalance;
    this.peakBalance = this.initialBalance;
    this.totalProfit = 0;
    this.totalLoss = 0;
    this.wins = 0;
    this.losses = 0;
    this.totalTrades = 0;
    this.consecutiveWins = 0;
    this.consecutiveLosses = 0;
    this.maxDrawdown = 0;
    this.updateBalance();
    this.updateStats();
    this.updateStatus("Bot Status: Reset Complete");
  }