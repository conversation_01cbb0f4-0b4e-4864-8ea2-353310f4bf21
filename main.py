"""
Binary Options Trading Bot

A trading bot for binary options that predicts random buy/sell trades with
expiration times and implements sound mathematical money management.

This is the main application file that integrates all components and provides a GUI.
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
import time
import logging
from datetime import datetime
import sys
import os

# Import custom modules
from money_management import MoneyManager
from trading_logic import TradingBot
from notification import NotificationSystem
from data_visualization import DataVisualizer

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("trading_bot.log"),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger('main')

class BinaryOptionsBot:
    """
    Main application class for the Binary Options Trading Bot.
    """
    
    def __init__(self, root):
        """
        Initialize the application.
        
        Args:
            root: Tkinter root window
        """
        self.root = root
        self.root.title("Binary Options Trading Bot")
        self.root.geometry("1200x800")
        self.root.minsize(1000, 700)
        self.root.configure(bg="#f0f0f0")
        
        # Set application icon if available
        try:
            if os.path.exists("icon.ico"):
                self.root.iconbitmap("icon.ico")
        except Exception as e:
            logger.warning(f"Could not set application icon: {e}")
        
        # Initialize components
        self.init_components()
        
        # Create GUI
        self.create_gui()
        
        # Start update loop
        self.update_thread = threading.Thread(target=self.update_loop, daemon=True)
        self.update_thread.start()
        
        logger.info("Binary Options Trading Bot initialized")
    
    def init_components(self):
        """
        Initialize the application components.
        """
        # Create money manager
        self.money_manager = MoneyManager(
            initial_balance=1000,
            max_risk_per_trade=0.02,  # 2% risk per trade
            win_rate=0.55,  # 55% win rate
            payout_ratio=0.85,  # 85% payout
            strategy="kelly"  # Use Kelly Criterion
        )
        
        # Create trading bot
        self.trading_bot = TradingBot(
            money_manager=self.money_manager,
            expiration_times=[30, 60, 120, 300]  # 30s, 1m, 2m, 5m
        )
        
        # Create notification system
        self.notification_system = NotificationSystem(enable_sound=True)
        
        # Create data visualizer
        self.data_visualizer = DataVisualizer(
            money_manager=self.money_manager,
            trading_bot=self.trading_bot
        )
        
        # Trading state
        self.auto_trading = False
        self.auto_trading_interval = 10  # seconds between trades
        
        logger.info("Application components initialized")
    
    def create_gui(self):
        """
        Create the graphical user interface.
        """
        # Create main frame
        main_frame = ttk.Frame(self.root, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Create top frame for controls
        control_frame = ttk.LabelFrame(main_frame, text="Trading Controls", padding=10)
        control_frame.pack(fill=tk.X, pady=5)
        
        # Create balance display
        balance_var = tk.StringVar(value=f"Balance: ${self.money_manager.balance:.2f}")
        balance_label = ttk.Label(control_frame, textvariable=balance_var, font=("Arial", 14, "bold"))
        balance_label.pack(side=tk.LEFT, padx=10)
        self.balance_var = balance_var
        
        # Create trading buttons
        button_frame = ttk.Frame(control_frame)
        button_frame.pack(side=tk.RIGHT)
        
        place_trade_btn = ttk.Button(button_frame, text="Place Trade", command=self.place_trade)
        place_trade_btn.pack(side=tk.LEFT, padx=5)
        
        self.auto_trade_btn = ttk.Button(button_frame, text="Start Auto Trading", command=self.toggle_auto_trading)
        self.auto_trade_btn.pack(side=tk.LEFT, padx=5)
        
        # Create settings frame
        settings_frame = ttk.LabelFrame(main_frame, text="Settings", padding=10)
        settings_frame.pack(fill=tk.X, pady=5)
        
        # Risk percentage setting
        risk_frame = ttk.Frame(settings_frame)
        risk_frame.pack(side=tk.LEFT, padx=20)
        
        ttk.Label(risk_frame, text="Risk per Trade (%)").pack(anchor=tk.W)
        
        risk_var = tk.DoubleVar(value=self.money_manager.max_risk_per_trade * 100)
        risk_scale = ttk.Scale(risk_frame, from_=0.5, to=5.0, variable=risk_var, 
                              orient=tk.HORIZONTAL, length=200)
        risk_scale.pack(side=tk.LEFT)
        
        risk_label = ttk.Label(risk_frame, textvariable=risk_var, width=4)
        risk_label.pack(side=tk.LEFT, padx=5)
        
        # Update risk when scale changes
        def update_risk(*args):
            self.money_manager.max_risk_per_trade = risk_var.get() / 100
        
        risk_var.trace_add("write", update_risk)
        
        # Strategy selection
        strategy_frame = ttk.Frame(settings_frame)
        strategy_frame.pack(side=tk.LEFT, padx=20)
        
        ttk.Label(strategy_frame, text="Money Management Strategy").pack(anchor=tk.W)
        
        strategy_var = tk.StringVar(value=self.money_manager.strategy)
        strategy_combo = ttk.Combobox(strategy_frame, textvariable=strategy_var, 
                                     values=["kelly", "fixed", "anti-martingale"], 
                                     state="readonly", width=15)
        strategy_combo.pack(pady=2)
        
        # Update strategy when selection changes
        def update_strategy(*args):
            self.money_manager.strategy = strategy_var.get()
        
        strategy_var.trace_add("write", update_strategy)
        
        # Auto trading interval setting
        interval_frame = ttk.Frame(settings_frame)
        interval_frame.pack(side=tk.LEFT, padx=20)
        
        ttk.Label(interval_frame, text="Auto Trading Interval (s)").pack(anchor=tk.W)
        
        interval_var = tk.IntVar(value=self.auto_trading_interval)
        interval_scale = ttk.Scale(interval_frame, from_=5, to=60, variable=interval_var, 
                                  orient=tk.HORIZONTAL, length=200)
        interval_scale.pack(side=tk.LEFT)
        
        interval_label = ttk.Label(interval_frame, textvariable=interval_var, width=4)
        interval_label.pack(side=tk.LEFT, padx=5)
        
        # Update interval when scale changes
        def update_interval(*args):
            self.auto_trading_interval = interval_var.get()
        
        interval_var.trace_add("write", update_interval)
        
        # Create main content frame with two columns
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # Left column for charts
        charts_frame = ttk.LabelFrame(content_frame, text="Charts", padding=10)
        charts_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        
        # Balance chart
        balance_chart_frame = ttk.Frame(charts_frame)
        balance_chart_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        balance_canvas = self.data_visualizer.create_balance_chart(balance_chart_frame)
        balance_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # Win/Loss chart
        win_loss_frame = ttk.Frame(charts_frame)
        win_loss_frame.pack(fill=tk.X, pady=5)
        
        self.win_loss_canvas = self.data_visualizer.create_win_loss_chart(win_loss_frame)
        self.win_loss_canvas.get_tk_widget().pack(side=tk.LEFT)
        
        # Statistics text
        self.stats_text = tk.Text(win_loss_frame, height=10, width=30, font=("Consolas", 10))
        self.stats_text.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=5)
        self.stats_text.insert(tk.END, self.data_visualizer.get_statistics_text())
        self.stats_text.config(state=tk.DISABLED)
        
        # Right column for trade info and notifications
        info_frame = ttk.Frame(content_frame)
        info_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))
        
        # Current trade info
        trade_info_frame = ttk.LabelFrame(info_frame, text="Current Trade", padding=10)
        trade_info_frame.pack(fill=tk.X, pady=(0, 5))
        
        self.trade_info_var = tk.StringVar(value="No active trade")
        trade_info_label = ttk.Label(trade_info_frame, textvariable=self.trade_info_var, 
                                    font=("Arial", 12))
        trade_info_label.pack(pady=10)
        
        # Notifications
        notifications_frame = ttk.LabelFrame(info_frame, text="Notifications", padding=10)
        notifications_frame.pack(fill=tk.BOTH, expand=True)
        
        self.notification_text = scrolledtext.ScrolledText(notifications_frame, height=20, 
                                                         font=("Consolas", 10))
        self.notification_text.pack(fill=tk.BOTH, expand=True)
        self.notification_text.config(state=tk.DISABLED)
        
        # Status bar
        status_frame = ttk.Frame(main_frame)
        status_frame.pack(fill=tk.X, pady=5)
        
        self.status_var = tk.StringVar(value="Ready")
        status_label = ttk.Label(status_frame, textvariable=self.status_var, 
                                relief=tk.SUNKEN, anchor=tk.W)
        status_label.pack(fill=tk.X)
        
        logger.info("GUI created")
    
    def place_trade(self):
        """
        Place a single trade.
        """
        if self.trading_bot.active_trade:
            messagebox.showinfo("Trade in Progress", "Please wait for the current trade to complete.")
            return
        
        # Place the trade
        trade_details = self.trading_bot.place_trade()
        
        if trade_details:
            # Update trade info display
            self.update_trade_info()
            
            # Send notification
            self.notification_system.notify_trade_placed(trade_details)
            self.update_notifications()
            
            # Update status
            self.status_var.set(f"Trade placed: {trade_details['direction']} for {trade_details['amount']:.2f}")
            
            logger.info(f"Manual trade placed: {trade_details}")
    
    def toggle_auto_trading(self):
        """
        Toggle automatic trading on/off.
        """
        self.auto_trading = not self.auto_trading
        
        if self.auto_trading:
            self.auto_trade_btn.config(text="Stop Auto Trading")
            self.status_var.set(f"Auto trading started with {self.auto_trading_interval}s interval")
            logger.info("Auto trading started")
        else:
            self.auto_trade_btn.config(text="Start Auto Trading")
            self.status_var.set("Auto trading stopped")
            logger.info("Auto trading stopped")
    
    def update_trade_info(self):
        """
        Update the current trade information display.
        """
        trade_status = self.trading_bot.get_trade_status()
        
        if trade_status['active']:
            direction = trade_status['direction'].upper()
            amount = trade_status['amount']
            remaining = trade_status['remaining']
            
            info_text = f"Active Trade: {direction} for ${amount:.2f}\nTime Remaining: {remaining}s"
            self.trade_info_var.set(info_text)
        else:
            self.trade_info_var.set("No active trade")
    
    def update_notifications(self):
        """
        Update the notifications display.
        """
        notifications = self.notification_system.get_notification_history()
        
        if not notifications:
            return
        
        self.notification_text.config(state=tk.NORMAL)
        self.notification_text.delete(1.0, tk.END)
        
        for notification in reversed(notifications):
            timestamp = notification['timestamp'].strftime("%H:%M:%S")
            message = notification['message']
            notification_type = notification['type']
            
            # Set tag for color based on notification type
            tag = f"tag_{notification_type}"
            
            self.notification_text.insert(tk.END, f"[{timestamp}] ", "time_tag")
            self.notification_text.insert(tk.END, f"{message}\n", tag)
        
        # Configure tags for colors
        self.notification_text.tag_configure("time_tag", foreground="gray")
        self.notification_text.tag_configure("tag_info", foreground="blue")
        self.notification_text.tag_configure("tag_success", foreground="green")
        self.notification_text.tag_configure("tag_warning", foreground="orange")
        self.notification_text.tag_configure("tag_error", foreground="red")
        
        self.notification_text.config(state=tk.DISABLED)
    
    def update_statistics(self):
        """
        Update the statistics display.
        """
        # Update balance display
        self.balance_var.set(f"Balance: ${self.money_manager.balance:.2f}")
        
        # Update statistics text
        self.stats_text.config(state=tk.NORMAL)
        self.stats_text.delete(1.0, tk.END)
        self.stats_text.insert(tk.END, self.data_visualizer.get_statistics_text())
        self.stats_text.config(state=tk.DISABLED)
    
    def update_charts(self):
        """
        Update all charts.
        """
        # Update balance chart
        self.data_visualizer.update_balance_chart()
        
        # Recreate win/loss chart (easier than updating)
        if hasattr(self, 'win_loss_canvas'):
            self.win_loss_canvas.get_tk_widget().pack_forget()
        
        win_loss_frame = self.win_loss_canvas.get_tk_widget().master
        self.win_loss_canvas = self.data_visualizer.create_win_loss_chart(win_loss_frame)
        self.win_loss_canvas.get_tk_widget().pack(side=tk.LEFT)
    
    def update_loop(self):
        """
        Main update loop that runs in a separate thread.
        """
        last_auto_trade_time = time.time()
        
        while True:
            try:
                # Check for trade results
                trade_result = self.trading_bot.check_trade_result()
                
                if trade_result:
                    # Notify about trade result
                    self.notification_system.notify_trade_result(trade_result)
                    
                    # Update displays
                    self.update_trade_info()
                    self.update_notifications()
                    self.update_statistics()
                    self.update_charts()
                
                # Update trade info display
                self.update_trade_info()
                
                # Place automatic trades if enabled
                current_time = time.time()
                if (self.auto_trading and 
                    not self.trading_bot.active_trade and 
                    current_time - last_auto_trade_time >= self.auto_trading_interval):
                    
                    self.place_trade()
                    last_auto_trade_time = current_time
                
                # Sleep to reduce CPU usage
                time.sleep(0.1)
                
            except Exception as e:
                logger.error(f"Error in update loop: {e}")
                time.sleep(1)  # Sleep longer on error

if __name__ == "__main__":
    try:
        # Create root window
        root = tk.Tk()
        
        # Create application
        app = BinaryOptionsBot(root)
        
        # Start main loop
        root.mainloop()
        
    except Exception as e:
        logger.critical(f"Application error: {e}", exc_info=True)
        messagebox.showerror("Error", f"An error occurred: {e}")
