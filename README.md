# Binary Options Trading Bot

A trading bot for binary options that predicts random buy/sell trades with expiration times and implements sound mathematical money management strategies.

## Features

- Random trade prediction (buy/sell)
- Multiple expiration time options (30s, 1m, 2m, 5m)
- Advanced money management strategies:
  - Modified Kelly Criterion
  - Fixed percentage risk
  - Anti-martingale system
- Real-time balance tracking and visualization
- Trade history and statistics
- Automatic trading mode
- Sound notifications for trade signals and results

## Money Management System

The bot implements several mathematically sound money management strategies:

### Modified Kelly Criterion

The Kelly Criterion is a formula that determines the optimal size of a series of bets to maximize the logarithm of wealth. For binary options, the formula is:

```
f* = (p(b) - q) / b
```

Where:
- f* is the fraction of the bankroll to wager
- p is the probability of winning
- q is the probability of losing (1-p)
- b is the payout ratio

The bot uses a conservative approach with 1/4 Kelly to reduce volatility.

### Anti-Martingale System

The anti-martingale system increases position size after wins and decreases after losses. This approach:

- Capitalizes on winning streaks
- Reduces exposure during losing streaks
- Helps preserve capital while maximizing gains

## Requirements

- Python 3.7+
- Required packages:
  - tkinter
  - matplotlib
  - numpy

## Installation

1. Clone or download this repository
2. Install the required packages:

```
pip install matplotlib numpy
```

## Usage

Run the main.py file to start the application:

```
python main.py
```

### Trading Controls

- **Place Trade**: Manually place a single trade
- **Start/Stop Auto Trading**: Toggle automatic trading mode
- **Risk per Trade**: Adjust the percentage of balance to risk on each trade
- **Money Management Strategy**: Select the strategy to use for position sizing
- **Auto Trading Interval**: Set the time between automatic trades

### Monitoring

- **Balance Chart**: View your account balance history
- **Win/Loss Chart**: See your win/loss ratio
- **Statistics**: Track performance metrics
- **Current Trade**: Information about the active trade
- **Notifications**: History of trade signals and results

## Disclaimer

This bot uses random trade predictions and is intended for educational purposes only. Binary options trading involves significant risk and can result in the loss of your invested capital. This software should not be used for actual trading without implementing a proper trading strategy.

## License

This project is licensed under the MIT License - see the LICENSE file for details.
