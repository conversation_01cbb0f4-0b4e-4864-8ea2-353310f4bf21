var l1=Object.defineProperty;var i1=(e,s,o)=>s in e?l1(e,s,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[s]=o;var R=(e,s,o)=>i1(e,typeof s!="symbol"?s+"":s,o);import{S as B,i as T,s as z,b as f,c as i,e as $,f as L,n as d,h as C,P as k,T as c1,a8 as X,ao as O,a as H,Q as E,D as I,y as r1,a1 as U,a2 as _,z as u1,a4 as x,V as S,W as Z,X as A,u as g,q as W,t as w,r as Y,g as J,B as d1,a6 as h1,a9 as p1,ap as C1,a7 as b,I as e1,J as o1,K as n1,L as a1,d as F,M as t1,j as M}from"./SpinnerAugment-BRymMBwV.js";function m1(e){let s,o;return{c(){s=f("svg"),o=f("path"),i(o,"fill-rule","evenodd"),i(o,"clip-rule","evenodd"),i(o,"d","M11.7816 4.03157C12.0062 3.80702 12.0062 3.44295 11.7816 3.2184C11.5571 2.99385 11.193 2.99385 10.9685 3.2184L7.50005 6.68682L4.03164 3.2184C3.80708 2.99385 3.44301 2.99385 3.21846 3.2184C2.99391 3.44295 2.99391 3.80702 3.21846 4.03157L6.68688 7.49999L3.21846 10.9684C2.99391 11.193 2.99391 11.557 3.21846 11.7816C3.44301 12.0061 3.80708 12.0061 4.03164 11.7816L7.50005 8.31316L10.9685 11.7816C11.193 12.0061 11.5571 12.0061 11.7816 11.7816C12.0062 11.557 12.0062 11.193 11.7816 10.9684L8.31322 7.49999L11.7816 4.03157Z"),i(o,"fill","currentColor"),i(s,"width","15"),i(s,"height","15"),i(s,"viewBox","0 0 15 15"),i(s,"fill","none"),i(s,"xmlns","http://www.w3.org/2000/svg")},m(l,a){$(l,s,a),L(s,o)},p:d,i:d,o:d,d(l){l&&C(s)}}}class E1 extends B{constructor(s){super(),T(this,s,null,m1,z,{})}}function g1(e){let s,o;return{c(){s=f("svg"),o=f("path"),i(o,"fill-rule","evenodd"),i(o,"clip-rule","evenodd"),i(o,"d","M3.5 2C3.22386 2 3 2.22386 3 2.5V12.5C3 12.7761 3.22386 13 3.5 13H11.5C11.7761 13 12 12.7761 12 12.5V4.70711L9.29289 2H3.5ZM2 2.5C2 1.67157 2.67157 1 3.5 1H9.5C9.63261 1 9.75979 1.05268 9.85355 1.14645L12.7803 4.07322C12.921 4.21388 13 4.40464 13 4.60355V12.5C13 13.3284 12.3284 14 11.5 14H3.5C2.67157 14 2 13.3284 2 12.5V2.5ZM4.75 7.5C4.75 7.22386 4.97386 7 5.25 7H7V5.25C7 4.97386 7.22386 4.75 7.5 4.75C7.77614 4.75 8 4.97386 8 5.25V7H9.75C10.0261 7 10.25 7.22386 10.25 7.5C10.25 7.77614 10.0261 8 9.75 8H8V9.75C8 10.0261 7.77614 10.25 7.5 10.25C7.22386 10.25 7 10.0261 7 9.75V8H5.25C4.97386 8 4.75 7.77614 4.75 7.5Z"),i(o,"fill","currentColor"),i(s,"width","15"),i(s,"height","15"),i(s,"viewBox","0 0 15 15"),i(s,"fill","none"),i(s,"xmlns","http://www.w3.org/2000/svg")},m(l,a){$(l,s,a),L(s,o)},p:d,i:d,o:d,d(l){l&&C(s)}}}class S1 extends B{constructor(s){super(),T(this,s,null,g1,z,{})}}class N{constructor(s){this._opts=s}get color(){return this._opts.color}get size(){return this._opts.size??1}get variant(){return this._opts.variant}}R(N,"CONTEXT_KEY","augment-badge");const f1=e=>({}),j=e=>({}),w1=e=>({}),q=e=>({}),v1=e=>({}),D=e=>({});function Q(e){let s,o;const l=e[7].leftButtons,a=k(l,e,e[16],q);return{c(){s=E("div"),a&&a.c(),i(s,"class","c-badge__left-buttons svelte-p47xt3")},m(n,c){$(n,s,c),a&&a.m(s,null),o=!0},p(n,c){a&&a.p&&(!o||65536&c)&&S(a,l,n,n[16],o?A(l,n[16],c,w1):Z(n[16]),q)},i(n){o||(g(a,n),o=!0)},o(n){w(a,n),o=!1},d(n){n&&C(s),a&&a.d(n)}}}function x1(e){let s,o;const l=e[7].default,a=k(l,e,e[16],null);return{c(){s=E("div"),a&&a.c(),i(s,"class","c-badge-body svelte-p47xt3")},m(n,c){$(n,s,c),a&&a.m(s,null),o=!0},p(n,c){a&&a.p&&(!o||65536&c)&&S(a,l,n,n[16],o?A(l,n[16],c,null):Z(n[16]),null)},i(n){o||(g(a,n),o=!0)},o(n){w(a,n),o=!1},d(n){n&&C(s),a&&a.d(n)}}}function s1(e){let s,o;const l=e[7].rightButtons,a=k(l,e,e[16],j);return{c(){s=E("div"),a&&a.c(),i(s,"class","c-badge__right-buttons svelte-p47xt3")},m(n,c){$(n,s,c),a&&a.m(s,null),o=!0},p(n,c){a&&a.p&&(!o||65536&c)&&S(a,l,n,n[16],o?A(l,n[16],c,f1):Z(n[16]),j)},i(n){o||(g(a,n),o=!0)},o(n){w(a,n),o=!1},d(n){n&&C(s),a&&a.d(n)}}}function b1(e){let s,o,l,a,n,c,m,v,P;const y=e[7].chaser,h=k(y,e,e[16],D);let u=e[6].leftButtons&&Q(e);a=new c1({props:{size:e[5],weight:"medium",$$slots:{default:[x1]},$$scope:{ctx:e}}});let t=e[6].rightButtons&&s1(e),G=[X(e[0]),O(e[3]),{class:c=`c-badge c-badge--${e[0]} c-badge--${e[1]} c-badge--size-${e[2]}`},{role:"button"},{tabindex:"0"}],V={};for(let r=0;r<G.length;r+=1)V=H(V,G[r]);return{c(){s=E("div"),h&&h.c(),o=I(),u&&u.c(),l=I(),r1(a.$$.fragment),n=I(),t&&t.c(),U(s,V),_(s,"c-badge--highContrast",e[4]),_(s,"svelte-p47xt3",!0)},m(r,p){$(r,s,p),h&&h.m(s,null),L(s,o),u&&u.m(s,null),L(s,l),u1(a,s,null),L(s,n),t&&t.m(s,null),m=!0,v||(P=[x(s,"click",e[8]),x(s,"keydown",e[9]),x(s,"keyup",e[10]),x(s,"mousedown",e[11]),x(s,"mouseover",e[12]),x(s,"focus",e[13]),x(s,"mouseleave",e[14]),x(s,"blur",e[15])],v=!0)},p(r,[p]){h&&h.p&&(!m||65536&p)&&S(h,y,r,r[16],m?A(y,r[16],p,v1):Z(r[16]),D),r[6].leftButtons?u?(u.p(r,p),64&p&&g(u,1)):(u=Q(r),u.c(),g(u,1),u.m(s,l)):u&&(W(),w(u,1,1,()=>{u=null}),Y());const K={};65536&p&&(K.$$scope={dirty:p,ctx:r}),a.$set(K),r[6].rightButtons?t?(t.p(r,p),64&p&&g(t,1)):(t=s1(r),t.c(),g(t,1),t.m(s,null)):t&&(W(),w(t,1,1,()=>{t=null}),Y()),U(s,V=J(G,[1&p&&X(r[0]),8&p&&O(r[3]),(!m||7&p&&c!==(c=`c-badge c-badge--${r[0]} c-badge--${r[1]} c-badge--size-${r[2]}`))&&{class:c},{role:"button"},{tabindex:"0"}])),_(s,"c-badge--highContrast",r[4]),_(s,"svelte-p47xt3",!0)},i(r){m||(g(h,r),g(u),g(a.$$.fragment,r),g(t),m=!0)},o(r){w(h,r),w(u),w(a.$$.fragment,r),w(t),m=!1},d(r){r&&C(s),h&&h.d(r),u&&u.d(),d1(a),t&&t.d(),v=!1,h1(P)}}}function $1(e,s,o){let{$$slots:l={},$$scope:a}=s;const n=p1(l);let{color:c="accent"}=s,{variant:m="soft"}=s,{size:v=1}=s,{radius:P="medium"}=s,{highContrast:y=!1}=s;const h=v===3?2:1,u=new N({color:c,size:v,variant:m});return C1(N.CONTEXT_KEY,u),e.$$set=t=>{"color"in t&&o(0,c=t.color),"variant"in t&&o(1,m=t.variant),"size"in t&&o(2,v=t.size),"radius"in t&&o(3,P=t.radius),"highContrast"in t&&o(4,y=t.highContrast),"$$scope"in t&&o(16,a=t.$$scope)},[c,m,v,P,y,h,n,l,function(t){b.call(this,e,t)},function(t){b.call(this,e,t)},function(t){b.call(this,e,t)},function(t){b.call(this,e,t)},function(t){b.call(this,e,t)},function(t){b.call(this,e,t)},function(t){b.call(this,e,t)},function(t){b.call(this,e,t)},a]}class Z1 extends B{constructor(s){super(),T(this,s,$1,b1,z,{color:0,variant:1,size:2,radius:3,highContrast:4})}}var L1=(e=>(e.readFile="read-file",e.saveFile="save-file",e.editFile="edit-file",e.clarify="clarify",e.onboardingSubAgent="onboarding-sub-agent",e.launchProcess="launch-process",e.killProcess="kill-process",e.readProcess="read-process",e.writeProcess="write-process",e.listProcesses="list-processes",e.waitProcess="wait-process",e.openBrowser="open-browser",e.strReplaceEditor="str-replace-editor",e.remember="remember",e.diagnostics="diagnostics",e.setupScript="setup-script",e.readTerminal="read-terminal",e.gitCommitRetrieval="git-commit-retrieval",e))(L1||{}),H1=(e=>(e.remoteToolHost="remoteToolHost",e.localToolHost="localToolHost",e.sidecarToolHost="sidecarToolHost",e.mcpHost="mcpHost",e))(H1||{}),B1=(e=>(e[e.ContentText=0]="ContentText",e[e.ContentImage=1]="ContentImage",e))(B1||{}),T1=(e=>(e[e.Unknown=0]="Unknown",e[e.WebSearch=1]="WebSearch",e[e.GitHubApi=8]="GitHubApi",e[e.Linear=12]="Linear",e[e.Jira=13]="Jira",e[e.Confluence=14]="Confluence",e[e.Notion=15]="Notion",e[e.Supabase=16]="Supabase",e[e.Glean=17]="Glean",e))(T1||{});function z1(e){let s,o;return{c(){s=f("svg"),o=f("path"),i(o,"fill-rule","evenodd"),i(o,"clip-rule","evenodd"),i(o,"d","M3.13523 6.15803C3.3241 5.95657 3.64052 5.94637 3.84197 6.13523L7.5 9.56464L11.158 6.13523C11.3595 5.94637 11.6759 5.95657 11.8648 6.15803C12.0536 6.35949 12.0434 6.67591 11.842 6.86477L7.84197 10.6148C7.64964 10.7951 7.35036 10.7951 7.15803 10.6148L3.15803 6.86477C2.95657 6.67591 2.94637 6.35949 3.13523 6.15803Z"),i(o,"fill","currentColor"),i(s,"width","15"),i(s,"height","15"),i(s,"viewBox","0 0 15 15"),i(s,"fill","none"),i(s,"xmlns","http://www.w3.org/2000/svg")},m(l,a){$(l,s,a),L(s,o)},p:d,i:d,o:d,d(l){l&&C(s)}}}class A1 extends B{constructor(s){super(),T(this,s,null,z1,z,{})}}function y1(e){let s,o,l=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},e[0]],a={};for(let n=0;n<l.length;n+=1)a=H(a,l[n]);return{c(){s=f("svg"),o=new e1(!0),this.h()},l(n){s=o1(n,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var c=n1(s);o=a1(c,!0),c.forEach(C),this.h()},h(){o.a=null,F(s,a)},m(n,c){t1(n,s,c),o.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M256 48a208 208 0 1 1 0 416 208 208 0 1 1 0-416m0 464a256 256 0 1 0 0-512 256 256 0 1 0 0 512m-81-337c-9.4 9.4-9.4 24.6 0 33.9l47 47-47 47c-9.4 9.4-9.4 24.6 0 33.9s24.6 9.4 33.9 0l47-47 47 47c9.4 9.4 24.6 9.4 33.9 0s9.4-24.6 0-33.9l-47-47 47-47c9.4-9.4 9.4-24.6 0-33.9s-24.6-9.4-33.9 0l-47 47-47-47c-9.4-9.4-24.6-9.4-33.9 0"/>',s)},p(n,[c]){F(s,a=J(l,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&c&&n[0]]))},i:d,o:d,d(n){n&&C(s)}}}function P1(e,s,o){return e.$$set=l=>{o(0,s=H(H({},s),M(l)))},[s=M(s)]}class G1 extends B{constructor(s){super(),T(this,s,P1,y1,z,{})}}function V1(e){let s,o,l=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 448 512"},e[0]],a={};for(let n=0;n<l.length;n+=1)a=H(a,l[n]);return{c(){s=f("svg"),o=new e1(!0),this.h()},l(n){s=o1(n,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var c=n1(s);o=a1(c,!0),c.forEach(C),this.h()},h(){o.a=null,F(s,a)},m(n,c){t1(n,s,c),o.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M177.1 48h93.7c2.7 0 5.2 1.3 6.7 3.6l19 28.4h-145l19-28.4c1.5-2.2 4-3.6 6.7-3.6zm177.1 32-36.7-55.1C307.1 9.4 289.6 0 270.9 0h-93.8c-18.7 0-36.2 9.4-46.6 24.9L93.8 80H24C10.7 80 0 90.7 0 104s10.7 24 24 24h11.6l24 324.7c2.5 33.4 30.3 59.3 63.8 59.3h201.1c33.5 0 61.3-25.9 63.8-59.3L412.4 128H424c13.3 0 24-10.7 24-24s-10.7-24-24-24h-56.1zm10.1 48-23.8 321.2c-.6 8.4-7.6 14.8-16 14.8H123.4c-8.4 0-15.3-6.5-16-14.8L83.7 128z"/>',s)},p(n,[c]){F(s,a=J(l,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 448 512"},1&c&&n[0]]))},i:d,o:d,d(n){n&&C(s)}}}function _1(e,s,o){return e.$$set=l=>{o(0,s=H(H({},s),M(l)))},[s=M(s)]}class I1 extends B{constructor(s){super(),T(this,s,_1,V1,z,{})}}function F1(e){let s,o;return{c(){s=f("svg"),o=f("path"),i(o,"fill-rule","evenodd"),i(o,"clip-rule","evenodd"),i(o,"d","M7.49933 0.25C3.49635 0.25 0.25 3.49593 0.25 7.50024C0.25 10.703 2.32715 13.4206 5.2081 14.3797C5.57084 14.446 5.70302 14.2222 5.70302 14.0299C5.70302 13.8576 5.69679 13.4019 5.69323 12.797C3.67661 13.235 3.25112 11.825 3.25112 11.825C2.92132 10.9874 2.44599 10.7644 2.44599 10.7644C1.78773 10.3149 2.49584 10.3238 2.49584 10.3238C3.22353 10.375 3.60629 11.0711 3.60629 11.0711C4.25298 12.1788 5.30335 11.8588 5.71638 11.6732C5.78225 11.205 5.96962 10.8854 6.17658 10.7043C4.56675 10.5209 2.87415 9.89918 2.87415 7.12104C2.87415 6.32925 3.15677 5.68257 3.62053 5.17563C3.54576 4.99226 3.29697 4.25521 3.69174 3.25691C3.69174 3.25691 4.30015 3.06196 5.68522 3.99973C6.26337 3.83906 6.8838 3.75895 7.50022 3.75583C8.1162 3.75895 8.73619 3.83906 9.31523 3.99973C10.6994 3.06196 11.3069 3.25691 11.3069 3.25691C11.7026 4.25521 11.4538 4.99226 11.3795 5.17563C11.8441 5.68257 12.1245 6.32925 12.1245 7.12104C12.1245 9.9063 10.4292 10.5192 8.81452 10.6985C9.07444 10.9224 9.30633 11.3648 9.30633 12.0413C9.30633 13.0102 9.29742 13.7922 9.29742 14.0299C9.29742 14.2239 9.42828 14.4496 9.79591 14.3788C12.6746 13.4179 14.75 10.7025 14.75 7.50024C14.75 3.49593 11.5036 0.25 7.49933 0.25Z"),i(o,"fill","currentColor"),i(s,"width","15"),i(s,"height","15"),i(s,"viewBox","0 0 15 15"),i(s,"fill","none"),i(s,"xmlns","http://www.w3.org/2000/svg")},m(l,a){$(l,s,a),L(s,o)},p:d,i:d,o:d,d(l){l&&C(s)}}}class N1 extends B{constructor(s){super(),T(this,s,null,F1,z,{})}}export{N as B,A1 as C,S1 as F,N1 as G,L1 as L,T1 as R,I1 as T,Z1 as a,G1 as b,H1 as c,E1 as d,B1 as e};
