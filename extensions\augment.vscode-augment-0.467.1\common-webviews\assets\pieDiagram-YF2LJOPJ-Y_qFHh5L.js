import{p as E}from"./chunk-TMUBEWPD-DrLg8OXC.js";import{X as y,O,aH as L,D as N,q as V,r as q,s as H,g as I,c as X,b as _,_ as m,l as R,x as G,d as J,E as K,I as Q,a5 as U,k as Y}from"./AugmentMessage-D6u8uU-A.js";import{p as Z}from"./gitGraph-YCYPL57B-CERJFtxy.js";import{d as F}from"./arc-BauXLw3o.js";import{o as tt}from"./ordinal-_rw2EY4v.js";import"./SpinnerAugment-BRymMBwV.js";import"./github-DCBOV_oD.js";import"./pen-to-square-DsQhBKje.js";import"./augment-logo-Cb6FLr8P.js";import"./TextTooltipAugment-VmEmcMVL.js";import"./BaseButton-rKFNr-KO.js";import"./IconButtonAugment-5yqT_m78.js";import"./Content-CZt_q_72.js";import"./globals-D0QH3NT1.js";import"./open-in-new-window-_CQmfLgB.js";import"./types-LfaCSdmF.js";import"./chat-types-NgqNgjwU.js";import"./test_service_pb-B6vKXZrG.js";import"./file-paths-BcSg4gks.js";import"./types-DvVg976p.js";import"./folder-Dee44ws-.js";import"./folder-opened-qXv2xhk3.js";import"./types-BSMhNRWH.js";import"./index-C57dba63.js";import"./CardAugment-BpvKVhgc.js";import"./TextAreaAugment-DJ6NCdxI.js";import"./diff-utils-UT8EbVpu.js";import"./toggleHighContrast-CwIv4U26.js";import"./preload-helper-Dv6uf1Os.js";import"./index-C59i2ECO.js";import"./keypress-DD1aQVr0.js";import"./await_block-B6zp5aG7.js";import"./ButtonAugment-B4rD0Iq1.js";import"./expand-DqlmSj23.js";import"./mcp-logo-CaRmgfKF.js";import"./ellipsis-bUrc34Ic.js";import"./IconFilePath-DzNJjZOv.js";import"./LanguageIcon-CLfcbvMk.js";import"./next-edit-types-904A5ehg.js";import"./magnifying-glass-Dmvedn_X.js";import"./MaterialIcon-D3827jJW.js";import"./Filespan-V8rQ2geT.js";import"./chevron-down-B8lwMNrs.js";import"./lodash-xZzKttBF.js";import"./terminal-BinWa3Yp.js";import"./_baseUniq-DiCvE7S1.js";import"./_basePickBy-Cuq_OEHt.js";import"./clone-AbSWQI7G.js";import"./init-g68aIKmP.js";function et(t,r){return r<t?-1:r>t?1:r>=t?0:NaN}function rt(t){return t}var W=N.pie,B={sections:new Map,showData:!1,config:W},M=B.sections,z=B.showData,at=structuredClone(W),P={getConfig:m(()=>structuredClone(at),"getConfig"),clear:m(()=>{M=new Map,z=B.showData,G()},"clear"),setDiagramTitle:V,getDiagramTitle:q,setAccTitle:H,getAccTitle:I,setAccDescription:X,getAccDescription:_,addSection:m(({label:t,value:r})=>{M.has(t)||(M.set(t,r),R.debug(`added new section: ${t}, with value: ${r}`))},"addSection"),getSections:m(()=>M,"getSections"),setShowData:m(t=>{z=t},"setShowData"),getShowData:m(()=>z,"getShowData")},it=m((t,r)=>{E(t,r),r.setShowData(t.showData),t.sections.map(r.addSection)},"populateDb"),nt={parse:m(async t=>{const r=await Z("pie",t);R.debug(r),it(r,P)},"parse")},ot=m(t=>`
  .pieCircle{
    stroke: ${t.pieStrokeColor};
    stroke-width : ${t.pieStrokeWidth};
    opacity : ${t.pieOpacity};
  }
  .pieOuterCircle{
    stroke: ${t.pieOuterStrokeColor};
    stroke-width: ${t.pieOuterStrokeWidth};
    fill: none;
  }
  .pieTitleText {
    text-anchor: middle;
    font-size: ${t.pieTitleTextSize};
    fill: ${t.pieTitleTextColor};
    font-family: ${t.fontFamily};
  }
  .slice {
    font-family: ${t.fontFamily};
    fill: ${t.pieSectionTextColor};
    font-size:${t.pieSectionTextSize};
    // fill: white;
  }
  .legend text {
    fill: ${t.pieLegendTextColor};
    font-family: ${t.fontFamily};
    font-size: ${t.pieLegendTextSize};
  }
`,"getStyles"),lt=m(t=>{const r=[...t.entries()].map(l=>({label:l[0],value:l[1]})).sort((l,u)=>u.value-l.value);return function(){var l=rt,u=et,c=null,w=y(0),S=y(O),$=y(0);function a(e){var i,p,n,A,g,s=(e=L(e)).length,v=0,D=new Array(s),d=new Array(s),f=+w.apply(this,arguments),C=Math.min(O,Math.max(-O,S.apply(this,arguments)-f)),h=Math.min(Math.abs(C)/s,$.apply(this,arguments)),b=h*(C<0?-1:1);for(i=0;i<s;++i)(g=d[D[i]=i]=+l(e[i],i,e))>0&&(v+=g);for(u!=null?D.sort(function(x,T){return u(d[x],d[T])}):c!=null&&D.sort(function(x,T){return c(e[x],e[T])}),i=0,n=v?(C-s*b)/v:0;i<s;++i,f=A)p=D[i],A=f+((g=d[p])>0?g*n:0)+b,d[p]={data:e[p],index:i,value:g,startAngle:f,endAngle:A,padAngle:h};return d}return a.value=function(e){return arguments.length?(l=typeof e=="function"?e:y(+e),a):l},a.sortValues=function(e){return arguments.length?(u=e,c=null,a):u},a.sort=function(e){return arguments.length?(c=e,u=null,a):c},a.startAngle=function(e){return arguments.length?(w=typeof e=="function"?e:y(+e),a):w},a.endAngle=function(e){return arguments.length?(S=typeof e=="function"?e:y(+e),a):S},a.padAngle=function(e){return arguments.length?($=typeof e=="function"?e:y(+e),a):$},a}().value(l=>l.value)(r)},"createPieArcs"),ae={parser:nt,db:P,renderer:{draw:m((t,r,l,u)=>{R.debug(`rendering pie chart
`+t);const c=u.db,w=J(),S=K(c.getConfig(),w.pie),$=18,a=450,e=a,i=Q(r),p=i.append("g");p.attr("transform","translate(225,225)");const{themeVariables:n}=w;let[A]=U(n.pieOuterStrokeWidth);A??(A=2);const g=S.textPosition,s=Math.min(e,a)/2-40,v=F().innerRadius(0).outerRadius(s),D=F().innerRadius(s*g).outerRadius(s*g);p.append("circle").attr("cx",0).attr("cy",0).attr("r",s+A/2).attr("class","pieOuterCircle");const d=c.getSections(),f=lt(d),C=[n.pie1,n.pie2,n.pie3,n.pie4,n.pie5,n.pie6,n.pie7,n.pie8,n.pie9,n.pie10,n.pie11,n.pie12],h=tt(C);p.selectAll("mySlices").data(f).enter().append("path").attr("d",v).attr("fill",o=>h(o.data.label)).attr("class","pieCircle");let b=0;d.forEach(o=>{b+=o}),p.selectAll("mySlices").data(f).enter().append("text").text(o=>(o.data.value/b*100).toFixed(0)+"%").attr("transform",o=>"translate("+D.centroid(o)+")").style("text-anchor","middle").attr("class","slice"),p.append("text").text(c.getDiagramTitle()).attr("x",0).attr("y",-200).attr("class","pieTitleText");const x=p.selectAll(".legend").data(h.domain()).enter().append("g").attr("class","legend").attr("transform",(o,k)=>"translate(216,"+(22*k-22*h.domain().length/2)+")");x.append("rect").attr("width",$).attr("height",$).style("fill",h).style("stroke",h),x.data(f).append("text").attr("x",22).attr("y",14).text(o=>{const{label:k,value:j}=o.data;return c.getShowData()?`${k} [${j}]`:k});const T=512+Math.max(...x.selectAll("text").nodes().map(o=>(o==null?void 0:o.getBoundingClientRect().width)??0));i.attr("viewBox",`0 0 ${T} 450`),Y(i,a,T,S.useMaxWidth)},"draw")},styles:ot};export{ae as diagram};
