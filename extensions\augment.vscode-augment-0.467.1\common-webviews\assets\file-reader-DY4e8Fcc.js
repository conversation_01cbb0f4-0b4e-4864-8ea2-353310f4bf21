import{S as O,i as R,s as B,E as v2,e as A,q as j1,t as F,r as M1,u as $,h as S,Z as A3,a as b1,j as z2,P as _1,Q as z,D as K,c as o,a1 as D1,a2 as r1,f,V as $1,W as A1,X as F1,g as v3,y as D,z as T,a4 as d1,a5 as T1,B as I,a6 as w2,ap as P2,ai as Z2,ae as D2,b as m,G as Z,n as _,aa as N,aB as F3,ag as I1,ah as L2,U as T2,ab as k1,H as I2,a0 as q2,_ as V2,w as G2,F as U2,am as y2,Y as W2,an as H2}from"./SpinnerAugment-BRymMBwV.js";import{V as K2}from"./VSCodeCodicon-C_4gSY6s.js";import{o as i3}from"./keypress-DD1aQVr0.js";import{I as J2,n as m1,A as k2,k as Q2,a as x1,j as N1,l as X2,m as S3}from"./IconFilePath-DzNJjZOv.js";import{e as j3,W as Y2}from"./BaseButton-rKFNr-KO.js";import{S as r3,C as M3}from"./next-edit-types-904A5ehg.js";import{e as t0}from"./toggleHighContrast-CwIv4U26.js";import{r as n0}from"./resize-observer-DdAtcrRr.js";import{a as e0,g as E3}from"./index-C59i2ECO.js";import{r as o0}from"./LanguageIcon-CLfcbvMk.js";import{_ as G1,i as x2,a as s1,b as O3,c as U1,d as i0}from"./isObjectLike-CaDwJzWd.js";const Y1={duration:300,easing:"ease-out"},r0=(n,t=Y1)=>{const e=n.querySelector("summary"),a=n.querySelector(".c-detail__content");if(!e||!a)return;t={...Y1,...t};const i=/^(tb|vertical)/.test(getComputedStyle(a).writingMode);let r=!1;const l=d=>{r=!0,d&&(n.open=!0),n.dispatchEvent(new CustomEvent(d?"open-start":"close-start",{detail:n}));const C=a[i?"clientWidth":"clientHeight"],g=a.animate({blockSize:d?["0",`${C}px`]:[`${C}px`,"0"]},t);g.oncancel=g.onfinish=g.onremove=()=>{n.dispatchEvent(new CustomEvent(d?"open-end":"close-end",{detail:n})),d||(n.open=!1),r=!1}},s=new MutationObserver(d=>{for(const C of d)if(C.type==="attributes"&&C.attributeName==="open"){if(r)return;n.open&&l(!0)}});s.observe(n,{attributes:!0});const c=d=>{d.preventDefault(),r||l(!n.open)},u=i3("Enter",c);return e.addEventListener("click",c),e.addEventListener("keypress",u),{destroy(){s.disconnect(),e.removeEventListener("click",c),e.removeEventListener("keypress",u)},update(d=Y1){t={...t,...d}}}},a0=n=>({}),R3=n=>({}),s0=n=>({}),B3=n=>({});function l0(n){let t,e,a,i,r,l;const s=n[11].summary,c=_1(s,n,n[10],R3),u=n[11].default,d=_1(u,n,n[10],null);let C=[n[7],{class:r="c-detail "+n[2]}],g={};for(let h=0;h<C.length;h+=1)g=b1(g,C[h]);return{c(){t=z("div"),e=z("div"),c&&c.c(),a=K(),i=z("div"),d&&d.c(),o(e,"class","c-detail__summary svelte-1q6x17o"),o(i,"class","c-detail__content svelte-1q6x17o"),D1(t,g),r1(t,"svelte-1q6x17o",!0)},m(h,p){A(h,t,p),f(t,e),c&&c.m(e,null),f(t,a),f(t,i),d&&d.m(i,null),l=!0},p(h,p){c&&c.p&&(!l||1024&p)&&$1(c,s,h,h[10],l?F1(s,h[10],p,a0):A1(h[10]),R3),d&&d.p&&(!l||1024&p)&&$1(d,u,h,h[10],l?F1(u,h[10],p,null):A1(h[10]),null),D1(t,g=v3(C,[128&p&&h[7],(!l||4&p&&r!==(r="c-detail "+h[2]))&&{class:r}])),r1(t,"svelte-1q6x17o",!0)},i(h){l||($(c,h),$(d,h),l=!0)},o(h){F(c,h),F(d,h),l=!1},d(h){h&&S(t),c&&c.d(h),d&&d.d(h)}}}function c0(n){let t,e,a,i,r,l,s,c,u,d,C;a=new K2({props:{icon:"chevron-down",class:"c-detail__chevron"}});const g=n[11].summary,h=_1(g,n,n[10],B3),p=n[11].default,L=_1(p,n,n[10],null);let w=[n[7],{style:s="--au-detail-duration: "+n[4].duration+"ms"},{class:c="c-detail "+n[2]}],x={};for(let y=0;y<w.length;y+=1)x=b1(x,w[y]);return{c(){t=z("details"),e=z("summary"),D(a.$$.fragment),i=K(),h&&h.c(),r=K(),l=z("div"),L&&L.c(),o(e,"class","c-detail__summary svelte-1q6x17o"),o(l,"class","c-detail__content svelte-1q6x17o"),D1(t,x),r1(t,"c-detail__rotate",n[3]),r1(t,"svelte-1q6x17o",!0)},m(y,v){A(y,t,v),f(t,e),T(a,e,null),f(e,i),h&&h.m(e,null),f(t,r),f(t,l),L&&L.m(l,null),t.open=n[0],u=!0,d||(C=[d1(t,"toggle",n[12]),T1(r0.call(null,t,n[4])),d1(t,"close-start",n[6]("close")),d1(t,"open-start",n[6]("open")),d1(t,"close-end",n[5]("close")),d1(t,"open-end",n[5]("open"))],d=!0)},p(y,v){h&&h.p&&(!u||1024&v)&&$1(h,g,y,y[10],u?F1(g,y[10],v,s0):A1(y[10]),B3),L&&L.p&&(!u||1024&v)&&$1(L,p,y,y[10],u?F1(p,y[10],v,null):A1(y[10]),null),D1(t,x=v3(w,[128&v&&y[7],{style:s},(!u||4&v&&c!==(c="c-detail "+y[2]))&&{class:c}])),1&v&&(t.open=y[0]),r1(t,"c-detail__rotate",y[3]),r1(t,"svelte-1q6x17o",!0)},i(y){u||($(a.$$.fragment,y),$(h,y),$(L,y),u=!0)},o(y){F(a.$$.fragment,y),F(h,y),F(L,y),u=!1},d(y){y&&S(t),I(a),h&&h.d(y),L&&L.d(y),d=!1,w2(C)}}}function u0(n){let t,e,a,i;const r=[c0,l0],l=[];function s(c,u){return c[1]?0:1}return t=s(n),e=l[t]=r[t](n),{c(){e.c(),a=v2()},m(c,u){l[t].m(c,u),A(c,a,u),i=!0},p(c,[u]){let d=t;t=s(c),t===d?l[t].p(c,u):(j1(),F(l[d],1,1,()=>{l[d]=null}),M1(),e=l[t],e?e.p(c,u):(e=l[t]=r[t](c),e.c()),$(e,1),e.m(a.parentNode,a))},i(c){i||($(e),i=!0)},o(c){F(e),i=!1},d(c){c&&S(a),l[t].d(c)}}}function d0(n,t,e){const a=["open","duration","expandable","onChangeOpen","class"];let i=A3(t,a),{$$slots:r={},$$scope:l}=t,{open:s=!0}=t,{duration:c=300}=t,{expandable:u=!0}=t,{onChangeOpen:d}=t,{class:C=""}=t;const g=typeof c=="number"?{duration:c}:c;let h=s;return n.$$set=p=>{t=b1(b1({},t),z2(p)),e(7,i=A3(t,a)),"open"in p&&e(0,s=p.open),"duration"in p&&e(8,c=p.duration),"expandable"in p&&e(1,u=p.expandable),"onChangeOpen"in p&&e(9,d=p.onChangeOpen),"class"in p&&e(2,C=p.class),"$$scope"in p&&e(10,l=p.$$scope)},n.$$.update=()=>{1&n.$$.dirty&&e(3,h=s)},[s,u,C,h,g,p=>()=>{e(3,h=p!=="close"),d==null||d(p==="open")},p=>()=>{e(3,h=p==="open")},i,c,d,l,r,function(){s=this.open,e(0,s)}]}class C0 extends O{constructor(t){super(),R(this,t,d0,u0,B,{open:0,duration:8,expandable:1,onChangeOpen:9,class:2})}}function h0(n){let t,e,a,i,r;return e=new J2({props:{filepath:n[0].relPath,onCodeAction:m1,value:n[3]}}),i=new k2({props:{actions:n[2],onAction:n[1],value:n[0]}}),{c(){t=z("div"),D(e.$$.fragment),a=K(),D(i.$$.fragment),o(t,"class","c-code-roll-item-header svelte-3zqetr")},m(l,s){A(l,t,s),T(e,t,null),f(t,a),T(i,t,null),r=!0},p(l,[s]){const c={};1&s&&(c.filepath=l[0].relPath),8&s&&(c.value=l[3]),e.$set(c);const u={};4&s&&(u.actions=l[2]),2&s&&(u.onAction=l[1]),1&s&&(u.value=l[0]),i.$set(u)},i(l){r||($(e.$$.fragment,l),$(i.$$.fragment,l),r=!0)},o(l){F(e.$$.fragment,l),F(i.$$.fragment,l),r=!1},d(l){l&&S(t),I(e),I(i)}}}function g0(n,t,e){let{filepath:a}=t,{onFileAction:i=m1}=t,{fileActions:r=[]}=t,{suggestions:l}=t;return n.$$set=s=>{"filepath"in s&&e(0,a=s.filepath),"onFileAction"in s&&e(1,i=s.onFileAction),"fileActions"in s&&e(2,r=s.fileActions),"suggestions"in s&&e(3,l=s.suggestions)},[a,i,r,l]}class f0 extends O{constructor(t){super(),R(this,t,g0,h0,B,{filepath:0,onFileAction:1,fileActions:2,suggestions:3})}}function N3(n,t,e,a){const i=n.split(""),r=t.toSorted(({lineRange:{start:l}},{lineRange:{start:s}})=>s-l);for(const l of r){const s=[l.result.charStart,e(l)],c=a(l);c&&s.push(...c.split("")),i.splice(...s)}return i.join("")}const z3=((n=0)=>()=>Date.now()+"-"+n++)(),p0=(n,t,e,a,i,r)=>{var u,d,C,g;if(!n||!e)return[];e=function(h,p){return N3(h,p,L=>L.result.suggestedCode.split("").length,L=>L.result.existingCode)}(e,t.filter(h=>h.state===r3.accepted));const l=function(h,p){return N3(h,p,L=>L.result.charEnd-L.result.charStart,L=>L.result.suggestedCode)}(e,t);(d=(u=n.getModel())==null?void 0:u.original)==null||d.dispose(),(g=(C=n.getModel())==null?void 0:C.modified)==null||g.dispose();const s=r.editor.createModel(e,a,r.Uri.parse("file://"+i+`#${z3()}`)),c=r.editor.createModel(l,a,r.Uri.parse("file://"+i+`#${z3()}`));return n.setModel({original:s,modified:c}),[s,c]};function m0(n){var t;return`${n.requestId}#${(t=n.result)==null?void 0:t.suggestionId}`}function P3(n){return n.map(m0).join(":")}function Z3(n){const t=n.toSorted((i,r)=>i.start-r.start),e=[];let a=t.shift();for(;t.length;){const i=t.shift();i.start<=a.end+1?a.end=Math.max(a.end,i.end):(e.push(a),a=i)}return e.push(a),e}function D3(n){return n.reduce((t,e)=>t+=e.end-e.start+1,0)}function T3(n){return n.reduce((t,e,a)=>a===0?t:t+=e.start-n[a-1].end-1,0)}function a3(n){var a;let t,e;if(n.modifiedEndLineNumber===0)e=n.originalEndLineNumber-n.originalStartLineNumber+1,t=n.originalStartLineNumber-1;else if((a=n.charChanges)!=null&&a.length){const i=Z3(n.charChanges.map(d=>({start:d.originalStartLineNumber,end:d.originalEndLineNumber}))),r=Z3(n.charChanges.map(d=>({start:d.modifiedStartLineNumber,end:d.modifiedEndLineNumber}))),l=D3(i),s=D3(r),c=T3(i),u=T3(r);e=l+s+Math.max(c,u),t=n.modifiedStartLineNumber-1}else{if(n.originalEndLineNumber!==0)throw new Error("Unexpected line change");e=n.modifiedEndLineNumber-n.modifiedStartLineNumber+1,t=n.modifiedStartLineNumber-1}return{lineCount:e,afterLineNumber:t}}function v0(...n){return n.reduce((t,e)=>({...w0(t,e),charChanges:[...t.charChanges??[],...e.charChanges??[]]}))}function w0(...n){return n.reduce((t,e)=>({originalStartLineNumber:Math.min(t.originalStartLineNumber,e.originalStartLineNumber),originalEndLineNumber:Math.max(t.originalEndLineNumber,e.originalEndLineNumber),modifiedStartLineNumber:Math.min(t.modifiedStartLineNumber,e.modifiedStartLineNumber),modifiedEndLineNumber:Math.max(t.modifiedEndLineNumber,e.modifiedEndLineNumber)}))}function L0(n,t){if(t.originalStartLineNumber===n.lineRange.start+1)return!0;const e=Math.min(t.originalStartLineNumber,t.modifiedStartLineNumber),a=Math.max(t.originalEndLineNumber,t.modifiedEndLineNumber);return e>=n.lineRange.start&&e<=n.lineRange.stop||a>=n.lineRange.start&&a<=n.lineRange.stop||e<=n.lineRange.start&&a>=n.lineRange.stop}function s3(n,t){const e=new Map,a=n.toSorted(({lineRange:{start:i}},{lineRange:{start:r}})=>i-r);t:for(const i of t.toSorted(({modifiedStartLineNumber:r,originalStartLineNumber:l},{modifiedStartLineNumber:s,originalStartLineNumber:c})=>r-s||l-c))for(const r of a)if(L0(r,i)){const l=e.get(r);e.set(r,l?v0(l,i):i);continue t}return e}function y0(n,t){let e,a,i=t;const r=()=>i.editor.getModifiedEditor(),l=()=>{const{afterLineNumber:s}=i,c=r();if(s===void 0)return void c.changeViewZones(d=>{e&&c&&a&&d.removeZone(a)});const u={...i,afterLineNumber:s,domNode:n,suppressMouseDown:!0};c==null||c.changeViewZones(d=>{e&&a&&d.removeZone(a),a=d.addZone(u),e=u})};return l(),{update:s=>{i=s,l()},destroy:()=>{const s=r();s.changeViewZones(c=>{if(e&&s&&a)try{c.removeZone(a)}catch(u){if(u instanceof Error){if(u.message.includes("Cannot read properties of null (reading 'removeChild')"))return}else console.warn(`Failed to remove view zone: ${u}`)}})}}}const b2=Symbol("code-roll-selection-context");function k0(){let n=D2(b2);return n||(n=x0({})),n}function x0(n){return P2(b2,Z2(n))}function I3(n){return n.activeSuggestion??n.selectedSuggestion??n.nextSuggestion}function D6(n){return n.activeSuggestion?"active":n.selectedSuggestion?"select":"next"}const t3={scrollContainer:document.documentElement,scrollIntoView:{behavior:"smooth",block:"nearest"},scrollDelayMS:100,useSmartBlockAlignment:!0,doScroll:!0};function b0(n,t=t3){let e,a=Object.assign({},t3,t);function i(r){let{doScroll:l,scrollIntoView:s,scrollDelayMS:c,useSmartBlockAlignment:u,scrollContainer:d}=Object.assign({},a,r);l&&(u&&n.getBoundingClientRect().height>((d==null?void 0:d.getBoundingClientRect().height)??1/0)&&(s=Object.assign({},s,{block:"start"})),e=setTimeout(()=>{const C=n.getBoundingClientRect();if(C.bottom===0&&C.top===0&&C.height===0&&C.width===0)return;const g=function(h,p,L){const w=h.getBoundingClientRect(),x=p.getBoundingClientRect();if(L==="nearest")if(w.bottom>x.bottom)L="end";else{if(!(w.top<x.top))return p.scrollTop;L="start"}return w.height>x.height||L==="start"?p.scrollTop+w.top:L==="end"?p.scrollTop+w.bottom-x.height:p.scrollTop+w.top-(x.height-w.height)/2}(n,d,(s==null?void 0:s.block)??t3.scrollIntoView.block);d.scrollTo({top:g,behavior:s==null?void 0:s.behavior})},c))}return i(a),{update:i,destroy(){clearTimeout(e)}}}function l3(n,t,e){return t.activeSuggestion?x1(t.activeSuggestion,n)?e?"select":"active":"none":t.selectedSuggestion?x1(t.selectedSuggestion,n)?e?"active":"select":"none":t.nextSuggestion&&x1(t.nextSuggestion,n)?"next":"none"}function q3(n){return["",`--augment-code-roll-selection-background: var(--augment-code-roll-item-background-${n})`,`--augment-code-roll-selection-color: var(--augment-code-roll-item-color-${n})`,"--augment-code-roll-selection-border: var(--augment-code-roll-selection-background)",""].join(";")}const Q=Symbol.for("@ts-pattern/matcher"),_0=Symbol.for("@ts-pattern/isVariadic"),q1="@ts-pattern/anonymous-select-key",c3=n=>!!(n&&typeof n=="object"),P1=n=>n&&!!n[Q],H=(n,t,e)=>{if(P1(n)){const a=n[Q](),{matched:i,selections:r}=a.match(t);return i&&r&&Object.keys(r).forEach(l=>e(l,r[l])),i}if(c3(n)){if(!c3(t))return!1;if(Array.isArray(n)){if(!Array.isArray(t))return!1;let a=[],i=[],r=[];for(const l of n.keys()){const s=n[l];P1(s)&&s[_0]?r.push(s):r.length?i.push(s):a.push(s)}if(r.length){if(r.length>1)throw new Error("Pattern error: Using `...P.array(...)` several times in a single pattern is not allowed.");if(t.length<a.length+i.length)return!1;const l=t.slice(0,a.length),s=i.length===0?[]:t.slice(-i.length),c=t.slice(a.length,i.length===0?1/0:-i.length);return a.every((u,d)=>H(u,l[d],e))&&i.every((u,d)=>H(u,s[d],e))&&(r.length===0||H(r[0],c,e))}return n.length===t.length&&n.every((l,s)=>H(l,t[s],e))}return Reflect.ownKeys(n).every(a=>{const i=n[a];return(a in t||P1(r=i)&&r[Q]().matcherType==="optional")&&H(i,t[a],e);var r})}return Object.is(t,n)},a1=n=>{var t,e,a;return c3(n)?P1(n)?(t=(e=(a=n[Q]()).getSelectionKeys)==null?void 0:e.call(a))!=null?t:[]:Array.isArray(n)?S1(n,a1):S1(Object.values(n),a1):[]},S1=(n,t)=>n.reduce((e,a)=>e.concat(t(a)),[]);function V(n){return Object.assign(n,{optional:()=>$0(n),and:t=>E(n,t),or:t=>A0(n,t),select:t=>t===void 0?V3(n):V3(t,n)})}function $0(n){return V({[Q]:()=>({match:t=>{let e={};const a=(i,r)=>{e[i]=r};return t===void 0?(a1(n).forEach(i=>a(i,void 0)),{matched:!0,selections:e}):{matched:H(n,t,a),selections:e}},getSelectionKeys:()=>a1(n),matcherType:"optional"})})}function E(...n){return V({[Q]:()=>({match:t=>{let e={};const a=(i,r)=>{e[i]=r};return{matched:n.every(i=>H(i,t,a)),selections:e}},getSelectionKeys:()=>S1(n,a1),matcherType:"and"})})}function A0(...n){return V({[Q]:()=>({match:t=>{let e={};const a=(i,r)=>{e[i]=r};return S1(n,a1).forEach(i=>a(i,void 0)),{matched:n.some(i=>H(i,t,a)),selections:e}},getSelectionKeys:()=>S1(n,a1),matcherType:"or"})})}function b(n){return{[Q]:()=>({match:t=>({matched:!!n(t)})})}}function V3(...n){const t=typeof n[0]=="string"?n[0]:void 0,e=n.length===2?n[1]:typeof n[0]=="string"?void 0:n[0];return V({[Q]:()=>({match:a=>{let i={[t??q1]:a};return{matched:e===void 0||H(e,a,(r,l)=>{i[r]=l}),selections:i}},getSelectionKeys:()=>[t??q1].concat(e===void 0?[]:a1(e))})})}function U(n){return typeof n=="number"}function n1(n){return typeof n=="string"}function e1(n){return typeof n=="bigint"}V(b(function(n){return!0}));const o1=n=>Object.assign(V(n),{startsWith:t=>{return o1(E(n,(e=t,b(a=>n1(a)&&a.startsWith(e)))));var e},endsWith:t=>{return o1(E(n,(e=t,b(a=>n1(a)&&a.endsWith(e)))));var e},minLength:t=>o1(E(n,(e=>b(a=>n1(a)&&a.length>=e))(t))),length:t=>o1(E(n,(e=>b(a=>n1(a)&&a.length===e))(t))),maxLength:t=>o1(E(n,(e=>b(a=>n1(a)&&a.length<=e))(t))),includes:t=>{return o1(E(n,(e=t,b(a=>n1(a)&&a.includes(e)))));var e},regex:t=>{return o1(E(n,(e=t,b(a=>n1(a)&&!!a.match(e)))));var e}});o1(b(n1));const W=n=>Object.assign(V(n),{between:(t,e)=>W(E(n,((a,i)=>b(r=>U(r)&&a<=r&&i>=r))(t,e))),lt:t=>W(E(n,(e=>b(a=>U(a)&&a<e))(t))),gt:t=>W(E(n,(e=>b(a=>U(a)&&a>e))(t))),lte:t=>W(E(n,(e=>b(a=>U(a)&&a<=e))(t))),gte:t=>W(E(n,(e=>b(a=>U(a)&&a>=e))(t))),int:()=>W(E(n,b(t=>U(t)&&Number.isInteger(t)))),finite:()=>W(E(n,b(t=>U(t)&&Number.isFinite(t)))),positive:()=>W(E(n,b(t=>U(t)&&t>0))),negative:()=>W(E(n,b(t=>U(t)&&t<0)))});W(b(U));const i1=n=>Object.assign(V(n),{between:(t,e)=>i1(E(n,((a,i)=>b(r=>e1(r)&&a<=r&&i>=r))(t,e))),lt:t=>i1(E(n,(e=>b(a=>e1(a)&&a<e))(t))),gt:t=>i1(E(n,(e=>b(a=>e1(a)&&a>e))(t))),lte:t=>i1(E(n,(e=>b(a=>e1(a)&&a<=e))(t))),gte:t=>i1(E(n,(e=>b(a=>e1(a)&&a>=e))(t))),positive:()=>i1(E(n,b(t=>e1(t)&&t>0))),negative:()=>i1(E(n,b(t=>e1(t)&&t<0)))});i1(b(e1)),V(b(function(n){return typeof n=="boolean"})),V(b(function(n){return typeof n=="symbol"})),V(b(function(n){return n==null})),V(b(function(n){return n!=null}));class F0 extends Error{constructor(t){let e;try{e=JSON.stringify(t)}catch{e=t}super(`Pattern matching error: no pattern matches value ${e}`),this.input=void 0,this.input=t}}const u3={matched:!1,value:void 0};class V1{constructor(t,e){this.input=void 0,this.state=void 0,this.input=t,this.state=e}with(...t){if(this.state.matched)return this;const e=t[t.length-1],a=[t[0]];let i;t.length===3&&typeof t[1]=="function"?i=t[1]:t.length>2&&a.push(...t.slice(1,t.length-1));let r=!1,l={};const s=(u,d)=>{r=!0,l[u]=d},c=!a.some(u=>H(u,this.input,s))||i&&!i(this.input)?u3:{matched:!0,value:e(r?q1 in l?l[q1]:l:this.input,this.input)};return new V1(this.input,c)}when(t,e){if(this.state.matched)return this;const a=!!t(this.input);return new V1(this.input,a?{matched:!0,value:e(this.input,this.input)}:u3)}otherwise(t){return this.state.matched?this.state.value:t(this.input)}exhaustive(){if(this.state.matched)return this.state.value;throw new F0(this.input)}run(){return this.exhaustive()}returnType(){return this}}function S0(n){let t,e,a,i,r,l,s,c,u,d,C,g;return{c(){t=m("svg"),e=m("title"),a=Z("nextedit_addition_light"),i=m("g"),r=m("g"),l=m("path"),s=m("rect"),u=m("g"),d=m("rect"),C=m("rect"),o(l,"d","M3.07557525,3.27946831 C3.10738379,3.27258798 3.13664209,3.26682472 3.16597818,3.26160513 C3.19407786,3.25661079 3.22181021,3.25217747 3.24959807,3.24822758 C3.3431507,3.23490837 3.43787348,3.22705558 3.53270619,3.22474499 C3.54619312,3.22441336 3.56021661,3.22418981 3.57424082,3.22408741 L3.59202055,3.22402251 C3.61600759,3.22402251 3.63999463,3.22437692 3.66397314,3.22508575 C3.69176119,3.22590043 3.72012236,3.22722855 3.74845755,3.22905289 C3.77692744,3.23089046 3.80498198,3.23319023 3.83299719,3.23597733 C3.86236278,3.23889105 3.89230728,3.24242516 3.92218997,3.24651769 C3.95842477,3.25149198 3.99379267,3.25714552 4.02904516,3.2635852 C4.04457753,3.26641925 4.06056799,3.26950351 4.07653203,3.27274998 C4.1217801,3.28195855 4.16647313,3.29238022 4.21089814,3.30408537 C4.22093231,3.3067264 4.23153789,3.30959531 4.24212737,3.31253756 C4.27196202,3.32083528 4.30106886,3.32952376 4.33003598,3.33877116 C4.35855924,3.347869 4.38751122,3.35771229 4.41630528,3.3681193 C4.42116985,3.36987869 4.42551008,3.37146263 4.42984665,3.3730594 C4.4761162,3.39008583 4.52241276,3.4087674 4.56821184,3.42893807 C4.59406406,3.44033198 4.61917606,3.45191971 4.64412424,3.46396063 C4.67111495,3.47697976 4.69839649,3.4907848 4.72546291,3.50513959 C4.75890801,3.52288219 4.79178851,3.54132453 4.82431475,3.56059431 C4.8374698,3.56838641 4.85073285,3.5764165 4.86393439,3.58458539 C4.89491851,3.60376145 4.92539479,3.6235868 4.95550936,3.64416832 C4.9772823,3.65904443 4.99913454,3.67451232 5.02078256,3.69038541 C5.03998798,3.70447076 5.05881967,3.71870909 5.07748715,3.73325923 C5.10440445,3.75423289 5.13126725,3.7760983 5.15775949,3.79862613 C5.1821715,3.81939236 5.20595148,3.84042939 5.22940861,3.86201411 C5.24512436,3.87647694 5.26059993,3.89109333 5.27592752,3.90595256 C5.28442786,3.91418351 5.29385225,3.92345739 5.30321896,3.9328241 L10.2031018,8.83270693 C10.255475,8.88508012 10.3065885,8.93859789 10.3564099,8.99321224 L10.2031018,8.83270693 C10.2748395,8.90444467 10.344214,8.97832987 10.4111413,9.05423915 C10.4223877,9.06699478 10.4335715,9.07981507 10.4446856,9.092692 C10.7663645,9.46539004 11.0297601,9.88553066 11.2252237,10.3388957 L11.6780206,11.3880225 L12.548286,13.4076516 C12.7467158,13.8678966 12.5344727,14.4018581 12.0742277,14.6002879 C11.9977866,14.6332447 11.9179446,14.6552159 11.836969,14.6662015 L11.7149387,14.6744406 C11.592625,14.6744406 11.4703113,14.6497231 11.3556497,14.6002879 L11.2340206,14.5480225 L9.33602055,13.7300225 L8.28689372,13.2772256 C7.83352871,13.081762 7.41338809,12.8183665 7.04069004,12.4966876 L7.0022372,12.4631433 C6.98177889,12.4451057 6.9614676,12.4268903 6.94130575,12.4084989 L7.04069004,12.4966876 C6.95122931,12.4194733 6.86450207,12.3389008 6.78070498,12.2551038 L1.88082214,7.35522092 C0.935753358,6.41015213 0.935753358,4.87789288 1.88082214,3.9328241 L1.90902055,3.90502251 L2.01192506,3.8109306 C2.19120357,3.65606766 2.38780913,3.5318516 2.59488381,3.4382824 C2.62872186,3.42311621 2.65522016,3.41182111 2.68187195,3.40102033 C2.76025666,3.36925866 2.83986347,3.34180278 2.92043821,3.31861145 L3.07557525,3.27946831 Z M9.58610551,9.95149698 L7.89951995,11.6381324 C8.10279642,11.805046 8.32371441,11.9494547 8.55841217,12.068738 L8.76594574,12.166096 L10.2570206,12.8090225 L10.7570206,12.3090225 L10.114094,10.8179477 C9.97930356,10.5053101 9.80144069,10.2137385 9.58610551,9.95149698 Z"),o(l,"id","Combined-Shape"),o(l,"fill-rule","nonzero"),o(s,"id","Rectangle"),o(s,"opacity","0.005"),o(s,"x","0"),o(s,"y","0"),o(s,"width","16"),o(s,"height","16"),o(s,"rx","2"),o(r,"id","Pencil_Base"),o(r,"fill",c=n[0]?n[1]:"#0A84FF"),o(d,"id","Rectangle"),o(d,"x","2.25"),o(d,"y","0"),o(d,"width","1.5"),o(d,"height","6"),o(d,"rx","0.75"),o(C,"id","Rectangle-Copy"),o(C,"transform","translate(3, 3) rotate(90) translate(-3, -3)"),o(C,"x","2.25"),o(C,"y","1.13686838e-13"),o(C,"width","1.5"),o(C,"height","6"),o(C,"rx","0.75"),o(u,"id","Group"),o(u,"transform","translate(9, 1)"),o(u,"fill",g=n[0]?n[1]:"#34C759"),o(i,"id","nextedit_addition_light"),o(i,"stroke","none"),o(i,"stroke-width","1"),o(i,"fill","none"),o(i,"fill-rule","evenodd"),o(t,"width","16"),o(t,"height","16"),o(t,"viewBox","0 0 16 16"),o(t,"version","1.1"),o(t,"xmlns","http://www.w3.org/2000/svg"),o(t,"xmlns:xlink","http://www.w3.org/1999/xlink")},m(h,p){A(h,t,p),f(t,e),f(e,a),f(t,i),f(i,r),f(r,l),f(r,s),f(i,u),f(u,d),f(u,C)},p(h,[p]){3&p&&c!==(c=h[0]?h[1]:"#0A84FF")&&o(r,"fill",c),3&p&&g!==(g=h[0]?h[1]:"#34C759")&&o(u,"fill",g)},i:_,o:_,d(h){h&&S(t)}}}function j0(n,t,e){let{mask:a=!1}=t,{maskColor:i="white"}=t;return n.$$set=r=>{"mask"in r&&e(0,a=r.mask),"maskColor"in r&&e(1,i=r.maskColor)},[a,i]}class M0 extends O{constructor(t){super(),R(this,t,j0,S0,B,{mask:0,maskColor:1})}}function E0(n){let t,e,a,i,r,l,s,c,u,d,C,g;return{c(){t=m("svg"),e=m("title"),a=Z("nextedit_addition_dark"),i=m("g"),r=m("g"),l=m("path"),s=m("rect"),u=m("g"),d=m("rect"),C=m("rect"),o(l,"d","M3.07557525,3.27946831 C3.10738379,3.27258798 3.13664209,3.26682472 3.16597818,3.26160513 C3.19407786,3.25661079 3.22181021,3.25217747 3.24959807,3.24822758 C3.3431507,3.23490837 3.43787348,3.22705558 3.53270619,3.22474499 C3.54619312,3.22441336 3.56021661,3.22418981 3.57424082,3.22408741 L3.59202055,3.22402251 C3.61600759,3.22402251 3.63999463,3.22437692 3.66397314,3.22508575 C3.69176119,3.22590043 3.72012236,3.22722855 3.74845755,3.22905289 C3.77692744,3.23089046 3.80498198,3.23319023 3.83299719,3.23597733 C3.86236278,3.23889105 3.89230728,3.24242516 3.92218997,3.24651769 C3.95842477,3.25149198 3.99379267,3.25714552 4.02904516,3.2635852 C4.04457753,3.26641925 4.06056799,3.26950351 4.07653203,3.27274998 C4.1217801,3.28195855 4.16647313,3.29238022 4.21089814,3.30408537 C4.22093231,3.3067264 4.23153789,3.30959531 4.24212737,3.31253756 C4.27196202,3.32083528 4.30106886,3.32952376 4.33003598,3.33877116 C4.35855924,3.347869 4.38751122,3.35771229 4.41630528,3.3681193 C4.42116985,3.36987869 4.42551008,3.37146263 4.42984665,3.3730594 C4.4761162,3.39008583 4.52241276,3.4087674 4.56821184,3.42893807 C4.59406406,3.44033198 4.61917606,3.45191971 4.64412424,3.46396063 C4.67111495,3.47697976 4.69839649,3.4907848 4.72546291,3.50513959 C4.75890801,3.52288219 4.79178851,3.54132453 4.82431475,3.56059431 C4.8374698,3.56838641 4.85073285,3.5764165 4.86393439,3.58458539 C4.89491851,3.60376145 4.92539479,3.6235868 4.95550936,3.64416832 C4.9772823,3.65904443 4.99913454,3.67451232 5.02078256,3.69038541 C5.03998798,3.70447076 5.05881967,3.71870909 5.07748715,3.73325923 C5.10440445,3.75423289 5.13126725,3.7760983 5.15775949,3.79862613 C5.1821715,3.81939236 5.20595148,3.84042939 5.22940861,3.86201411 C5.24512436,3.87647694 5.26059993,3.89109333 5.27592752,3.90595256 C5.28442786,3.91418351 5.29385225,3.92345739 5.30321896,3.9328241 L10.2031018,8.83270693 C10.255475,8.88508012 10.3065885,8.93859789 10.3564099,8.99321224 L10.2031018,8.83270693 C10.2748395,8.90444467 10.344214,8.97832987 10.4111413,9.05423915 C10.4223877,9.06699478 10.4335715,9.07981507 10.4446856,9.092692 C10.7663645,9.46539004 11.0297601,9.88553066 11.2252237,10.3388957 L11.6780206,11.3880225 L12.548286,13.4076516 C12.7467158,13.8678966 12.5344727,14.4018581 12.0742277,14.6002879 C11.9977866,14.6332447 11.9179446,14.6552159 11.836969,14.6662015 L11.7149387,14.6744406 C11.592625,14.6744406 11.4703113,14.6497231 11.3556497,14.6002879 L11.2340206,14.5480225 L9.33602055,13.7300225 L8.28689372,13.2772256 C7.83352871,13.081762 7.41338809,12.8183665 7.04069004,12.4966876 L7.0022372,12.4631433 C6.98177889,12.4451057 6.9614676,12.4268903 6.94130575,12.4084989 L7.04069004,12.4966876 C6.95122931,12.4194733 6.86450207,12.3389008 6.78070498,12.2551038 L1.88082214,7.35522092 C0.935753358,6.41015213 0.935753358,4.87789288 1.88082214,3.9328241 L1.90902055,3.90502251 L2.01192506,3.8109306 C2.19120357,3.65606766 2.38780913,3.5318516 2.59488381,3.4382824 C2.62872186,3.42311621 2.65522016,3.41182111 2.68187195,3.40102033 C2.76025666,3.36925866 2.83986347,3.34180278 2.92043821,3.31861145 L3.07557525,3.27946831 Z M9.58610551,9.95149698 L7.89951995,11.6381324 C8.10279642,11.805046 8.32371441,11.9494547 8.55841217,12.068738 L8.76594574,12.166096 L10.2570206,12.8090225 L10.7570206,12.3090225 L10.114094,10.8179477 C9.97930356,10.5053101 9.80144069,10.2137385 9.58610551,9.95149698 Z"),o(l,"id","Combined-Shape"),o(l,"fill-rule","nonzero"),o(s,"id","Rectangle"),o(s,"opacity","0.005"),o(s,"x","0"),o(s,"y","0"),o(s,"width","16"),o(s,"height","16"),o(s,"rx","2"),o(r,"id","Pencil_Base"),o(r,"fill",c=n[0]?n[1]:"#168AFF"),o(d,"id","Rectangle"),o(d,"x","2.25"),o(d,"y","0"),o(d,"width","1.5"),o(d,"height","6"),o(d,"rx","0.75"),o(C,"id","Rectangle-Copy"),o(C,"transform","translate(3, 3) rotate(90) translate(-3, -3)"),o(C,"x","2.25"),o(C,"y","1.13686838e-13"),o(C,"width","1.5"),o(C,"height","6"),o(C,"rx","0.75"),o(u,"id","Group"),o(u,"transform","translate(9, 1)"),o(u,"fill",g=n[0]?n[1]:"#30D158"),o(i,"id","nextedit_addition_dark"),o(i,"stroke","none"),o(i,"stroke-width","1"),o(i,"fill","none"),o(i,"fill-rule","evenodd"),o(t,"width","16"),o(t,"height","16"),o(t,"viewBox","0 0 16 16"),o(t,"version","1.1"),o(t,"xmlns","http://www.w3.org/2000/svg"),o(t,"xmlns:xlink","http://www.w3.org/1999/xlink")},m(h,p){A(h,t,p),f(t,e),f(e,a),f(t,i),f(i,r),f(r,l),f(r,s),f(i,u),f(u,d),f(u,C)},p(h,[p]){3&p&&c!==(c=h[0]?h[1]:"#168AFF")&&o(r,"fill",c),3&p&&g!==(g=h[0]?h[1]:"#30D158")&&o(u,"fill",g)},i:_,o:_,d(h){h&&S(t)}}}function O0(n,t,e){let{mask:a=!1}=t,{maskColor:i="white"}=t;return n.$$set=r=>{"mask"in r&&e(0,a=r.mask),"maskColor"in r&&e(1,i=r.maskColor)},[a,i]}class R0 extends O{constructor(t){super(),R(this,t,O0,E0,B,{mask:0,maskColor:1})}}function B0(n){let t,e,a,i,r,l,s,c,u,d,C;return{c(){t=m("svg"),e=m("title"),a=Z("nextedit_deletion_light"),i=m("g"),r=m("g"),l=m("path"),s=m("rect"),u=m("g"),d=m("rect"),o(l,"d","M3.07557525,3.27946831 C3.10738379,3.27258798 3.13664209,3.26682472 3.16597818,3.26160513 C3.19407786,3.25661079 3.22181021,3.25217747 3.24959807,3.24822758 C3.3431507,3.23490837 3.43787348,3.22705558 3.53270619,3.22474499 C3.54619312,3.22441336 3.56021661,3.22418981 3.57424082,3.22408741 L3.59202055,3.22402251 C3.61600759,3.22402251 3.63999463,3.22437692 3.66397314,3.22508575 C3.69176119,3.22590043 3.72012236,3.22722855 3.74845755,3.22905289 C3.77692744,3.23089046 3.80498198,3.23319023 3.83299719,3.23597733 C3.86236278,3.23889105 3.89230728,3.24242516 3.92218997,3.24651769 C3.95842477,3.25149198 3.99379267,3.25714552 4.02904516,3.2635852 C4.04457753,3.26641925 4.06056799,3.26950351 4.07653203,3.27274998 C4.1217801,3.28195855 4.16647313,3.29238022 4.21089814,3.30408537 C4.22093231,3.3067264 4.23153789,3.30959531 4.24212737,3.31253756 C4.27196202,3.32083528 4.30106886,3.32952376 4.33003598,3.33877116 C4.35855924,3.347869 4.38751122,3.35771229 4.41630528,3.3681193 C4.42116985,3.36987869 4.42551008,3.37146263 4.42984665,3.3730594 C4.4761162,3.39008583 4.52241276,3.4087674 4.56821184,3.42893807 C4.59406406,3.44033198 4.61917606,3.45191971 4.64412424,3.46396063 C4.67111495,3.47697976 4.69839649,3.4907848 4.72546291,3.50513959 C4.75890801,3.52288219 4.79178851,3.54132453 4.82431475,3.56059431 C4.8374698,3.56838641 4.85073285,3.5764165 4.86393439,3.58458539 C4.89491851,3.60376145 4.92539479,3.6235868 4.95550936,3.64416832 C4.9772823,3.65904443 4.99913454,3.67451232 5.02078256,3.69038541 C5.03998798,3.70447076 5.05881967,3.71870909 5.07748715,3.73325923 C5.10440445,3.75423289 5.13126725,3.7760983 5.15775949,3.79862613 C5.1821715,3.81939236 5.20595148,3.84042939 5.22940861,3.86201411 C5.24512436,3.87647694 5.26059993,3.89109333 5.27592752,3.90595256 C5.28442786,3.91418351 5.29385225,3.92345739 5.30321896,3.9328241 L10.2031018,8.83270693 C10.255475,8.88508012 10.3065885,8.93859789 10.3564099,8.99321224 L10.2031018,8.83270693 C10.2748395,8.90444467 10.344214,8.97832987 10.4111413,9.05423915 C10.4223877,9.06699478 10.4335715,9.07981507 10.4446856,9.092692 C10.7663645,9.46539004 11.0297601,9.88553066 11.2252237,10.3388957 L11.6780206,11.3880225 L12.548286,13.4076516 C12.7467158,13.8678966 12.5344727,14.4018581 12.0742277,14.6002879 C11.9977866,14.6332447 11.9179446,14.6552159 11.836969,14.6662015 L11.7149387,14.6744406 C11.592625,14.6744406 11.4703113,14.6497231 11.3556497,14.6002879 L11.2340206,14.5480225 L9.33602055,13.7300225 L8.28689372,13.2772256 C7.83352871,13.081762 7.41338809,12.8183665 7.04069004,12.4966876 L7.0022372,12.4631433 C6.98177889,12.4451057 6.9614676,12.4268903 6.94130575,12.4084989 L7.04069004,12.4966876 C6.95122931,12.4194733 6.86450207,12.3389008 6.78070498,12.2551038 L1.88082214,7.35522092 C0.935753358,6.41015213 0.935753358,4.87789288 1.88082214,3.9328241 L1.90902055,3.90502251 L2.01192506,3.8109306 C2.19120357,3.65606766 2.38780913,3.5318516 2.59488381,3.4382824 C2.62872186,3.42311621 2.65522016,3.41182111 2.68187195,3.40102033 C2.76025666,3.36925866 2.83986347,3.34180278 2.92043821,3.31861145 L3.07557525,3.27946831 Z M9.58610551,9.95149698 L7.89951995,11.6381324 C8.10279642,11.805046 8.32371441,11.9494547 8.55841217,12.068738 L8.76594574,12.166096 L10.2570206,12.8090225 L10.7570206,12.3090225 L10.114094,10.8179477 C9.97930356,10.5053101 9.80144069,10.2137385 9.58610551,9.95149698 Z"),o(l,"id","Combined-Shape"),o(l,"fill-rule","nonzero"),o(s,"id","Rectangle"),o(s,"opacity","0.005"),o(s,"x","0"),o(s,"y","0"),o(s,"width","16"),o(s,"height","16"),o(s,"rx","2"),o(r,"id","Pencil_Base"),o(r,"fill",c=n[0]?n[1]:"#0A84FF"),o(d,"id","Rectangle-Copy"),o(d,"transform","translate(3, 0.75) rotate(90) translate(-3, -0.75)"),o(d,"x","2.25"),o(d,"y","-2.25"),o(d,"width","1.5"),o(d,"height","6"),o(d,"rx","0.75"),o(u,"id","Group"),o(u,"transform","translate(9, 3.25)"),o(u,"fill",C=n[0]?n[1]:"#FF5D4E"),o(i,"id","nextedit_deletion_light"),o(i,"stroke","none"),o(i,"stroke-width","1"),o(i,"fill","none"),o(i,"fill-rule","evenodd"),o(t,"width","16"),o(t,"height","16"),o(t,"viewBox","0 0 16 16"),o(t,"version","1.1"),o(t,"xmlns","http://www.w3.org/2000/svg"),o(t,"xmlns:xlink","http://www.w3.org/1999/xlink")},m(g,h){A(g,t,h),f(t,e),f(e,a),f(t,i),f(i,r),f(r,l),f(r,s),f(i,u),f(u,d)},p(g,[h]){3&h&&c!==(c=g[0]?g[1]:"#0A84FF")&&o(r,"fill",c),3&h&&C!==(C=g[0]?g[1]:"#FF5D4E")&&o(u,"fill",C)},i:_,o:_,d(g){g&&S(t)}}}function N0(n,t,e){let{mask:a=!1}=t,{maskColor:i="white"}=t;return n.$$set=r=>{"mask"in r&&e(0,a=r.mask),"maskColor"in r&&e(1,i=r.maskColor)},[a,i]}class z0 extends O{constructor(t){super(),R(this,t,N0,B0,B,{mask:0,maskColor:1})}}function P0(n){let t,e,a,i,r,l,s,c,u,d,C;return{c(){t=m("svg"),e=m("title"),a=Z("nextedit_deletion_dark"),i=m("g"),r=m("g"),l=m("path"),s=m("rect"),u=m("g"),d=m("rect"),o(l,"d","M3.07557525,3.27946831 C3.10738379,3.27258798 3.13664209,3.26682472 3.16597818,3.26160513 C3.19407786,3.25661079 3.22181021,3.25217747 3.24959807,3.24822758 C3.3431507,3.23490837 3.43787348,3.22705558 3.53270619,3.22474499 C3.54619312,3.22441336 3.56021661,3.22418981 3.57424082,3.22408741 L3.59202055,3.22402251 C3.61600759,3.22402251 3.63999463,3.22437692 3.66397314,3.22508575 C3.69176119,3.22590043 3.72012236,3.22722855 3.74845755,3.22905289 C3.77692744,3.23089046 3.80498198,3.23319023 3.83299719,3.23597733 C3.86236278,3.23889105 3.89230728,3.24242516 3.92218997,3.24651769 C3.95842477,3.25149198 3.99379267,3.25714552 4.02904516,3.2635852 C4.04457753,3.26641925 4.06056799,3.26950351 4.07653203,3.27274998 C4.1217801,3.28195855 4.16647313,3.29238022 4.21089814,3.30408537 C4.22093231,3.3067264 4.23153789,3.30959531 4.24212737,3.31253756 C4.27196202,3.32083528 4.30106886,3.32952376 4.33003598,3.33877116 C4.35855924,3.347869 4.38751122,3.35771229 4.41630528,3.3681193 C4.42116985,3.36987869 4.42551008,3.37146263 4.42984665,3.3730594 C4.4761162,3.39008583 4.52241276,3.4087674 4.56821184,3.42893807 C4.59406406,3.44033198 4.61917606,3.45191971 4.64412424,3.46396063 C4.67111495,3.47697976 4.69839649,3.4907848 4.72546291,3.50513959 C4.75890801,3.52288219 4.79178851,3.54132453 4.82431475,3.56059431 C4.8374698,3.56838641 4.85073285,3.5764165 4.86393439,3.58458539 C4.89491851,3.60376145 4.92539479,3.6235868 4.95550936,3.64416832 C4.9772823,3.65904443 4.99913454,3.67451232 5.02078256,3.69038541 C5.03998798,3.70447076 5.05881967,3.71870909 5.07748715,3.73325923 C5.10440445,3.75423289 5.13126725,3.7760983 5.15775949,3.79862613 C5.1821715,3.81939236 5.20595148,3.84042939 5.22940861,3.86201411 C5.24512436,3.87647694 5.26059993,3.89109333 5.27592752,3.90595256 C5.28442786,3.91418351 5.29385225,3.92345739 5.30321896,3.9328241 L10.2031018,8.83270693 C10.255475,8.88508012 10.3065885,8.93859789 10.3564099,8.99321224 L10.2031018,8.83270693 C10.2748395,8.90444467 10.344214,8.97832987 10.4111413,9.05423915 C10.4223877,9.06699478 10.4335715,9.07981507 10.4446856,9.092692 C10.7663645,9.46539004 11.0297601,9.88553066 11.2252237,10.3388957 L11.6780206,11.3880225 L12.548286,13.4076516 C12.7467158,13.8678966 12.5344727,14.4018581 12.0742277,14.6002879 C11.9977866,14.6332447 11.9179446,14.6552159 11.836969,14.6662015 L11.7149387,14.6744406 C11.592625,14.6744406 11.4703113,14.6497231 11.3556497,14.6002879 L11.2340206,14.5480225 L9.33602055,13.7300225 L8.28689372,13.2772256 C7.83352871,13.081762 7.41338809,12.8183665 7.04069004,12.4966876 L7.0022372,12.4631433 C6.98177889,12.4451057 6.9614676,12.4268903 6.94130575,12.4084989 L7.04069004,12.4966876 C6.95122931,12.4194733 6.86450207,12.3389008 6.78070498,12.2551038 L1.88082214,7.35522092 C0.935753358,6.41015213 0.935753358,4.87789288 1.88082214,3.9328241 L1.90902055,3.90502251 L2.01192506,3.8109306 C2.19120357,3.65606766 2.38780913,3.5318516 2.59488381,3.4382824 C2.62872186,3.42311621 2.65522016,3.41182111 2.68187195,3.40102033 C2.76025666,3.36925866 2.83986347,3.34180278 2.92043821,3.31861145 L3.07557525,3.27946831 Z M9.58610551,9.95149698 L7.89951995,11.6381324 C8.10279642,11.805046 8.32371441,11.9494547 8.55841217,12.068738 L8.76594574,12.166096 L10.2570206,12.8090225 L10.7570206,12.3090225 L10.114094,10.8179477 C9.97930356,10.5053101 9.80144069,10.2137385 9.58610551,9.95149698 Z"),o(l,"id","Combined-Shape"),o(l,"fill-rule","nonzero"),o(s,"id","Rectangle"),o(s,"opacity","0.005"),o(s,"x","0"),o(s,"y","0"),o(s,"width","16"),o(s,"height","16"),o(s,"rx","2"),o(r,"id","Pencil_Base"),o(r,"fill",c=n[0]?n[1]:"#168AFF"),o(d,"id","Rectangle-Copy"),o(d,"transform","translate(3, 0.75) rotate(90) translate(-3, -0.75)"),o(d,"x","2.25"),o(d,"y","-2.25"),o(d,"width","1.5"),o(d,"height","6"),o(d,"rx","0.75"),o(u,"id","Group"),o(u,"transform","translate(9, 3.25)"),o(u,"fill",C=n[0]?n[1]:"#FF7E72"),o(i,"id","nextedit_deletion_dark"),o(i,"stroke","none"),o(i,"stroke-width","1"),o(i,"fill","none"),o(i,"fill-rule","evenodd"),o(t,"width","16"),o(t,"height","16"),o(t,"viewBox","0 0 16 16"),o(t,"version","1.1"),o(t,"xmlns","http://www.w3.org/2000/svg"),o(t,"xmlns:xlink","http://www.w3.org/1999/xlink")},m(g,h){A(g,t,h),f(t,e),f(e,a),f(t,i),f(i,r),f(r,l),f(r,s),f(i,u),f(u,d)},p(g,[h]){3&h&&c!==(c=g[0]?g[1]:"#168AFF")&&o(r,"fill",c),3&h&&C!==(C=g[0]?g[1]:"#FF7E72")&&o(u,"fill",C)},i:_,o:_,d(g){g&&S(t)}}}function Z0(n,t,e){let{mask:a=!1}=t,{maskColor:i="white"}=t;return n.$$set=r=>{"mask"in r&&e(0,a=r.mask),"maskColor"in r&&e(1,i=r.maskColor)},[a,i]}class D0 extends O{constructor(t){super(),R(this,t,Z0,P0,B,{mask:0,maskColor:1})}}function T0(n){let t,e,a,i,r,l,s,c,u,d,C,g,h,p,L;return{c(){t=m("svg"),e=m("title"),a=Z("nextedit_change_light"),i=m("g"),r=m("g"),l=m("path"),s=m("rect"),u=m("g"),d=m("g"),C=m("rect"),g=m("rect"),h=m("g"),p=m("path"),o(l,"d","M3.07557525,3.27946831 C3.10738379,3.27258798 3.13664209,3.26682472 3.16597818,3.26160513 C3.19407786,3.25661079 3.22181021,3.25217747 3.24959807,3.24822758 C3.3431507,3.23490837 3.43787348,3.22705558 3.53270619,3.22474499 C3.54619312,3.22441336 3.56021661,3.22418981 3.57424082,3.22408741 L3.59202055,3.22402251 C3.61600759,3.22402251 3.63999463,3.22437692 3.66397314,3.22508575 C3.69176119,3.22590043 3.72012236,3.22722855 3.74845755,3.22905289 C3.77692744,3.23089046 3.80498198,3.23319023 3.83299719,3.23597733 C3.86236278,3.23889105 3.89230728,3.24242516 3.92218997,3.24651769 C3.95842477,3.25149198 3.99379267,3.25714552 4.02904516,3.2635852 C4.04457753,3.26641925 4.06056799,3.26950351 4.07653203,3.27274998 C4.1217801,3.28195855 4.16647313,3.29238022 4.21089814,3.30408537 C4.22093231,3.3067264 4.23153789,3.30959531 4.24212737,3.31253756 C4.27196202,3.32083528 4.30106886,3.32952376 4.33003598,3.33877116 C4.35855924,3.347869 4.38751122,3.35771229 4.41630528,3.3681193 C4.42116985,3.36987869 4.42551008,3.37146263 4.42984665,3.3730594 C4.4761162,3.39008583 4.52241276,3.4087674 4.56821184,3.42893807 C4.59406406,3.44033198 4.61917606,3.45191971 4.64412424,3.46396063 C4.67111495,3.47697976 4.69839649,3.4907848 4.72546291,3.50513959 C4.75890801,3.52288219 4.79178851,3.54132453 4.82431475,3.56059431 C4.8374698,3.56838641 4.85073285,3.5764165 4.86393439,3.58458539 C4.89491851,3.60376145 4.92539479,3.6235868 4.95550936,3.64416832 C4.9772823,3.65904443 4.99913454,3.67451232 5.02078256,3.69038541 C5.03998798,3.70447076 5.05881967,3.71870909 5.07748715,3.73325923 C5.10440445,3.75423289 5.13126725,3.7760983 5.15775949,3.79862613 C5.1821715,3.81939236 5.20595148,3.84042939 5.22940861,3.86201411 C5.24512436,3.87647694 5.26059993,3.89109333 5.27592752,3.90595256 C5.28442786,3.91418351 5.29385225,3.92345739 5.30321896,3.9328241 L10.2031018,8.83270693 C10.255475,8.88508012 10.3065885,8.93859789 10.3564099,8.99321224 L10.2031018,8.83270693 C10.2748395,8.90444467 10.344214,8.97832987 10.4111413,9.05423915 C10.4223877,9.06699478 10.4335715,9.07981507 10.4446856,9.092692 C10.7663645,9.46539004 11.0297601,9.88553066 11.2252237,10.3388957 L11.6780206,11.3880225 L12.548286,13.4076516 C12.7467158,13.8678966 12.5344727,14.4018581 12.0742277,14.6002879 C11.9977866,14.6332447 11.9179446,14.6552159 11.836969,14.6662015 L11.7149387,14.6744406 C11.592625,14.6744406 11.4703113,14.6497231 11.3556497,14.6002879 L11.2340206,14.5480225 L9.33602055,13.7300225 L8.28689372,13.2772256 C7.83352871,13.081762 7.41338809,12.8183665 7.04069004,12.4966876 L7.0022372,12.4631433 C6.98177889,12.4451057 6.9614676,12.4268903 6.94130575,12.4084989 L7.04069004,12.4966876 C6.95122931,12.4194733 6.86450207,12.3389008 6.78070498,12.2551038 L1.88082214,7.35522092 C0.935753358,6.41015213 0.935753358,4.87789288 1.88082214,3.9328241 L1.90902055,3.90502251 L2.01192506,3.8109306 C2.19120357,3.65606766 2.38780913,3.5318516 2.59488381,3.4382824 C2.62872186,3.42311621 2.65522016,3.41182111 2.68187195,3.40102033 C2.76025666,3.36925866 2.83986347,3.34180278 2.92043821,3.31861145 L3.07557525,3.27946831 Z M9.58610551,9.95149698 L7.89951995,11.6381324 C8.10279642,11.805046 8.32371441,11.9494547 8.55841217,12.068738 L8.76594574,12.166096 L10.2570206,12.8090225 L10.7570206,12.3090225 L10.114094,10.8179477 C9.97930356,10.5053101 9.80144069,10.2137385 9.58610551,9.95149698 Z"),o(l,"id","Combined-Shape"),o(l,"fill-rule","nonzero"),o(s,"id","Rectangle"),o(s,"opacity","0.005"),o(s,"x","0"),o(s,"y","0"),o(s,"width","16"),o(s,"height","16"),o(s,"rx","2"),o(r,"id","Pencil_Base"),o(r,"fill",c=n[0]?n[1]:"#0A84FF"),o(C,"id","Rectangle-Copy"),o(C,"transform","translate(2, 0.5) rotate(90) translate(-2, -0.5)"),o(C,"x","1.5"),o(C,"y","-1.5"),o(C,"width","1"),o(C,"height","4"),o(C,"rx","0.5"),o(g,"id","Rectangle-Copy"),o(g,"transform","translate(2, 4.5) rotate(90) translate(-2, -4.5)"),o(g,"x","1.5"),o(g,"y","2.5"),o(g,"width","1"),o(g,"height","4"),o(g,"rx","0.5"),o(d,"id","Group"),o(d,"transform","translate(0, 1.5)"),o(p,"d","M2,-1.5 C2.27614237,-1.5 2.5,-1.27614237 2.5,-1 L2.5,2 C2.5,2.27614237 2.27614237,2.5 2,2.5 C1.72385763,2.5 1.5,2.27614237 1.5,2 L1.5,-1 C1.5,-1.27614237 1.72385763,-1.5 2,-1.5 Z"),o(p,"id","Rectangle-Copy"),o(p,"transform","translate(2, 0.5) rotate(90) translate(-2, -0.5)"),o(h,"id","Group"),o(h,"transform","translate(2, 2) rotate(90) translate(-2, -2)translate(0, 1.5)"),o(u,"id","Group-2"),o(u,"transform","translate(10.5, 1.5)"),o(u,"fill",L=n[0]?n[1]:"#F4A414"),o(i,"id","nextedit_change_light"),o(i,"stroke","none"),o(i,"stroke-width","1"),o(i,"fill","none"),o(i,"fill-rule","evenodd"),o(t,"width","16"),o(t,"height","16"),o(t,"viewBox","0 0 16 16"),o(t,"version","1.1"),o(t,"xmlns","http://www.w3.org/2000/svg"),o(t,"xmlns:xlink","http://www.w3.org/1999/xlink")},m(w,x){A(w,t,x),f(t,e),f(e,a),f(t,i),f(i,r),f(r,l),f(r,s),f(i,u),f(u,d),f(d,C),f(d,g),f(u,h),f(h,p)},p(w,[x]){3&x&&c!==(c=w[0]?w[1]:"#0A84FF")&&o(r,"fill",c),3&x&&L!==(L=w[0]?w[1]:"#F4A414")&&o(u,"fill",L)},i:_,o:_,d(w){w&&S(t)}}}function I0(n,t,e){let{mask:a=!1}=t,{maskColor:i="white"}=t;return n.$$set=r=>{"mask"in r&&e(0,a=r.mask),"maskColor"in r&&e(1,i=r.maskColor)},[a,i]}class G3 extends O{constructor(t){super(),R(this,t,I0,T0,B,{mask:0,maskColor:1})}}function q0(n){let t,e,a,i,r,l,s,c,u,d,C,g,h,p,L;return{c(){t=m("svg"),e=m("title"),a=Z("nextedit_change_dark"),i=m("g"),r=m("g"),l=m("path"),s=m("rect"),u=m("g"),d=m("g"),C=m("rect"),g=m("rect"),h=m("g"),p=m("path"),o(l,"d","M3.07557525,3.27946831 C3.10738379,3.27258798 3.13664209,3.26682472 3.16597818,3.26160513 C3.19407786,3.25661079 3.22181021,3.25217747 3.24959807,3.24822758 C3.3431507,3.23490837 3.43787348,3.22705558 3.53270619,3.22474499 C3.54619312,3.22441336 3.56021661,3.22418981 3.57424082,3.22408741 L3.59202055,3.22402251 C3.61600759,3.22402251 3.63999463,3.22437692 3.66397314,3.22508575 C3.69176119,3.22590043 3.72012236,3.22722855 3.74845755,3.22905289 C3.77692744,3.23089046 3.80498198,3.23319023 3.83299719,3.23597733 C3.86236278,3.23889105 3.89230728,3.24242516 3.92218997,3.24651769 C3.95842477,3.25149198 3.99379267,3.25714552 4.02904516,3.2635852 C4.04457753,3.26641925 4.06056799,3.26950351 4.07653203,3.27274998 C4.1217801,3.28195855 4.16647313,3.29238022 4.21089814,3.30408537 C4.22093231,3.3067264 4.23153789,3.30959531 4.24212737,3.31253756 C4.27196202,3.32083528 4.30106886,3.32952376 4.33003598,3.33877116 C4.35855924,3.347869 4.38751122,3.35771229 4.41630528,3.3681193 C4.42116985,3.36987869 4.42551008,3.37146263 4.42984665,3.3730594 C4.4761162,3.39008583 4.52241276,3.4087674 4.56821184,3.42893807 C4.59406406,3.44033198 4.61917606,3.45191971 4.64412424,3.46396063 C4.67111495,3.47697976 4.69839649,3.4907848 4.72546291,3.50513959 C4.75890801,3.52288219 4.79178851,3.54132453 4.82431475,3.56059431 C4.8374698,3.56838641 4.85073285,3.5764165 4.86393439,3.58458539 C4.89491851,3.60376145 4.92539479,3.6235868 4.95550936,3.64416832 C4.9772823,3.65904443 4.99913454,3.67451232 5.02078256,3.69038541 C5.03998798,3.70447076 5.05881967,3.71870909 5.07748715,3.73325923 C5.10440445,3.75423289 5.13126725,3.7760983 5.15775949,3.79862613 C5.1821715,3.81939236 5.20595148,3.84042939 5.22940861,3.86201411 C5.24512436,3.87647694 5.26059993,3.89109333 5.27592752,3.90595256 C5.28442786,3.91418351 5.29385225,3.92345739 5.30321896,3.9328241 L10.2031018,8.83270693 C10.255475,8.88508012 10.3065885,8.93859789 10.3564099,8.99321224 L10.2031018,8.83270693 C10.2748395,8.90444467 10.344214,8.97832987 10.4111413,9.05423915 C10.4223877,9.06699478 10.4335715,9.07981507 10.4446856,9.092692 C10.7663645,9.46539004 11.0297601,9.88553066 11.2252237,10.3388957 L11.6780206,11.3880225 L12.548286,13.4076516 C12.7467158,13.8678966 12.5344727,14.4018581 12.0742277,14.6002879 C11.9977866,14.6332447 11.9179446,14.6552159 11.836969,14.6662015 L11.7149387,14.6744406 C11.592625,14.6744406 11.4703113,14.6497231 11.3556497,14.6002879 L11.2340206,14.5480225 L9.33602055,13.7300225 L8.28689372,13.2772256 C7.83352871,13.081762 7.41338809,12.8183665 7.04069004,12.4966876 L7.0022372,12.4631433 C6.98177889,12.4451057 6.9614676,12.4268903 6.94130575,12.4084989 L7.04069004,12.4966876 C6.95122931,12.4194733 6.86450207,12.3389008 6.78070498,12.2551038 L1.88082214,7.35522092 C0.935753358,6.41015213 0.935753358,4.87789288 1.88082214,3.9328241 L1.90902055,3.90502251 L2.01192506,3.8109306 C2.19120357,3.65606766 2.38780913,3.5318516 2.59488381,3.4382824 C2.62872186,3.42311621 2.65522016,3.41182111 2.68187195,3.40102033 C2.76025666,3.36925866 2.83986347,3.34180278 2.92043821,3.31861145 L3.07557525,3.27946831 Z M9.58610551,9.95149698 L7.89951995,11.6381324 C8.10279642,11.805046 8.32371441,11.9494547 8.55841217,12.068738 L8.76594574,12.166096 L10.2570206,12.8090225 L10.7570206,12.3090225 L10.114094,10.8179477 C9.97930356,10.5053101 9.80144069,10.2137385 9.58610551,9.95149698 Z"),o(l,"id","Combined-Shape"),o(l,"fill-rule","nonzero"),o(s,"id","Rectangle"),o(s,"opacity","0.005"),o(s,"x","0"),o(s,"y","0"),o(s,"width","16"),o(s,"height","16"),o(s,"rx","2"),o(r,"id","Pencil_Base"),o(r,"fill",c=n[0]?n[1]:"#168AFF"),o(C,"id","Rectangle-Copy"),o(C,"transform","translate(2, 0.5) rotate(90) translate(-2, -0.5)"),o(C,"x","1.5"),o(C,"y","-1.5"),o(C,"width","1"),o(C,"height","4"),o(C,"rx","0.5"),o(g,"id","Rectangle-Copy"),o(g,"transform","translate(2, 4.5) rotate(90) translate(-2, -4.5)"),o(g,"x","1.5"),o(g,"y","2.5"),o(g,"width","1"),o(g,"height","4"),o(g,"rx","0.5"),o(d,"id","Group"),o(d,"transform","translate(0, 1.5)"),o(p,"d","M2,-1.5 C2.27614237,-1.5 2.5,-1.27614237 2.5,-1 L2.5,2 C2.5,2.27614237 2.27614237,2.5 2,2.5 C1.72385763,2.5 1.5,2.27614237 1.5,2 L1.5,-1 C1.5,-1.27614237 1.72385763,-1.5 2,-1.5 Z"),o(p,"id","Rectangle-Copy"),o(p,"transform","translate(2, 0.5) rotate(90) translate(-2, -0.5)"),o(h,"id","Group"),o(h,"transform","translate(2, 2) rotate(90) translate(-2, -2)translate(0, 1.5)"),o(u,"id","Group-2"),o(u,"transform","translate(10.5, 1.5)"),o(u,"fill",L=n[0]?n[1]:"#FFC255"),o(i,"id","nextedit_change_dark"),o(i,"stroke","none"),o(i,"stroke-width","1"),o(i,"fill","none"),o(i,"fill-rule","evenodd"),o(t,"width","16"),o(t,"height","16"),o(t,"viewBox","0 0 16 16"),o(t,"version","1.1"),o(t,"xmlns","http://www.w3.org/2000/svg"),o(t,"xmlns:xlink","http://www.w3.org/1999/xlink")},m(w,x){A(w,t,x),f(t,e),f(e,a),f(t,i),f(i,r),f(r,l),f(r,s),f(i,u),f(u,d),f(d,C),f(d,g),f(u,h),f(h,p)},p(w,[x]){3&x&&c!==(c=w[0]?w[1]:"#168AFF")&&o(r,"fill",c),3&x&&L!==(L=w[0]?w[1]:"#FFC255")&&o(u,"fill",L)},i:_,o:_,d(w){w&&S(t)}}}function V0(n,t,e){let{mask:a=!1}=t,{maskColor:i="white"}=t;return n.$$set=r=>{"mask"in r&&e(0,a=r.mask),"maskColor"in r&&e(1,i=r.maskColor)},[a,i]}class U3 extends O{constructor(t){super(),R(this,t,V0,q0,B,{mask:0,maskColor:1})}}function G0(n){let t,e,a,i,r,l,s,c,u,d,C;return{c(){t=m("svg"),e=m("title"),a=Z("nextedit_applied_light"),i=m("g"),r=m("g"),l=m("path"),c=m("g"),u=m("path"),d=m("rect"),o(l,"d","M2.68994141,6.32666016 C3.01578776,6.32666016 3.26074219,6.2047526 3.42480469,5.9609375 L6.55908203,1.30566406 C6.61832682,1.21907552 6.66162109,1.13191732 6.68896484,1.04418945 C6.71630859,0.956461589 6.72998047,0.872721354 6.72998047,0.79296875 C6.72998047,0.567382812 6.65079753,0.37882487 6.49243164,0.227294922 C6.33406576,0.075764974 6.13867188,0 5.90625,0 C5.74902344,0 5.61572266,0.0313313802 5.50634766,0.0939941406 C5.39697266,0.156656901 5.29329427,0.263183594 5.1953125,0.413574219 L2.67626953,4.34765625 L1.42871094,2.91210938 C1.26692708,2.72753906 1.06184896,2.63525391 0.813476562,2.63525391 C0.578776042,2.63525391 0.384521484,2.71101888 0.230712891,2.86254883 C0.0769042969,3.01407878 0,3.20377604 0,3.43164063 C0,3.53417969 0.0165201823,3.62988281 0.0495605469,3.71875 C0.0826009115,3.80761719 0.143554688,3.90218099 0.232421875,4.00244141 L1.98925781,6.01904297 C2.16927083,6.22412109 2.40283203,6.32666016 2.68994141,6.32666016 Z"),o(l,"id","Path"),o(r,"id","􀆅"),o(r,"transform","translate(8.5216, 0.8311)"),o(r,"fill",s=n[0]?n[1]:"#34C759"),o(r,"fill-rule","nonzero"),o(u,"d","M3.07557525,3.27946831 C3.10738379,3.27258798 3.13664209,3.26682472 3.16597818,3.26160513 C3.19407786,3.25661079 3.22181021,3.25217747 3.24959807,3.24822758 C3.3431507,3.23490837 3.43787348,3.22705558 3.53270619,3.22474499 C3.54619312,3.22441336 3.56021661,3.22418981 3.57424082,3.22408741 L3.59202055,3.22402251 C3.61600759,3.22402251 3.63999463,3.22437692 3.66397314,3.22508575 C3.69176119,3.22590043 3.72012236,3.22722855 3.74845755,3.22905289 C3.77692744,3.23089046 3.80498198,3.23319023 3.83299719,3.23597733 C3.86236278,3.23889105 3.89230728,3.24242516 3.92218997,3.24651769 C3.95842477,3.25149198 3.99379267,3.25714552 4.02904516,3.2635852 C4.04457753,3.26641925 4.06056799,3.26950351 4.07653203,3.27274998 C4.1217801,3.28195855 4.16647313,3.29238022 4.21089814,3.30408537 C4.22093231,3.3067264 4.23153789,3.30959531 4.24212737,3.31253756 C4.27196202,3.32083528 4.30106886,3.32952376 4.33003598,3.33877116 C4.35855924,3.347869 4.38751122,3.35771229 4.41630528,3.3681193 C4.42116985,3.36987869 4.42551008,3.37146263 4.42984665,3.3730594 C4.4761162,3.39008583 4.52241276,3.4087674 4.56821184,3.42893807 C4.59406406,3.44033198 4.61917606,3.45191971 4.64412424,3.46396063 C4.67111495,3.47697976 4.69839649,3.4907848 4.72546291,3.50513959 C4.75890801,3.52288219 4.79178851,3.54132453 4.82431475,3.56059431 C4.8374698,3.56838641 4.85073285,3.5764165 4.86393439,3.58458539 C4.89491851,3.60376145 4.92539479,3.6235868 4.95550936,3.64416832 C4.9772823,3.65904443 4.99913454,3.67451232 5.02078256,3.69038541 C5.03998798,3.70447076 5.05881967,3.71870909 5.07748715,3.73325923 C5.10440445,3.75423289 5.13126725,3.7760983 5.15775949,3.79862613 C5.1821715,3.81939236 5.20595148,3.84042939 5.22940861,3.86201411 C5.24512436,3.87647694 5.26059993,3.89109333 5.27592752,3.90595256 C5.28442786,3.91418351 5.29385225,3.92345739 5.30321896,3.9328241 L10.2031018,8.83270693 C10.255475,8.88508012 10.3065885,8.93859789 10.3564099,8.99321224 L10.2031018,8.83270693 C10.2748395,8.90444467 10.344214,8.97832987 10.4111413,9.05423915 C10.4223877,9.06699478 10.4335715,9.07981507 10.4446856,9.092692 C10.7663645,9.46539004 11.0297601,9.88553066 11.2252237,10.3388957 L11.6780206,11.3880225 L12.548286,13.4076516 C12.7467158,13.8678966 12.5344727,14.4018581 12.0742277,14.6002879 C11.9977866,14.6332447 11.9179446,14.6552159 11.836969,14.6662015 L11.7149387,14.6744406 C11.592625,14.6744406 11.4703113,14.6497231 11.3556497,14.6002879 L11.2340206,14.5480225 L9.33602055,13.7300225 L8.28689372,13.2772256 C7.83352871,13.081762 7.41338809,12.8183665 7.04069004,12.4966876 L7.0022372,12.4631433 C6.98177889,12.4451057 6.9614676,12.4268903 6.94130575,12.4084989 L7.04069004,12.4966876 C6.95122931,12.4194733 6.86450207,12.3389008 6.78070498,12.2551038 L1.88082214,7.35522092 C0.935753358,6.41015213 0.935753358,4.87789288 1.88082214,3.9328241 L1.90902055,3.90502251 L2.01192506,3.8109306 C2.19120357,3.65606766 2.38780913,3.5318516 2.59488381,3.4382824 C2.62872186,3.42311621 2.65522016,3.41182111 2.68187195,3.40102033 C2.76025666,3.36925866 2.83986347,3.34180278 2.92043821,3.31861145 L3.07557525,3.27946831 Z M9.58610551,9.95149698 L7.89951995,11.6381324 C8.10279642,11.805046 8.32371441,11.9494547 8.55841217,12.068738 L8.76594574,12.166096 L10.2570206,12.8090225 L10.7570206,12.3090225 L10.114094,10.8179477 C9.97930356,10.5053101 9.80144069,10.2137385 9.58610551,9.95149698 Z"),o(u,"id","Combined-Shape"),o(u,"fill-rule","nonzero"),o(d,"id","Rectangle"),o(d,"opacity","0.005"),o(d,"x","0"),o(d,"y","0"),o(d,"width","16"),o(d,"height","16"),o(c,"id","Pencil_Base"),o(c,"fill",C=n[0]?n[1]:"#000000"),N(c,"opacity",n[0]?"1":"0.2"),o(i,"id","nextedit_applied_light"),o(i,"stroke","none"),o(i,"stroke-width","1"),o(i,"fill","none"),o(i,"fill-rule","evenodd"),o(t,"width","16"),o(t,"height","16"),o(t,"viewBox","0 0 16 16"),o(t,"version","1.1"),o(t,"xmlns","http://www.w3.org/2000/svg"),o(t,"xmlns:xlink","http://www.w3.org/1999/xlink")},m(g,h){A(g,t,h),f(t,e),f(e,a),f(t,i),f(i,r),f(r,l),f(i,c),f(c,u),f(c,d)},p(g,[h]){3&h&&s!==(s=g[0]?g[1]:"#34C759")&&o(r,"fill",s),3&h&&C!==(C=g[0]?g[1]:"#000000")&&o(c,"fill",C),1&h&&N(c,"opacity",g[0]?"1":"0.2")},i:_,o:_,d(g){g&&S(t)}}}function U0(n,t,e){let{mask:a=!1}=t,{maskColor:i="white"}=t;return n.$$set=r=>{"mask"in r&&e(0,a=r.mask),"maskColor"in r&&e(1,i=r.maskColor)},[a,i]}class W0 extends O{constructor(t){super(),R(this,t,U0,G0,B,{mask:0,maskColor:1})}}function H0(n){let t,e,a,i,r,l,s,c,u,d,C;return{c(){t=m("svg"),e=m("title"),a=Z("nextedit_applied_dark"),i=m("g"),r=m("g"),l=m("path"),c=m("g"),u=m("path"),d=m("rect"),o(l,"d","M2.68994141,6.32666016 C3.01578776,6.32666016 3.26074219,6.2047526 3.42480469,5.9609375 L6.55908203,1.30566406 C6.61832682,1.21907552 6.66162109,1.13191732 6.68896484,1.04418945 C6.71630859,0.956461589 6.72998047,0.872721354 6.72998047,0.79296875 C6.72998047,0.567382812 6.65079753,0.37882487 6.49243164,0.227294922 C6.33406576,0.075764974 6.13867188,0 5.90625,0 C5.74902344,0 5.61572266,0.0313313802 5.50634766,0.0939941406 C5.39697266,0.156656901 5.29329427,0.263183594 5.1953125,0.413574219 L2.67626953,4.34765625 L1.42871094,2.91210938 C1.26692708,2.72753906 1.06184896,2.63525391 0.813476562,2.63525391 C0.578776042,2.63525391 0.384521484,2.71101888 0.230712891,2.86254883 C0.0769042969,3.01407878 0,3.20377604 0,3.43164063 C0,3.53417969 0.0165201823,3.62988281 0.0495605469,3.71875 C0.0826009115,3.80761719 0.143554688,3.90218099 0.232421875,4.00244141 L1.98925781,6.01904297 C2.16927083,6.22412109 2.40283203,6.32666016 2.68994141,6.32666016 Z"),o(l,"id","Path"),o(r,"id","􀆅"),o(r,"transform","translate(8.5167, 0.8311)"),o(r,"fill",s=n[0]?n[1]:"#30D158"),o(r,"fill-rule","nonzero"),o(u,"d","M3.07557525,3.27946831 C3.10738379,3.27258798 3.13664209,3.26682472 3.16597818,3.26160513 C3.19407786,3.25661079 3.22181021,3.25217747 3.24959807,3.24822758 C3.3431507,3.23490837 3.43787348,3.22705558 3.53270619,3.22474499 C3.54619312,3.22441336 3.56021661,3.22418981 3.57424082,3.22408741 L3.59202055,3.22402251 C3.61600759,3.22402251 3.63999463,3.22437692 3.66397314,3.22508575 C3.69176119,3.22590043 3.72012236,3.22722855 3.74845755,3.22905289 C3.77692744,3.23089046 3.80498198,3.23319023 3.83299719,3.23597733 C3.86236278,3.23889105 3.89230728,3.24242516 3.92218997,3.24651769 C3.95842477,3.25149198 3.99379267,3.25714552 4.02904516,3.2635852 C4.04457753,3.26641925 4.06056799,3.26950351 4.07653203,3.27274998 C4.1217801,3.28195855 4.16647313,3.29238022 4.21089814,3.30408537 C4.22093231,3.3067264 4.23153789,3.30959531 4.24212737,3.31253756 C4.27196202,3.32083528 4.30106886,3.32952376 4.33003598,3.33877116 C4.35855924,3.347869 4.38751122,3.35771229 4.41630528,3.3681193 C4.42116985,3.36987869 4.42551008,3.37146263 4.42984665,3.3730594 C4.4761162,3.39008583 4.52241276,3.4087674 4.56821184,3.42893807 C4.59406406,3.44033198 4.61917606,3.45191971 4.64412424,3.46396063 C4.67111495,3.47697976 4.69839649,3.4907848 4.72546291,3.50513959 C4.75890801,3.52288219 4.79178851,3.54132453 4.82431475,3.56059431 C4.8374698,3.56838641 4.85073285,3.5764165 4.86393439,3.58458539 C4.89491851,3.60376145 4.92539479,3.6235868 4.95550936,3.64416832 C4.9772823,3.65904443 4.99913454,3.67451232 5.02078256,3.69038541 C5.03998798,3.70447076 5.05881967,3.71870909 5.07748715,3.73325923 C5.10440445,3.75423289 5.13126725,3.7760983 5.15775949,3.79862613 C5.1821715,3.81939236 5.20595148,3.84042939 5.22940861,3.86201411 C5.24512436,3.87647694 5.26059993,3.89109333 5.27592752,3.90595256 C5.28442786,3.91418351 5.29385225,3.92345739 5.30321896,3.9328241 L10.2031018,8.83270693 C10.255475,8.88508012 10.3065885,8.93859789 10.3564099,8.99321224 L10.2031018,8.83270693 C10.2748395,8.90444467 10.344214,8.97832987 10.4111413,9.05423915 C10.4223877,9.06699478 10.4335715,9.07981507 10.4446856,9.092692 C10.7663645,9.46539004 11.0297601,9.88553066 11.2252237,10.3388957 L11.6780206,11.3880225 L12.548286,13.4076516 C12.7467158,13.8678966 12.5344727,14.4018581 12.0742277,14.6002879 C11.9977866,14.6332447 11.9179446,14.6552159 11.836969,14.6662015 L11.7149387,14.6744406 C11.592625,14.6744406 11.4703113,14.6497231 11.3556497,14.6002879 L11.2340206,14.5480225 L9.33602055,13.7300225 L8.28689372,13.2772256 C7.83352871,13.081762 7.41338809,12.8183665 7.04069004,12.4966876 L7.0022372,12.4631433 C6.98177889,12.4451057 6.9614676,12.4268903 6.94130575,12.4084989 L7.04069004,12.4966876 C6.95122931,12.4194733 6.86450207,12.3389008 6.78070498,12.2551038 L1.88082214,7.35522092 C0.935753358,6.41015213 0.935753358,4.87789288 1.88082214,3.9328241 L1.90902055,3.90502251 L2.01192506,3.8109306 C2.19120357,3.65606766 2.38780913,3.5318516 2.59488381,3.4382824 C2.62872186,3.42311621 2.65522016,3.41182111 2.68187195,3.40102033 C2.76025666,3.36925866 2.83986347,3.34180278 2.92043821,3.31861145 L3.07557525,3.27946831 Z M9.58610551,9.95149698 L7.89951995,11.6381324 C8.10279642,11.805046 8.32371441,11.9494547 8.55841217,12.068738 L8.76594574,12.166096 L10.2570206,12.8090225 L10.7570206,12.3090225 L10.114094,10.8179477 C9.97930356,10.5053101 9.80144069,10.2137385 9.58610551,9.95149698 Z"),o(u,"id","Combined-Shape"),o(u,"fill-rule","nonzero"),o(d,"id","Rectangle"),o(d,"opacity","0.005"),o(d,"x","0"),o(d,"y","0"),o(d,"width","16"),o(d,"height","16"),o(c,"id","Pencil_Base"),o(c,"fill",C=n[0]?n[1]:"#FFFFFF"),N(c,"opacity",n[0]?"1":"0.4"),o(i,"id","nextedit_applied_dark"),o(i,"stroke","none"),o(i,"stroke-width","1"),o(i,"fill","none"),o(i,"fill-rule","evenodd"),o(t,"width","16"),o(t,"height","16"),o(t,"viewBox","0 0 16 16"),o(t,"version","1.1"),o(t,"xmlns","http://www.w3.org/2000/svg"),o(t,"xmlns:xlink","http://www.w3.org/1999/xlink")},m(g,h){A(g,t,h),f(t,e),f(e,a),f(t,i),f(i,r),f(r,l),f(i,c),f(c,u),f(c,d)},p(g,[h]){3&h&&s!==(s=g[0]?g[1]:"#30D158")&&o(r,"fill",s),3&h&&C!==(C=g[0]?g[1]:"#FFFFFF")&&o(c,"fill",C),1&h&&N(c,"opacity",g[0]?"1":"0.4")},i:_,o:_,d(g){g&&S(t)}}}function K0(n,t,e){let{mask:a=!1}=t,{maskColor:i="white"}=t;return n.$$set=r=>{"mask"in r&&e(0,a=r.mask),"maskColor"in r&&e(1,i=r.maskColor)},[a,i]}class J0 extends O{constructor(t){super(),R(this,t,K0,H0,B,{mask:0,maskColor:1})}}function Q0(n){let t,e,a,i,r,l,s,c,u,d,C,g;return{c(){t=m("svg"),e=m("title"),a=Z("nextedit_rejected_light"),i=m("g"),r=m("g"),l=m("path"),s=m("rect"),u=m("g"),d=m("rect"),C=m("rect"),o(l,"d","M3.07557525,3.27946831 C3.10738379,3.27258798 3.13664209,3.26682472 3.16597818,3.26160513 C3.19407786,3.25661079 3.22181021,3.25217747 3.24959807,3.24822758 C3.3431507,3.23490837 3.43787348,3.22705558 3.53270619,3.22474499 C3.54619312,3.22441336 3.56021661,3.22418981 3.57424082,3.22408741 L3.59202055,3.22402251 C3.61600759,3.22402251 3.63999463,3.22437692 3.66397314,3.22508575 C3.69176119,3.22590043 3.72012236,3.22722855 3.74845755,3.22905289 C3.77692744,3.23089046 3.80498198,3.23319023 3.83299719,3.23597733 C3.86236278,3.23889105 3.89230728,3.24242516 3.92218997,3.24651769 C3.95842477,3.25149198 3.99379267,3.25714552 4.02904516,3.2635852 C4.04457753,3.26641925 4.06056799,3.26950351 4.07653203,3.27274998 C4.1217801,3.28195855 4.16647313,3.29238022 4.21089814,3.30408537 C4.22093231,3.3067264 4.23153789,3.30959531 4.24212737,3.31253756 C4.27196202,3.32083528 4.30106886,3.32952376 4.33003598,3.33877116 C4.35855924,3.347869 4.38751122,3.35771229 4.41630528,3.3681193 C4.42116985,3.36987869 4.42551008,3.37146263 4.42984665,3.3730594 C4.4761162,3.39008583 4.52241276,3.4087674 4.56821184,3.42893807 C4.59406406,3.44033198 4.61917606,3.45191971 4.64412424,3.46396063 C4.67111495,3.47697976 4.69839649,3.4907848 4.72546291,3.50513959 C4.75890801,3.52288219 4.79178851,3.54132453 4.82431475,3.56059431 C4.8374698,3.56838641 4.85073285,3.5764165 4.86393439,3.58458539 C4.89491851,3.60376145 4.92539479,3.6235868 4.95550936,3.64416832 C4.9772823,3.65904443 4.99913454,3.67451232 5.02078256,3.69038541 C5.03998798,3.70447076 5.05881967,3.71870909 5.07748715,3.73325923 C5.10440445,3.75423289 5.13126725,3.7760983 5.15775949,3.79862613 C5.1821715,3.81939236 5.20595148,3.84042939 5.22940861,3.86201411 C5.24512436,3.87647694 5.26059993,3.89109333 5.27592752,3.90595256 C5.28442786,3.91418351 5.29385225,3.92345739 5.30321896,3.9328241 L10.2031018,8.83270693 C10.255475,8.88508012 10.3065885,8.93859789 10.3564099,8.99321224 L10.2031018,8.83270693 C10.2748395,8.90444467 10.344214,8.97832987 10.4111413,9.05423915 C10.4223877,9.06699478 10.4335715,9.07981507 10.4446856,9.092692 C10.7663645,9.46539004 11.0297601,9.88553066 11.2252237,10.3388957 L11.6780206,11.3880225 L12.548286,13.4076516 C12.7467158,13.8678966 12.5344727,14.4018581 12.0742277,14.6002879 C11.9977866,14.6332447 11.9179446,14.6552159 11.836969,14.6662015 L11.7149387,14.6744406 C11.592625,14.6744406 11.4703113,14.6497231 11.3556497,14.6002879 L11.2340206,14.5480225 L9.33602055,13.7300225 L8.28689372,13.2772256 C7.83352871,13.081762 7.41338809,12.8183665 7.04069004,12.4966876 L7.0022372,12.4631433 C6.98177889,12.4451057 6.9614676,12.4268903 6.94130575,12.4084989 L7.04069004,12.4966876 C6.95122931,12.4194733 6.86450207,12.3389008 6.78070498,12.2551038 L1.88082214,7.35522092 C0.935753358,6.41015213 0.935753358,4.87789288 1.88082214,3.9328241 L1.90902055,3.90502251 L2.01192506,3.8109306 C2.19120357,3.65606766 2.38780913,3.5318516 2.59488381,3.4382824 C2.62872186,3.42311621 2.65522016,3.41182111 2.68187195,3.40102033 C2.76025666,3.36925866 2.83986347,3.34180278 2.92043821,3.31861145 L3.07557525,3.27946831 Z M9.58610551,9.95149698 L7.89951995,11.6381324 C8.10279642,11.805046 8.32371441,11.9494547 8.55841217,12.068738 L8.76594574,12.166096 L10.2570206,12.8090225 L10.7570206,12.3090225 L10.114094,10.8179477 C9.97930356,10.5053101 9.80144069,10.2137385 9.58610551,9.95149698 Z"),o(l,"id","Combined-Shape"),o(l,"fill-rule","nonzero"),o(s,"id","Rectangle"),o(s,"opacity","0.005"),o(s,"x","0"),o(s,"y","0"),o(s,"width","16"),o(s,"height","16"),o(s,"rx","2"),o(r,"id","Pencil_Base"),o(r,"fill",c=n[0]?n[1]:"#0A84FF"),o(d,"id","Rectangle"),o(d,"x","2.25"),o(d,"y","0"),o(d,"width","1.5"),o(d,"height","6"),o(d,"rx","0.75"),o(C,"id","Rectangle-Copy"),o(C,"transform","translate(3, 3) rotate(90) translate(-3, -3)"),o(C,"x","2.25"),o(C,"y","1.13686838e-13"),o(C,"width","1.5"),o(C,"height","6"),o(C,"rx","0.75"),o(u,"id","Group"),o(u,"transform","translate(12, 4) rotate(45) translate(-12, -4)translate(9, 1)"),o(u,"fill",g=n[0]?n[1]:"#FF5D4E"),o(i,"id","nextedit_rejected_light"),o(i,"stroke","none"),o(i,"stroke-width","1"),o(i,"fill","none"),o(i,"fill-rule","evenodd"),o(t,"width","16"),o(t,"height","16"),o(t,"viewBox","0 0 16 16"),o(t,"version","1.1"),o(t,"xmlns","http://www.w3.org/2000/svg"),o(t,"xmlns:xlink","http://www.w3.org/1999/xlink")},m(h,p){A(h,t,p),f(t,e),f(e,a),f(t,i),f(i,r),f(r,l),f(r,s),f(i,u),f(u,d),f(u,C)},p(h,[p]){3&p&&c!==(c=h[0]?h[1]:"#0A84FF")&&o(r,"fill",c),3&p&&g!==(g=h[0]?h[1]:"#FF5D4E")&&o(u,"fill",g)},i:_,o:_,d(h){h&&S(t)}}}function X0(n,t,e){let{mask:a=!0}=t,{maskColor:i="white"}=t;return n.$$set=r=>{"mask"in r&&e(0,a=r.mask),"maskColor"in r&&e(1,i=r.maskColor)},[a,i]}class Y0 extends O{constructor(t){super(),R(this,t,X0,Q0,B,{mask:0,maskColor:1})}}function t4(n){let t,e,a,i,r,l,s,c,u,d,C,g;return{c(){t=m("svg"),e=m("title"),a=Z("nextedit_rejected_dark"),i=m("g"),r=m("g"),l=m("path"),s=m("rect"),u=m("g"),d=m("rect"),C=m("rect"),o(l,"d","M3.07557525,3.27946831 C3.10738379,3.27258798 3.13664209,3.26682472 3.16597818,3.26160513 C3.19407786,3.25661079 3.22181021,3.25217747 3.24959807,3.24822758 C3.3431507,3.23490837 3.43787348,3.22705558 3.53270619,3.22474499 C3.54619312,3.22441336 3.56021661,3.22418981 3.57424082,3.22408741 L3.59202055,3.22402251 C3.61600759,3.22402251 3.63999463,3.22437692 3.66397314,3.22508575 C3.69176119,3.22590043 3.72012236,3.22722855 3.74845755,3.22905289 C3.77692744,3.23089046 3.80498198,3.23319023 3.83299719,3.23597733 C3.86236278,3.23889105 3.89230728,3.24242516 3.92218997,3.24651769 C3.95842477,3.25149198 3.99379267,3.25714552 4.02904516,3.2635852 C4.04457753,3.26641925 4.06056799,3.26950351 4.07653203,3.27274998 C4.1217801,3.28195855 4.16647313,3.29238022 4.21089814,3.30408537 C4.22093231,3.3067264 4.23153789,3.30959531 4.24212737,3.31253756 C4.27196202,3.32083528 4.30106886,3.32952376 4.33003598,3.33877116 C4.35855924,3.347869 4.38751122,3.35771229 4.41630528,3.3681193 C4.42116985,3.36987869 4.42551008,3.37146263 4.42984665,3.3730594 C4.4761162,3.39008583 4.52241276,3.4087674 4.56821184,3.42893807 C4.59406406,3.44033198 4.61917606,3.45191971 4.64412424,3.46396063 C4.67111495,3.47697976 4.69839649,3.4907848 4.72546291,3.50513959 C4.75890801,3.52288219 4.79178851,3.54132453 4.82431475,3.56059431 C4.8374698,3.56838641 4.85073285,3.5764165 4.86393439,3.58458539 C4.89491851,3.60376145 4.92539479,3.6235868 4.95550936,3.64416832 C4.9772823,3.65904443 4.99913454,3.67451232 5.02078256,3.69038541 C5.03998798,3.70447076 5.05881967,3.71870909 5.07748715,3.73325923 C5.10440445,3.75423289 5.13126725,3.7760983 5.15775949,3.79862613 C5.1821715,3.81939236 5.20595148,3.84042939 5.22940861,3.86201411 C5.24512436,3.87647694 5.26059993,3.89109333 5.27592752,3.90595256 C5.28442786,3.91418351 5.29385225,3.92345739 5.30321896,3.9328241 L10.2031018,8.83270693 C10.255475,8.88508012 10.3065885,8.93859789 10.3564099,8.99321224 L10.2031018,8.83270693 C10.2748395,8.90444467 10.344214,8.97832987 10.4111413,9.05423915 C10.4223877,9.06699478 10.4335715,9.07981507 10.4446856,9.092692 C10.7663645,9.46539004 11.0297601,9.88553066 11.2252237,10.3388957 L11.6780206,11.3880225 L12.548286,13.4076516 C12.7467158,13.8678966 12.5344727,14.4018581 12.0742277,14.6002879 C11.9977866,14.6332447 11.9179446,14.6552159 11.836969,14.6662015 L11.7149387,14.6744406 C11.592625,14.6744406 11.4703113,14.6497231 11.3556497,14.6002879 L11.2340206,14.5480225 L9.33602055,13.7300225 L8.28689372,13.2772256 C7.83352871,13.081762 7.41338809,12.8183665 7.04069004,12.4966876 L7.0022372,12.4631433 C6.98177889,12.4451057 6.9614676,12.4268903 6.94130575,12.4084989 L7.04069004,12.4966876 C6.95122931,12.4194733 6.86450207,12.3389008 6.78070498,12.2551038 L1.88082214,7.35522092 C0.935753358,6.41015213 0.935753358,4.87789288 1.88082214,3.9328241 L1.90902055,3.90502251 L2.01192506,3.8109306 C2.19120357,3.65606766 2.38780913,3.5318516 2.59488381,3.4382824 C2.62872186,3.42311621 2.65522016,3.41182111 2.68187195,3.40102033 C2.76025666,3.36925866 2.83986347,3.34180278 2.92043821,3.31861145 L3.07557525,3.27946831 Z M9.58610551,9.95149698 L7.89951995,11.6381324 C8.10279642,11.805046 8.32371441,11.9494547 8.55841217,12.068738 L8.76594574,12.166096 L10.2570206,12.8090225 L10.7570206,12.3090225 L10.114094,10.8179477 C9.97930356,10.5053101 9.80144069,10.2137385 9.58610551,9.95149698 Z"),o(l,"id","Combined-Shape"),o(l,"fill-rule","nonzero"),o(s,"id","Rectangle"),o(s,"opacity","0.005"),o(s,"x","0"),o(s,"y","0"),o(s,"width","16"),o(s,"height","16"),o(s,"rx","2"),o(r,"id","Pencil_Base"),o(r,"fill",c=n[0]?n[1]:"#168AFF"),o(d,"id","Rectangle"),o(d,"x","2.25"),o(d,"y","0"),o(d,"width","1.5"),o(d,"height","6"),o(d,"rx","0.75"),o(C,"id","Rectangle-Copy"),o(C,"transform","translate(3, 3) rotate(90) translate(-3, -3)"),o(C,"x","2.25"),o(C,"y","1.13686838e-13"),o(C,"width","1.5"),o(C,"height","6"),o(C,"rx","0.75"),o(u,"id","Group"),o(u,"transform","translate(12, 4) rotate(45) translate(-12, -4)translate(9, 1)"),o(u,"fill",g=n[0]?n[1]:"#FF7E72"),o(i,"id","nextedit_rejected_dark"),o(i,"stroke","none"),o(i,"stroke-width","1"),o(i,"fill","none"),o(i,"fill-rule","evenodd"),o(t,"width","16"),o(t,"height","16"),o(t,"viewBox","0 0 16 16"),o(t,"version","1.1"),o(t,"xmlns","http://www.w3.org/2000/svg"),o(t,"xmlns:xlink","http://www.w3.org/1999/xlink")},m(h,p){A(h,t,p),f(t,e),f(e,a),f(t,i),f(i,r),f(r,l),f(r,s),f(i,u),f(u,d),f(u,C)},p(h,[p]){3&p&&c!==(c=h[0]?h[1]:"#168AFF")&&o(r,"fill",c),3&p&&g!==(g=h[0]?h[1]:"#FF7E72")&&o(u,"fill",g)},i:_,o:_,d(h){h&&S(t)}}}function n4(n,t,e){let{mask:a=!0}=t,{maskColor:i="white"}=t;return n.$$set=r=>{"mask"in r&&e(0,a=r.mask),"maskColor"in r&&e(1,i=r.maskColor)},[a,i]}class e4 extends O{constructor(t){super(),R(this,t,n4,t4,B,{mask:0,maskColor:1})}}function o4(n){let t,e,a,i,r,l,s,c,u,d,C;return{c(){t=m("svg"),e=m("title"),a=Z("Option 2_light"),i=m("g"),r=m("path"),s=m("g"),c=m("path"),d=m("path"),o(r,"d","M11.0070258,6 C11.1334895,6 11.2318501,5.90866511 11.2529274,5.76814988 C11.5409836,3.95550351 11.8641686,3.52693208 13.7751756,3.2529274 C13.9156909,3.23185012 14,3.13348946 14,3 C14,2.8735363 13.9156909,2.77517564 13.7751756,2.75409836 C11.8571429,2.48009368 11.618267,2.07259953 11.2529274,0.217798595 C11.2248244,0.0913348946 11.1334895,4.88498131e-15 11.0070258,4.88498131e-15 C10.8735363,4.88498131e-15 10.7751756,0.0913348946 10.7540984,0.224824356 C10.4660422,2.07259953 10.1498829,2.48009368 8.23887588,2.75409836 C8.09836066,2.78220141 8.00702576,2.8735363 8.00702576,3 C8.00702576,3.13348946 8.09836066,3.23185012 8.23887588,3.2529274 C10.1569087,3.52693208 10.4028103,3.92740047 10.7540984,5.77517564 C10.7822014,5.91569087 10.8805621,6 11.0070258,6 Z"),o(r,"id","Path"),o(r,"fill",l=n[0]?n[1]:"#007AFF"),o(c,"d","M4.81096943,2.24362978 L8.60328164,6.03648403 C8.98206926,6.41527164 9.28555422,6.86248408 9.4976383,7.35439917 L10.8207006,10.4231551 L7.35439917,9.4976383 C6.86248408,9.28555422 6.41527164,8.98206926 6.03645345,8.60325107 L2.24362978,4.81096943 L4.81096943,2.24362978 Z M2.40551485,0.605057322 C2.66486762,0.603004601 2.92459099,0.65617863 3.16550077,0.764612139 L0.782130647,3.14739928 C0.671050497,2.89397491 0.616248167,2.62929203 0.618310403,2.36812709 C0.621891891,1.91456136 0.798131359,1.47507103 1.13660119,1.13660119 C1.48731739,0.785884996 1.94585368,0.608695442 2.40551485,0.605057322 Z"),o(c,"id","Combined-Shape"),o(c,"stroke",u=n[0]?n[1]:"#007AFF"),o(c,"stroke-width","1.21"),o(d,"d","M10.506,8.164 L11.3762654,10.1836291 C11.5746952,10.6438741 11.3624521,11.1778356 10.9022072,11.3762654 C10.6728839,11.4751357 10.4129523,11.4751357 10.1836291,11.3762654 L8.164,10.506 L10.506,8.164 Z M4.13119841,0.708801589 L9.03108125,5.60868442 C9.11487834,5.69248152 9.19545077,5.77920876 9.27266509,5.86866949 L5.86866949,9.27266509 C5.77920876,9.19545077 5.69248152,9.11487834 5.60868442,9.03108125 L0.708801589,4.13119841 C-0.236267196,3.18612962 -0.236267196,1.65387038 0.708801589,0.708801589 C1.65387038,-0.236267196 3.18612962,-0.236267196 4.13119841,0.708801589 Z"),o(d,"id","Combined-Shape"),o(d,"fill",C=n[0]?n[1]:"#007AFF"),o(s,"id","Pencil"),o(s,"transform","translate(0.172, 2.224)"),o(i,"id","Option-2_light"),o(i,"stroke","none"),o(i,"stroke-width","1"),o(i,"fill","none"),o(i,"fill-rule","evenodd"),o(t,"width","16"),o(t,"height","16"),o(t,"viewBox","0 0 16 16"),o(t,"version","1.1"),o(t,"xmlns","http://www.w3.org/2000/svg"),o(t,"xmlns:xlink","http://www.w3.org/1999/xlink")},m(g,h){A(g,t,h),f(t,e),f(e,a),f(t,i),f(i,r),f(i,s),f(s,c),f(s,d)},p(g,[h]){3&h&&l!==(l=g[0]?g[1]:"#007AFF")&&o(r,"fill",l),3&h&&u!==(u=g[0]?g[1]:"#007AFF")&&o(c,"stroke",u),3&h&&C!==(C=g[0]?g[1]:"#007AFF")&&o(d,"fill",C)},i:_,o:_,d(g){g&&S(t)}}}function i4(n,t,e){let{mask:a=!1}=t,{maskColor:i="white"}=t;return n.$$set=r=>{"mask"in r&&e(0,a=r.mask),"maskColor"in r&&e(1,i=r.maskColor)},[a,i]}class r4 extends O{constructor(t){super(),R(this,t,i4,o4,B,{mask:0,maskColor:1})}}function a4(n){let t,e,a,i,r,l,s,c,u,d,C;return{c(){t=m("svg"),e=m("title"),a=Z("Option 2_dark"),i=m("g"),r=m("path"),s=m("g"),c=m("path"),d=m("path"),o(r,"d","M11.0070258,6 C11.1334895,6 11.2318501,5.90866511 11.2529274,5.76814988 C11.5409836,3.95550351 11.8641686,3.52693208 13.7751756,3.2529274 C13.9156909,3.23185012 14,3.13348946 14,3 C14,2.8735363 13.9156909,2.77517564 13.7751756,2.75409836 C11.8571429,2.48009368 11.618267,2.07259953 11.2529274,0.217798595 C11.2248244,0.0913348946 11.1334895,4.88498131e-15 11.0070258,4.88498131e-15 C10.8735363,4.88498131e-15 10.7751756,0.0913348946 10.7540984,0.224824356 C10.4660422,2.07259953 10.1498829,2.48009368 8.23887588,2.75409836 C8.09836066,2.78220141 8.00702576,2.8735363 8.00702576,3 C8.00702576,3.13348946 8.09836066,3.23185012 8.23887588,3.2529274 C10.1569087,3.52693208 10.4028103,3.92740047 10.7540984,5.77517564 C10.7822014,5.91569087 10.8805621,6 11.0070258,6 Z"),o(r,"id","Path"),o(r,"fill",l=n[0]?n[1]:"#BF5AF2"),o(c,"d","M4.81096943,2.24362978 L8.60328164,6.03648403 C8.98206926,6.41527164 9.28555422,6.86248408 9.4976383,7.35439917 L10.8207006,10.4231551 L7.35439917,9.4976383 C6.86248408,9.28555422 6.41527164,8.98206926 6.03645345,8.60325107 L2.24362978,4.81096943 L4.81096943,2.24362978 Z M2.40551485,0.605057322 C2.66486762,0.603004601 2.92459099,0.65617863 3.16550077,0.764612139 L0.782130647,3.14739928 C0.671050497,2.89397491 0.616248167,2.62929203 0.618310403,2.36812709 C0.621891891,1.91456136 0.798131359,1.47507103 1.13660119,1.13660119 C1.48731739,0.785884996 1.94585368,0.608695442 2.40551485,0.605057322 Z"),o(c,"id","Combined-Shape"),o(c,"stroke",u=n[0]?n[1]:"#389BFF"),o(c,"stroke-width","1.21"),o(d,"d","M10.506,8.164 L11.3762654,10.1836291 C11.5746952,10.6438741 11.3624521,11.1778356 10.9022072,11.3762654 C10.6728839,11.4751357 10.4129523,11.4751357 10.1836291,11.3762654 L8.164,10.506 L10.506,8.164 Z M4.13119841,0.708801589 L9.03108125,5.60868442 C9.11487834,5.69248152 9.19545077,5.77920876 9.27266509,5.86866949 L5.86866949,9.27266509 C5.77920876,9.19545077 5.69248152,9.11487834 5.60868442,9.03108125 L0.708801589,4.13119841 C-0.236267196,3.18612962 -0.236267196,1.65387038 0.708801589,0.708801589 C1.65387038,-0.236267196 3.18612962,-0.236267196 4.13119841,0.708801589 Z"),o(d,"id","Combined-Shape"),o(d,"fill",C=n[0]?n[1]:"#389BFF"),o(s,"id","Pencil"),o(s,"transform","translate(0.172, 2.224)"),o(i,"id","Option-2_dark"),o(i,"stroke","none"),o(i,"stroke-width","1"),o(i,"fill","none"),o(i,"fill-rule","evenodd"),o(t,"width","16"),o(t,"height","16"),o(t,"viewBox","0 0 16 16"),o(t,"version","1.1"),o(t,"xmlns","http://www.w3.org/2000/svg"),o(t,"xmlns:xlink","http://www.w3.org/1999/xlink")},m(g,h){A(g,t,h),f(t,e),f(e,a),f(t,i),f(i,r),f(i,s),f(s,c),f(s,d)},p(g,[h]){3&h&&l!==(l=g[0]?g[1]:"#BF5AF2")&&o(r,"fill",l),3&h&&u!==(u=g[0]?g[1]:"#389BFF")&&o(c,"stroke",u),3&h&&C!==(C=g[0]?g[1]:"#389BFF")&&o(d,"fill",C)},i:_,o:_,d(g){g&&S(t)}}}function s4(n,t,e){let{mask:a=!1}=t,{maskColor:i="white"}=t;return n.$$set=r=>{"mask"in r&&e(0,a=r.mask),"maskColor"in r&&e(1,i=r.maskColor)},[a,i]}class l4 extends O{constructor(t){super(),R(this,t,s4,a4,B,{mask:0,maskColor:1})}}function c4(n){let t,e,a,i,r,l,s,c,u;return{c(){t=m("svg"),e=m("title"),a=Z("Option 2_Inactive_light"),i=m("g"),r=m("path"),s=m("g"),c=m("path"),o(r,"d","M11.0070258,6 C11.1334895,6 11.2318501,5.90866511 11.2529274,5.76814988 C11.5409836,3.95550351 11.8641686,3.52693208 13.7751756,3.2529274 C13.9156909,3.23185012 14,3.13348946 14,3 C14,2.8735363 13.9156909,2.77517564 13.7751756,2.75409836 C11.8571429,2.48009368 11.618267,2.07259953 11.2529274,0.217798595 C11.2248244,0.0913348946 11.1334895,4.88498131e-15 11.0070258,4.88498131e-15 C10.8735363,4.88498131e-15 10.7751756,0.0913348946 10.7540984,0.224824356 C10.4660422,2.07259953 10.1498829,2.48009368 8.23887588,2.75409836 C8.09836066,2.78220141 8.00702576,2.8735363 8.00702576,3 C8.00702576,3.13348946 8.09836066,3.23185012 8.23887588,3.2529274 C10.1569087,3.52693208 10.4028103,3.92740047 10.7540984,5.77517564 C10.7822014,5.91569087 10.8805621,6 11.0070258,6 Z"),o(r,"id","Path"),o(r,"fill",l=n[0]?n[1]:"#BF5AF2"),o(c,"d","M2.42,0.605 C2.88449901,0.605 3.34899801,0.782200397 3.70339881,1.13660119 L8.60328164,6.03648403 C8.98206926,6.41527164 9.28555422,6.86248408 9.4976383,7.35439917 L10.8207006,10.4231551 L7.35439917,9.4976383 C6.86248408,9.28555422 6.41527164,8.98206926 6.03648403,8.60328164 L1.13660119,3.70339881 C0.782200397,3.34899801 0.605,2.88449901 0.605,2.42 C0.605,1.95550099 0.782200397,1.49100199 1.13660119,1.13660119 C1.49100199,0.782200397 1.95550099,0.605 2.42,0.605 Z"),o(c,"id","Combined-Shape"),o(s,"id","Pencil"),o(s,"transform","translate(0.172, 2.224)"),o(s,"stroke",u=n[0]?n[1]:"#007AFF"),o(s,"stroke-width","1.21"),o(i,"id","Option-2_Inactive_light"),o(i,"stroke","none"),o(i,"stroke-width","1"),o(i,"fill","none"),o(i,"fill-rule","evenodd"),o(t,"width","16"),o(t,"height","16"),o(t,"viewBox","0 0 16 16"),o(t,"version","1.1"),o(t,"xmlns","http://www.w3.org/2000/svg"),o(t,"xmlns:xlink","http://www.w3.org/1999/xlink")},m(d,C){A(d,t,C),f(t,e),f(e,a),f(t,i),f(i,r),f(i,s),f(s,c)},p(d,[C]){3&C&&l!==(l=d[0]?d[1]:"#BF5AF2")&&o(r,"fill",l),3&C&&u!==(u=d[0]?d[1]:"#007AFF")&&o(s,"stroke",u)},i:_,o:_,d(d){d&&S(t)}}}function u4(n,t,e){let{mask:a=!1}=t,{maskColor:i="white"}=t;return n.$$set=r=>{"mask"in r&&e(0,a=r.mask),"maskColor"in r&&e(1,i=r.maskColor)},[a,i]}class d4 extends O{constructor(t){super(),R(this,t,u4,c4,B,{mask:0,maskColor:1})}}function C4(n){let t,e,a,i,r,l,s,c,u;return{c(){t=m("svg"),e=m("title"),a=Z("Option 2_inactive_dark"),i=m("g"),r=m("path"),s=m("g"),c=m("path"),o(r,"d","M11.0070258,6 C11.1334895,6 11.2318501,5.90866511 11.2529274,5.76814988 C11.5409836,3.95550351 11.8641686,3.52693208 13.7751756,3.2529274 C13.9156909,3.23185012 14,3.13348946 14,3 C14,2.8735363 13.9156909,2.77517564 13.7751756,2.75409836 C11.8571429,2.48009368 11.618267,2.07259953 11.2529274,0.217798595 C11.2248244,0.0913348946 11.1334895,4.88498131e-15 11.0070258,4.88498131e-15 C10.8735363,4.88498131e-15 10.7751756,0.0913348946 10.7540984,0.224824356 C10.4660422,2.07259953 10.1498829,2.48009368 8.23887588,2.75409836 C8.09836066,2.78220141 8.00702576,2.8735363 8.00702576,3 C8.00702576,3.13348946 8.09836066,3.23185012 8.23887588,3.2529274 C10.1569087,3.52693208 10.4028103,3.92740047 10.7540984,5.77517564 C10.7822014,5.91569087 10.8805621,6 11.0070258,6 Z"),o(r,"id","Path"),o(r,"fill",l=n[0]?n[1]:"#BF5AF2"),o(c,"d","M2.42,0.605 C2.88449901,0.605 3.34899801,0.782200397 3.70339881,1.13660119 L8.60328164,6.03648403 C8.98206926,6.41527164 9.28555422,6.86248408 9.4976383,7.35439917 L10.8207006,10.4231551 L7.35439917,9.4976383 C6.86248408,9.28555422 6.41527164,8.98206926 6.03648403,8.60328164 L1.13660119,3.70339881 C0.782200397,3.34899801 0.605,2.88449901 0.605,2.42 C0.605,1.95550099 0.782200397,1.49100199 1.13660119,1.13660119 C1.49100199,0.782200397 1.95550099,0.605 2.42,0.605 Z"),o(c,"id","Combined-Shape"),o(s,"id","Pencil"),o(s,"transform","translate(0.172, 2.224)"),o(s,"stroke",u=n[0]?n[1]:"#389BFF"),o(s,"stroke-width","1.21"),o(i,"id","Option-2_inactive_dark"),o(i,"stroke","none"),o(i,"stroke-width","1"),o(i,"fill","none"),o(i,"fill-rule","evenodd"),o(t,"width","16"),o(t,"height","16"),o(t,"viewBox","0 0 16 16"),o(t,"version","1.1"),o(t,"xmlns","http://www.w3.org/2000/svg"),o(t,"xmlns:xlink","http://www.w3.org/1999/xlink")},m(d,C){A(d,t,C),f(t,e),f(e,a),f(t,i),f(i,r),f(i,s),f(s,c)},p(d,[C]){3&C&&l!==(l=d[0]?d[1]:"#BF5AF2")&&o(r,"fill",l),3&C&&u!==(u=d[0]?d[1]:"#389BFF")&&o(s,"stroke",u)},i:_,o:_,d(d){d&&S(t)}}}function h4(n,t,e){let{mask:a=!1}=t,{maskColor:i="white"}=t;return n.$$set=r=>{"mask"in r&&e(0,a=r.mask),"maskColor"in r&&e(1,i=r.maskColor)},[a,i]}class g4 extends O{constructor(t){super(),R(this,t,h4,C4,B,{mask:0,maskColor:1})}}function f4(n){let t,e,a,i;var r=n[2];function l(u,d){return{props:{mask:u[0],maskColor:u[1]}}}r&&(e=F3(r,l(n)));const s=n[9].default,c=_1(s,n,n[8],null);return{c(){t=z("span"),e&&D(e.$$.fragment),a=K(),c&&c.c(),o(t,"class","c-pencil-icon svelte-6fsamc")},m(u,d){A(u,t,d),e&&T(e,t,null),f(t,a),c&&c.m(t,null),i=!0},p(u,[d]){if(4&d&&r!==(r=u[2])){if(e){j1();const C=e;F(C.$$.fragment,1,0,()=>{I(C,1)}),M1()}r?(e=F3(r,l(u)),D(e.$$.fragment),$(e.$$.fragment,1),T(e,t,a)):e=null}else if(r){const C={};1&d&&(C.mask=u[0]),2&d&&(C.maskColor=u[1]),e.$set(C)}c&&c.p&&(!i||256&d)&&$1(c,s,u,u[8],i?F1(s,u[8],d,null):A1(u[8]),null)},i(u){i||(e&&$(e.$$.fragment,u),$(c,u),i=!0)},o(u){e&&F(e.$$.fragment,u),F(c,u),i=!1},d(u){u&&S(t),e&&I(e),c&&c.d(u)}}}function p4(n,t,e){let a,i,r;I1(n,L2,p=>e(7,r=p));let{$$slots:l={},$$scope:s}=t;const c={insertion:{light:M0,dark:R0},deletion:{light:z0,dark:D0},modification:{light:G3,dark:U3},noop:{light:G3,dark:U3},active:{light:r4,dark:l4},inactive:{light:d4,dark:g4},accepted:{light:W0,dark:J0},rejected:{light:Y0,dark:e4}};let u,{mask:d=!1}=t,{maskColor:C="currentColor"}=t,{suggestion:g}=t,{themeCategory:h}=t;return n.$$set=p=>{"mask"in p&&e(0,d=p.mask),"maskColor"in p&&e(1,C=p.maskColor),"suggestion"in p&&e(4,g=p.suggestion),"themeCategory"in p&&e(3,h=p.themeCategory),"$$scope"in p&&e(8,s=p.$$scope)},n.$$.update=()=>{136&n.$$.dirty&&(r!=null&&r.category)&&e(3,h??(h=r.category)),16&n.$$.dirty&&e(5,u=function(p){return new V1(p,u3)}(g).with({state:r3.stale},{state:r3.accepted},()=>"accepted").otherwise(({changeType:p})=>p)),32&n.$$.dirty&&e(6,a=c[u]??c.active),72&n.$$.dirty&&e(2,i=a[h??T2.light])},[d,C,i,h,g,u,a,r,s,l]}class m4 extends O{constructor(t){super(),R(this,t,p4,f4,B,{mask:0,maskColor:1,suggestion:4,themeCategory:3})}}function v4(n){let t,e,a,i,r,l,s,c,u,d,C,g,h,p,L,w,x,y,v,X,C1,Y=n[1].result.changeDescription+"";return c=new m4({props:{mask:n[11]!=="none",suggestion:n[1]}}),g=new k2({props:{compact:!0,actions:n[2],onAction:n[3],value:n[1]}}),{c(){t=z("div"),a=K(),i=z("div"),r=z("div"),l=z("div"),s=z("button"),D(c.$$.fragment),u=K(),d=Z(Y),C=K(),D(g.$$.fragment),h=K(),p=z("div"),L=z("div"),o(t,"class","c-code-roll-suggestion-window__view-zone"),N(t,"--augment-code-roll-left-alignment",n[9]+"px"),o(s,"class","c-code-roll-suggestion-window__item-title-text svelte-kis6hx"),o(s,"tabindex","0"),o(l,"class","c-code-roll-suggestion-window__item-title svelte-kis6hx"),N(l,"height",n[6]+"px"),o(L,"class","c-code-roll-suggestion-window__window svelte-kis6hx"),N(L,"height",n[10]+"px"),o(p,"class","c-code-roll-suggestion-window__border svelte-kis6hx"),o(r,"class","c-code-roll-suggestion-window__item svelte-kis6hx"),o(r,"style",w=q3(l3(n[1],n[7],!1))),N(i,"--augment-code-roll-left-alignment",n[9]+"px"),N(i,"--augment-code-roll-suggestion-window-line-height",n[6]+"px"),N(i,"top",n[8]+"px"),o(i,"class","c-code-roll-suggestion-window svelte-kis6hx"),o(i,"data-result-id",y=`${n[1].result.suggestionId}:${n[1].requestId}`)},m(P,j){A(P,t,j),A(P,a,j),A(P,i,j),f(i,r),f(r,l),f(l,s),T(c,s,null),f(s,u),f(s,d),f(l,C),T(g,l,null),f(r,h),f(r,p),f(p,L),v=!0,X||(C1=[T1(e=y0.call(null,t,{editor:n[0],afterLineNumber:n[4],heightInPx:n[6],onDomNodeTop:n[16]})),d1(s,"keydown",function(){k1(i3("Enter",N1(n[3],"active",n[1])))&&i3("Enter",N1(n[3],"active",n[1])).apply(this,arguments)}),d1(s,"click",function(){k1(N1(n[3],"active",n[1]))&&N1(n[3],"active",n[1]).apply(this,arguments)}),T1(x=b0.call(null,r,{scrollContainer:n[5],doScroll:x1(n[1],I3(n[7])),scrollIntoView:{behavior:"smooth",block:"center"},useSmartBlockAlignment:!0}))],X=!0)},p(P,[j]){n=P,(!v||512&j)&&N(t,"--augment-code-roll-left-alignment",n[9]+"px"),e&&k1(e.update)&&337&j&&e.update.call(null,{editor:n[0],afterLineNumber:n[4],heightInPx:n[6],onDomNodeTop:n[16]});const t1={};2048&j&&(t1.mask=n[11]!=="none"),2&j&&(t1.suggestion=n[1]),c.$set(t1),(!v||2&j)&&Y!==(Y=n[1].result.changeDescription+"")&&I2(d,Y);const l1={};4&j&&(l1.actions=n[2]),8&j&&(l1.onAction=n[3]),2&j&&(l1.value=n[1]),g.$set(l1),(!v||64&j)&&N(l,"height",n[6]+"px"),(!v||1024&j)&&N(L,"height",n[10]+"px"),(!v||130&j&&w!==(w=q3(l3(n[1],n[7],!1))))&&o(r,"style",w),x&&k1(x.update)&&162&j&&x.update.call(null,{scrollContainer:n[5],doScroll:x1(n[1],I3(n[7])),scrollIntoView:{behavior:"smooth",block:"center"},useSmartBlockAlignment:!0}),(!v||512&j)&&N(i,"--augment-code-roll-left-alignment",n[9]+"px"),(!v||64&j)&&N(i,"--augment-code-roll-suggestion-window-line-height",n[6]+"px"),(!v||256&j)&&N(i,"top",n[8]+"px"),(!v||2&j&&y!==(y=`${n[1].result.suggestionId}:${n[1].requestId}`))&&o(i,"data-result-id",y)},i(P){v||($(c.$$.fragment,P),$(g.$$.fragment,P),v=!0)},o(P){F(c.$$.fragment,P),F(g.$$.fragment,P),v=!1},d(P){P&&(S(t),S(a),S(i)),I(c),I(g),X=!1,w2(C1)}}}function w4(n,t,e){let a,i,r,l,s,c,u,{diffEditor:d}=t,{suggestion:C}=t,{codeActions:g=[]}=t,{onCodeAction:h}=t,{afterLineNumber:p}=t,{lineCount:L=0}=t,{scrollContainer:w}=t,x=0;const y=k0();return I1(n,y,v=>e(7,u=v)),n.$$set=v=>{"diffEditor"in v&&e(0,d=v.diffEditor),"suggestion"in v&&e(1,C=v.suggestion),"codeActions"in v&&e(2,g=v.codeActions),"onCodeAction"in v&&e(3,h=v.onCodeAction),"afterLineNumber"in v&&e(4,p=v.afterLineNumber),"lineCount"in v&&e(13,L=v.lineCount),"scrollContainer"in v&&e(5,w=v.scrollContainer)},n.$$.update=()=>{130&n.$$.dirty&&e(11,a=l3(C,u,!1)),1&n.$$.dirty&&e(6,i=d.getModifiedEditor().getOption(t0.EditorOption.lineHeight)),8256&n.$$.dirty&&e(10,r=L*i),2&n.$$.dirty&&e(15,l=C.changeType===M3.insertion||C.changeType===M3.modification),40976&n.$$.dirty&&e(14,s=l?p+L:p),16384&n.$$.dirty&&e(9,c=s>999?0:5)},[d,C,g,h,p,w,i,u,x,c,r,a,y,L,s,l,function(v){e(8,x=v)}]}class L4 extends O{constructor(t){super(),R(this,t,w4,v4,B,{diffEditor:0,suggestion:1,codeActions:2,onCodeAction:3,afterLineNumber:4,lineCount:13,scrollContainer:5})}}function W3(n,t,e){const a=n.slice();return a[29]=t[e][0],a[30]=t[e][1],a}function y4(n){let t,e,a=j3(s3(n[2],n[8])),i=[];for(let l=0;l<a.length;l+=1)i[l]=H3(W3(n,a,l));const r=l=>F(i[l],1,1,()=>{i[l]=null});return{c(){for(let l=0;l<i.length;l+=1)i[l].c();t=v2()},m(l,s){for(let c=0;c<i.length;c+=1)i[c]&&i[c].m(l,s);A(l,t,s),e=!0},p(l,s){if(380&s[0]){let c;for(a=j3(s3(l[2],l[8])),c=0;c<a.length;c+=1){const u=W3(l,a,c);i[c]?(i[c].p(u,s),$(i[c],1)):(i[c]=H3(u),i[c].c(),$(i[c],1),i[c].m(t.parentNode,t))}for(j1(),c=a.length;c<i.length;c+=1)r(c);M1()}},i(l){if(!e){for(let s=0;s<a.length;s+=1)$(i[s]);e=!0}},o(l){i=i.filter(Boolean);for(let s=0;s<i.length;s+=1)F(i[s]);e=!1},d(l){l&&S(t),U2(i,l)}}}function k4(n){let t,e,a;return e=new y2({}),{c(){t=z("div"),D(e.$$.fragment),o(t,"class","c-diff-view__loading")},m(i,r){A(i,t,r),T(e,t,null),a=!0},p:_,i(i){a||($(e.$$.fragment,i),a=!0)},o(i){F(e.$$.fragment,i),a=!1},d(i){i&&S(t),I(e)}}}function H3(n){let t,e;const a=[{diffEditor:n[6]},{suggestion:n[29]},{codeActions:n[3]},{onCodeAction:n[4]},a3(n[30]),{scrollContainer:n[5]}];let i={};for(let r=0;r<a.length;r+=1)i=b1(i,a[r]);return t=new L4({props:i}),{c(){D(t.$$.fragment)},m(r,l){T(t,r,l),e=!0},p(r,l){const s=380&l[0]?v3(a,[64&l[0]&&{diffEditor:r[6]},260&l[0]&&{suggestion:r[29]},8&l[0]&&{codeActions:r[3]},16&l[0]&&{onCodeAction:r[4]},260&l[0]&&W2(a3(r[30])),32&l[0]&&{scrollContainer:r[5]}]):{};t.$set(s)},i(r){e||($(t.$$.fragment,r),e=!0)},o(r){F(t.$$.fragment,r),e=!1},d(r){I(t,r)}}}function x4(n){let t,e,a,i,r,l,s,c,u;const d=[k4,y4],C=[];function g(h,p){return h[1]||!h[6]?0:1}return r=g(n),l=C[r]=d[r](n),{c(){t=z("div"),e=z("div"),i=K(),l.c(),o(e,"class","c-diff-view svelte-syu9kz"),N(e,"display",n[1]?"none":"flex"),o(t,"class","c-diff-view__container svelte-syu9kz"),N(t,"--augment-codeblock-min-height",n[0]+"px"),r1(t,"has-top-decorations",n[9])},m(h,p){A(h,t,p),f(t,e),n[21](e),f(t,i),C[r].m(t,null),s=!0,c||(u=T1(a=n0.call(null,e,{onResize:n[20]})),c=!0)},p(h,p){a&&k1(a.update)&&64&p[0]&&a.update.call(null,{onResize:h[20]}),2&p[0]&&N(e,"display",h[1]?"none":"flex");let L=r;r=g(h),r===L?C[r].p(h,p):(j1(),F(C[L],1,1,()=>{C[L]=null}),M1(),l=C[r],l?l.p(h,p):(l=C[r]=d[r](h),l.c()),$(l,1),l.m(t,null)),(!s||1&p[0])&&N(t,"--augment-codeblock-min-height",h[0]+"px"),(!s||512&p[0])&&r1(t,"has-top-decorations",h[9])},i(h){s||($(l),s=!0)},o(h){F(l),s=!1},d(h){h&&S(t),n[21](null),C[r].d(),c=!1,u()}}}function b4(n,t,e){let a,i,r;I1(n,L2,k=>e(19,r=k));let{height:l=500}=t,{language:s}=t,{originalCode:c}=t,{suggestions:u}=t,{codeActions:d=[]}=t,{onCodeAction:C=m1}=t,{path:g}=t,{busy:h=!0}=t,{expanded:p=!1}=t,{scrollContainer:L}=t,{options:w={enableSplitViewResizing:!1,automaticLayout:!0,readOnly:!0,overviewRulerLanes:0,lineHeight:20,renderLineHighlight:"none",contextmenu:!1,renderSideBySide:!1,renderIndicators:!0,renderMarginRevertIcon:!1,originalEditable:!1,diffCodeLens:!1,renderOverviewRuler:!1,ignoreTrimWhitespace:!1,maxComputationTime:3e3,scrollBeyondLastColumn:0,scrollBeyondLastLine:!1,scrollPredominantAxis:!1,scrollbar:{alwaysConsumeMouseWheel:!1,vertical:"hidden",horizontal:"hidden"},cursorSurroundingLines:0,cursorSurroundingLinesStyle:"all",hideUnchangedRegions:{enabled:!p,revealLineCount:5,minimumLineCount:3,contextLineCount:5},lineNumbers:String,hover:{enabled:!1}}}=t;const x=e0.getContext().monaco;I1(n,x,k=>e(18,i=k));let y,v,X,C1=P3(u),Y="",P=[],j=document.createElement("div");j.classList.add("c-diff-view__editor");let t1=[],l1=!1;function y3(k){const q=function(L1,{enabled:E1=!0,revealLineCount:c1=5,minimumLineCount:B2=3,contextLineCount:Q1=5}={enabled:!0,revealLineCount:5,minimumLineCount:3,contextLineCount:5},x3,O1){const N2={lines:0,decorations:0,hasTopDecorations:!1};if(!L1.length||!E1)return N2;let R1=0,b3=!1;const X1=[...L1].sort((y1,B1)=>y1.lineRange.start-B1.lineRange.start);let G=1;for(let y1=0,B1=1;y1<X1.length;y1++,B1++){const J=X1[y1],_3=X1[B1];if(G+=Math.min(J.lineRange.start+1,c1),G+=O1!=null&&O1.get(J)?a3(O1.get(J)).lineCount:Q2(J),J.lineRange.start-c1>1?(b3=!0,R1++):J.lineRange.start-c1==1&&G++,_3){const $3=_3.lineRange.start-J.lineRange.start;$3>Q1+c1?(R1++,G+=Q1,G+=c1):G+=$3}else J.lineRange.stop<x3?(R1++,G+=Math.min(x3-J.lineRange.stop,c1)):(G+=Q1,G+=c1)}return{lines:Math.max(G,B2),decorations:R1,hasTopDecorations:b3}}(u,w.hideUnchangedRegions,X2(c),t1.length>0?s3(u,t1):void 0);e(0,l=p&&k?k.getModifiedEditor().getContentHeight():q.lines*(w.lineHeight??20)+24*q.decorations),e(9,l1=q.hasTopDecorations)}function k3(){var k,q,L1,E1;(q=(k=v==null?void 0:v.getModel())==null?void 0:k.original)==null||q.dispose(),(E1=(L1=v==null?void 0:v.getModel())==null?void 0:L1.modified)==null||E1.dispose(),i&&(v||(e(6,v=i.editor.createDiffEditor(j,a)),P.push(v)),v.onDidDispose(()=>e(6,v=void 0)),P.push(...p0(v,u,c,s,g,i)),y3(v),X==null||X.dispose(),X=v.onDidUpdateDiff(()=>{e(1,h=!1),e(8,t1=(v==null?void 0:v.getLineChanges())??[]),y3(v)}))}return q2(()=>{y.appendChild(j),k3()}),V2(()=>{P.forEach(k=>{var q;return(q=k==null?void 0:k.dispose)==null?void 0:q.call(k)}),j.remove()}),n.$$set=k=>{"height"in k&&e(0,l=k.height),"language"in k&&e(11,s=k.language),"originalCode"in k&&e(12,c=k.originalCode),"suggestions"in k&&e(2,u=k.suggestions),"codeActions"in k&&e(3,d=k.codeActions),"onCodeAction"in k&&e(4,C=k.onCodeAction),"path"in k&&e(13,g=k.path),"busy"in k&&e(1,h=k.busy),"expanded"in k&&e(14,p=k.expanded),"scrollContainer"in k&&e(5,L=k.scrollContainer),"options"in k&&e(15,w=k.options)},n.$$.update=()=>{if(557056&n.$$.dirty[0]&&(a={...w,theme:E3(r==null?void 0:r.category,r==null?void 0:r.intensity)}),16448&n.$$.dirty[0]&&(v==null||v.updateOptions({hideUnchangedRegions:{enabled:!p,revealLineCount:5,minimumLineCount:3,contextLineCount:5}})),786496&n.$$.dirty[0]){const k=r,q=E3(k==null?void 0:k.category,k==null?void 0:k.intensity);i==null||i.editor.setTheme(q),v==null||v.getModifiedEditor().updateOptions({theme:q}),v==null||v.getOriginalEditor().updateOptions({theme:q}),v==null||v.layout()}if(200708&n.$$.dirty[0]){const k=P3(u);C1===k&&c===Y||(e(16,C1=k),e(17,Y=c),k3())}},[l,h,u,d,C,L,v,y,t1,l1,x,s,c,g,p,w,C1,Y,i,r,()=>v==null?void 0:v.layout(),function(k){G2[k?"unshift":"push"](()=>{y=k,e(7,y)})}]}class _4 extends O{constructor(t){super(),R(this,t,b4,x4,B,{height:0,language:11,originalCode:12,suggestions:2,codeActions:3,onCodeAction:4,path:13,busy:1,expanded:14,scrollContainer:5,options:15},null,[-1,-1])}}function $4(n){let t,e,a;return e=new y2({}),{c(){t=z("div"),D(e.$$.fragment),o(t,"class","c-code-roll-item__loading svelte-1i59d33")},m(i,r){A(i,t,r),T(e,t,null),a=!0},p:_,i(i){a||($(e.$$.fragment,i),a=!0)},o(i){F(e.$$.fragment,i),a=!1},d(i){i&&S(t),I(e)}}}function A4(n){let t,e;return t=new _4({props:{onCodeAction:n[5],codeActions:n[6],suggestions:n[0],originalCode:n[2],language:n[4],path:n[1].relPath,scrollContainer:n[7]}}),{c(){D(t.$$.fragment)},m(a,i){T(t,a,i),e=!0},p(a,i){const r={};32&i&&(r.onCodeAction=a[5]),64&i&&(r.codeActions=a[6]),1&i&&(r.suggestions=a[0]),4&i&&(r.originalCode=a[2]),16&i&&(r.language=a[4]),2&i&&(r.path=a[1].relPath),128&i&&(r.scrollContainer=a[7]),t.$set(r)},i(a){e||($(t.$$.fragment,a),e=!0)},o(a){F(t.$$.fragment,a),e=!1},d(a){I(t,a)}}}function F4(n){let t,e,a,i;const r=[A4,$4],l=[];function s(c,u){return c[3]?0:1}return e=s(n),a=l[e]=r[e](n),{c(){t=z("div"),a.c(),o(t,"class","c-code-roll-item-diff svelte-1i59d33")},m(c,u){A(c,t,u),l[e].m(t,null),i=!0},p(c,[u]){let d=e;e=s(c),e===d?l[e].p(c,u):(j1(),F(l[d],1,1,()=>{l[d]=null}),M1(),a=l[e],a?a.p(c,u):(a=l[e]=r[e](c),a.c()),$(a,1),a.m(t,null))},i(c){i||($(a),i=!0)},o(c){F(a),i=!1},d(c){c&&S(t),l[e].d()}}}function S4(n,t,e){let{suggestions:a}=t,{filepath:i}=t,{originalCode:r}=t,{loaded:l=!1}=t,{language:s=o0(i.relPath)}=t,{onCodeAction:c=m1}=t,{codeActions:u=[]}=t,{scrollContainer:d}=t;return n.$$set=C=>{"suggestions"in C&&e(0,a=C.suggestions),"filepath"in C&&e(1,i=C.filepath),"originalCode"in C&&e(2,r=C.originalCode),"loaded"in C&&e(3,l=C.loaded),"language"in C&&e(4,s=C.language),"onCodeAction"in C&&e(5,c=C.onCodeAction),"codeActions"in C&&e(6,u=C.codeActions),"scrollContainer"in C&&e(7,d=C.scrollContainer)},[a,i,r,l,s,c,u,d]}class j4 extends O{constructor(t){super(),R(this,t,S4,F4,B,{suggestions:0,filepath:1,originalCode:2,loaded:3,language:4,onCodeAction:5,codeActions:6,scrollContainer:7})}}var M4=function(){this.__data__=[],this.size=0},_2=function(n,t){return n===t||n!=n&&t!=t},E4=_2,W1=function(n,t){for(var e=n.length;e--;)if(E4(n[e][0],t))return e;return-1},O4=W1,R4=Array.prototype.splice,B4=function(n){var t=this.__data__,e=O4(t,n);return!(e<0)&&(e==t.length-1?t.pop():R4.call(t,e,1),--this.size,!0)},N4=W1,z4=function(n){var t=this.__data__,e=N4(t,n);return e<0?void 0:t[e][1]},P4=W1,Z4=function(n){return P4(this.__data__,n)>-1},D4=W1,T4=function(n,t){var e=this.__data__,a=D4(e,n);return a<0?(++this.size,e.push([n,t])):e[a][1]=t,this},I4=M4,q4=B4,V4=z4,G4=Z4,U4=T4;function h1(n){var t=-1,e=n==null?0:n.length;for(this.clear();++t<e;){var a=n[t];this.set(a[0],a[1])}}h1.prototype.clear=I4,h1.prototype.delete=q4,h1.prototype.get=V4,h1.prototype.has=G4,h1.prototype.set=U4;var H1=h1,W4=H1,H4=function(){this.__data__=new W4,this.size=0},K4=function(n){var t=this.__data__,e=t.delete(n);return this.size=t.size,e},J4=function(n){return this.__data__.get(n)},Q4=function(n){return this.__data__.has(n)},X4=G1,Y4=x2,K3,$2=function(n){if(!Y4(n))return!1;var t=X4(n);return t=="[object Function]"||t=="[object GeneratorFunction]"||t=="[object AsyncFunction]"||t=="[object Proxy]"},n3=s1["__core-js_shared__"],J3=(K3=/[^.]+$/.exec(n3&&n3.keys&&n3.keys.IE_PROTO||""))?"Symbol(src)_1."+K3:"",t5=function(n){return!!J3&&J3 in n},n5=Function.prototype.toString,A2=function(n){if(n!=null){try{return n5.call(n)}catch{}try{return n+""}catch{}}return""},e5=$2,o5=t5,i5=x2,r5=A2,a5=/^\[object .+?Constructor\]$/,s5=Function.prototype,l5=Object.prototype,c5=s5.toString,u5=l5.hasOwnProperty,d5=RegExp("^"+c5.call(u5).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),C5=function(n,t){return n==null?void 0:n[t]},h5=function(n){return!(!i5(n)||o5(n))&&(e5(n)?d5:a5).test(r5(n))},g5=C5,v1=function(n,t){var e=g5(n,t);return h5(e)?e:void 0},w3=v1(s1,"Map"),K1=v1(Object,"create"),Q3=K1,f5=function(){this.__data__=Q3?Q3(null):{},this.size=0},p5=function(n){var t=this.has(n)&&delete this.__data__[n];return this.size-=t?1:0,t},m5=K1,v5=Object.prototype.hasOwnProperty,w5=function(n){var t=this.__data__;if(m5){var e=t[n];return e==="__lodash_hash_undefined__"?void 0:e}return v5.call(t,n)?t[n]:void 0},L5=K1,y5=Object.prototype.hasOwnProperty,k5=function(n){var t=this.__data__;return L5?t[n]!==void 0:y5.call(t,n)},x5=K1,b5=function(n,t){var e=this.__data__;return this.size+=this.has(n)?0:1,e[n]=x5&&t===void 0?"__lodash_hash_undefined__":t,this},_5=f5,$5=p5,A5=w5,F5=k5,S5=b5;function g1(n){var t=-1,e=n==null?0:n.length;for(this.clear();++t<e;){var a=n[t];this.set(a[0],a[1])}}g1.prototype.clear=_5,g1.prototype.delete=$5,g1.prototype.get=A5,g1.prototype.has=F5,g1.prototype.set=S5;var X3=g1,j5=H1,M5=w3,E5=function(n){var t=typeof n;return t=="string"||t=="number"||t=="symbol"||t=="boolean"?n!=="__proto__":n===null},J1=function(n,t){var e=n.__data__;return E5(t)?e[typeof t=="string"?"string":"hash"]:e.map},O5=J1,R5=function(n){var t=O5(this,n).delete(n);return this.size-=t?1:0,t},B5=J1,N5=function(n){return B5(this,n).get(n)},z5=J1,P5=function(n){return z5(this,n).has(n)},Z5=J1,D5=function(n,t){var e=Z5(this,n),a=e.size;return e.set(n,t),this.size+=e.size==a?0:1,this},T5=function(){this.size=0,this.__data__={hash:new X3,map:new(M5||j5),string:new X3}},I5=R5,q5=N5,V5=P5,G5=D5;function f1(n){var t=-1,e=n==null?0:n.length;for(this.clear();++t<e;){var a=n[t];this.set(a[0],a[1])}}f1.prototype.clear=T5,f1.prototype.delete=I5,f1.prototype.get=q5,f1.prototype.has=V5,f1.prototype.set=G5;var F2=f1,U5=H1,W5=w3,H5=F2,K5=function(n,t){var e=this.__data__;if(e instanceof U5){var a=e.__data__;if(!W5||a.length<199)return a.push([n,t]),this.size=++e.size,this;e=this.__data__=new H5(a)}return e.set(n,t),this.size=e.size,this},J5=H1,Q5=H4,X5=K4,Y5=J4,t9=Q4,n9=K5;function p1(n){var t=this.__data__=new J5(n);this.size=t.size}p1.prototype.clear=Q5,p1.prototype.delete=X5,p1.prototype.get=Y5,p1.prototype.has=t9,p1.prototype.set=n9;var e9=p1,o9=F2,i9=function(n){return this.__data__.set(n,"__lodash_hash_undefined__"),this},r9=function(n){return this.__data__.has(n)};function Z1(n){var t=-1,e=n==null?0:n.length;for(this.__data__=new o9;++t<e;)this.add(n[t])}Z1.prototype.add=Z1.prototype.push=i9,Z1.prototype.has=r9;var a9=function(n,t){return n.has(t)},s9=Z1,l9=function(n,t){for(var e=-1,a=n==null?0:n.length;++e<a;)if(t(n[e],e,n))return!0;return!1},c9=a9,S2=function(n,t,e,a,i,r){var l=1&e,s=n.length,c=t.length;if(s!=c&&!(l&&c>s))return!1;var u=r.get(n),d=r.get(t);if(u&&d)return u==t&&d==n;var C=-1,g=!0,h=2&e?new s9:void 0;for(r.set(n,t),r.set(t,n);++C<s;){var p=n[C],L=t[C];if(a)var w=l?a(L,p,C,t,n,r):a(p,L,C,n,t,r);if(w!==void 0){if(w)continue;g=!1;break}if(h){if(!l9(t,function(x,y){if(!c9(h,y)&&(p===x||i(p,x,e,a,r)))return h.push(y)})){g=!1;break}}else if(p!==L&&!i(p,L,e,a,r)){g=!1;break}}return r.delete(n),r.delete(t),g},u9=function(n){var t=-1,e=Array(n.size);return n.forEach(function(a,i){e[++t]=[i,a]}),e},Y3=s1.Uint8Array,d9=_2,C9=S2,h9=u9,g9=function(n){var t=-1,e=Array(n.size);return n.forEach(function(a){e[++t]=a}),e},t2=O3?O3.prototype:void 0,e3=t2?t2.valueOf:void 0,f9=function(n,t,e,a,i,r,l){switch(e){case"[object DataView]":if(n.byteLength!=t.byteLength||n.byteOffset!=t.byteOffset)return!1;n=n.buffer,t=t.buffer;case"[object ArrayBuffer]":return!(n.byteLength!=t.byteLength||!r(new Y3(n),new Y3(t)));case"[object Boolean]":case"[object Date]":case"[object Number]":return d9(+n,+t);case"[object Error]":return n.name==t.name&&n.message==t.message;case"[object RegExp]":case"[object String]":return n==t+"";case"[object Map]":var s=h9;case"[object Set]":var c=1&a;if(s||(s=g9),n.size!=t.size&&!c)return!1;var u=l.get(n);if(u)return u==t;a|=2,l.set(n,t);var d=C9(s(n),s(t),a,i,r,l);return l.delete(n),d;case"[object Symbol]":if(e3)return e3.call(n)==e3.call(t)}return!1},p9=function(n,t){for(var e=-1,a=t.length,i=n.length;++e<a;)n[i+e]=t[e];return n},L3=Array.isArray,m9=p9,v9=L3,w9=function(n,t,e){var a=t(n);return v9(n)?a:m9(a,e(n))},L9=function(n,t){for(var e=-1,a=n==null?0:n.length,i=0,r=[];++e<a;){var l=n[e];t(l,e,n)&&(r[i++]=l)}return r},y9=function(){return[]},k9=Object.prototype.propertyIsEnumerable,n2=Object.getOwnPropertySymbols,x9=n2?function(n){return n==null?[]:(n=Object(n),L9(n2(n),function(t){return k9.call(n,t)}))}:y9,b9=function(n,t){for(var e=-1,a=Array(n);++e<n;)a[e]=t(e);return a},_9=G1,$9=U1,e2=function(n){return $9(n)&&_9(n)=="[object Arguments]"},A9=U1,j2=Object.prototype,F9=j2.hasOwnProperty,S9=j2.propertyIsEnumerable,j9=e2(function(){return arguments}())?e2:function(n){return A9(n)&&F9.call(n,"callee")&&!S9.call(n,"callee")},d3={exports:{}},M9=function(){return!1};(function(n,t){var e=s1,a=M9,i=t&&!t.nodeType&&t,r=i&&n&&!n.nodeType&&n,l=r&&r.exports===i?e.Buffer:void 0,s=(l?l.isBuffer:void 0)||a;n.exports=s})(d3,d3.exports);var M2=d3.exports,E9=/^(?:0|[1-9]\d*)$/,O9=function(n,t){var e=typeof n;return!!(t=t??9007199254740991)&&(e=="number"||e!="symbol"&&E9.test(n))&&n>-1&&n%1==0&&n<t},E2=function(n){return typeof n=="number"&&n>-1&&n%1==0&&n<=9007199254740991},R9=G1,B9=E2,N9=U1,M={};M["[object Float32Array]"]=M["[object Float64Array]"]=M["[object Int8Array]"]=M["[object Int16Array]"]=M["[object Int32Array]"]=M["[object Uint8Array]"]=M["[object Uint8ClampedArray]"]=M["[object Uint16Array]"]=M["[object Uint32Array]"]=!0,M["[object Arguments]"]=M["[object Array]"]=M["[object ArrayBuffer]"]=M["[object Boolean]"]=M["[object DataView]"]=M["[object Date]"]=M["[object Error]"]=M["[object Function]"]=M["[object Map]"]=M["[object Number]"]=M["[object Object]"]=M["[object RegExp]"]=M["[object Set]"]=M["[object String]"]=M["[object WeakMap]"]=!1;var z9=function(n){return N9(n)&&B9(n.length)&&!!M[R9(n)]},P9=function(n){return function(t){return n(t)}},C3={exports:{}};(function(n,t){var e=i0,a=t&&!t.nodeType&&t,i=a&&n&&!n.nodeType&&n,r=i&&i.exports===a&&e.process,l=function(){try{var s=i&&i.require&&i.require("util").types;return s||r&&r.binding&&r.binding("util")}catch{}}();n.exports=l})(C3,C3.exports);var o2=C3.exports,Z9=z9,D9=P9,i2=o2&&o2.isTypedArray,O2=i2?D9(i2):Z9,T9=b9,I9=j9,q9=L3,V9=M2,G9=O9,U9=O2,W9=Object.prototype.hasOwnProperty,H9=function(n,t){var e=q9(n),a=!e&&I9(n),i=!e&&!a&&V9(n),r=!e&&!a&&!i&&U9(n),l=e||a||i||r,s=l?T9(n.length,String):[],c=s.length;for(var u in n)!t&&!W9.call(n,u)||l&&(u=="length"||i&&(u=="offset"||u=="parent")||r&&(u=="buffer"||u=="byteLength"||u=="byteOffset")||G9(u,c))||s.push(u);return s},K9=Object.prototype,J9=function(n){var t=n&&n.constructor;return n===(typeof t=="function"&&t.prototype||K9)},Q9=function(n,t){return function(e){return n(t(e))}}(Object.keys,Object),X9=J9,Y9=Q9,t6=Object.prototype.hasOwnProperty,n6=function(n){if(!X9(n))return Y9(n);var t=[];for(var e in Object(n))t6.call(n,e)&&e!="constructor"&&t.push(e);return t},e6=$2,o6=E2,i6=H9,r6=n6,a6=function(n){return n!=null&&o6(n.length)&&!e6(n)},s6=w9,l6=x9,c6=function(n){return a6(n)?i6(n):r6(n)},r2=function(n){return s6(n,c6,l6)},u6=Object.prototype.hasOwnProperty,d6=function(n,t,e,a,i,r){var l=1&e,s=r2(n),c=s.length;if(c!=r2(t).length&&!l)return!1;for(var u=c;u--;){var d=s[u];if(!(l?d in t:u6.call(t,d)))return!1}var C=r.get(n),g=r.get(t);if(C&&g)return C==t&&g==n;var h=!0;r.set(n,t),r.set(t,n);for(var p=l;++u<c;){var L=n[d=s[u]],w=t[d];if(a)var x=l?a(w,L,d,t,n,r):a(L,w,d,n,t,r);if(!(x===void 0?L===w||i(L,w,e,a,r):x)){h=!1;break}p||(p=d=="constructor")}if(h&&!p){var y=n.constructor,v=t.constructor;y==v||!("constructor"in n)||!("constructor"in t)||typeof y=="function"&&y instanceof y&&typeof v=="function"&&v instanceof v||(h=!1)}return r.delete(n),r.delete(t),h},h3=v1(s1,"DataView"),g3=w3,f3=v1(s1,"Promise"),p3=v1(s1,"Set"),m3=v1(s1,"WeakMap"),R2=G1,w1=A2,a2="[object Map]",s2="[object Promise]",l2="[object Set]",c2="[object WeakMap]",u2="[object DataView]",C6=w1(h3),h6=w1(g3),g6=w1(f3),f6=w1(p3),p6=w1(m3),u1=R2;(h3&&u1(new h3(new ArrayBuffer(1)))!=u2||g3&&u1(new g3)!=a2||f3&&u1(f3.resolve())!=s2||p3&&u1(new p3)!=l2||m3&&u1(new m3)!=c2)&&(u1=function(n){var t=R2(n),e=t=="[object Object]"?n.constructor:void 0,a=e?w1(e):"";if(a)switch(a){case C6:return u2;case h6:return a2;case g6:return s2;case f6:return l2;case p6:return c2}return t});var o3=e9,m6=S2,v6=f9,w6=d6,d2=u1,C2=L3,h2=M2,L6=O2,g2="[object Arguments]",f2="[object Array]",z1="[object Object]",p2=Object.prototype.hasOwnProperty,y6=function(n,t,e,a,i,r){var l=C2(n),s=C2(t),c=l?f2:d2(n),u=s?f2:d2(t),d=(c=c==g2?z1:c)==z1,C=(u=u==g2?z1:u)==z1,g=c==u;if(g&&h2(n)){if(!h2(t))return!1;l=!0,d=!1}if(g&&!d)return r||(r=new o3),l||L6(n)?m6(n,t,e,a,i,r):v6(n,t,c,e,a,i,r);if(!(1&e)){var h=d&&p2.call(n,"__wrapped__"),p=C&&p2.call(t,"__wrapped__");if(h||p){var L=h?n.value():n,w=p?t.value():t;return r||(r=new o3),i(L,w,e,a,r)}}return!!g&&(r||(r=new o3),w6(n,t,e,a,i,r))},m2=U1,k6=function n(t,e,a,i,r){return t===e||(t==null||e==null||!m2(t)&&!m2(e)?t!=t&&e!=e:y6(t,e,a,i,n,r))},x6=k6;const b6=H2(function(n,t){return x6(n,t)});function _6(n){let t,e;return t=new j4({props:{codeActions:n[5],loaded:n[9],suggestions:n[0],filepath:n[1],originalCode:n[8],onCodeAction:n[3],scrollContainer:n[7]}}),{c(){D(t.$$.fragment)},m(a,i){T(t,a,i),e=!0},p(a,i){const r={};32&i&&(r.codeActions=a[5]),512&i&&(r.loaded=a[9]),1&i&&(r.suggestions=a[0]),2&i&&(r.filepath=a[1]),256&i&&(r.originalCode=a[8]),8&i&&(r.onCodeAction=a[3]),128&i&&(r.scrollContainer=a[7]),t.$set(r)},i(a){e||($(t.$$.fragment,a),e=!0)},o(a){F(t.$$.fragment,a),e=!1},d(a){I(t,a)}}}function $6(n){let t,e;return t=new f0({props:{filepath:n[1],onFileAction:n[2],fileActions:n[4],slot:"summary",suggestions:n[0]}}),{c(){D(t.$$.fragment)},m(a,i){T(t,a,i),e=!0},p(a,i){const r={};2&i&&(r.filepath=a[1]),4&i&&(r.onFileAction=a[2]),16&i&&(r.fileActions=a[4]),1&i&&(r.suggestions=a[0]),t.$set(r)},i(a){e||($(t.$$.fragment,a),e=!0)},o(a){F(t.$$.fragment,a),e=!1},d(a){I(t,a)}}}function A6(n){let t,e;return t=new C0({props:{open:!0,class:"c-code-roll-item ",expandable:n[6],$$slots:{summary:[$6],default:[_6]},$$scope:{ctx:n}}}),{c(){D(t.$$.fragment)},m(a,i){T(t,a,i),e=!0},p(a,[i]){const r={};64&i&&(r.expandable=a[6]),17343&i&&(r.$$scope={dirty:i,ctx:a}),t.$set(r)},i(a){e||($(t.$$.fragment,a),e=!0)},o(a){F(t.$$.fragment,a),e=!1},d(a){I(t,a)}}}function F6(n,t,e){let{suggestions:a}=t,{filepath:i}=t,{readFile:r}=t,{onFileAction:l=m1}=t,{onCodeAction:s=m1}=t,{fileActions:c=[]}=t,{codeActions:u=[]}=t,{expandable:d=!0}=t,{scrollContainer:C}=t,g="",h=!1,p=null,L=null;return n.$$set=w=>{"suggestions"in w&&e(0,a=w.suggestions),"filepath"in w&&e(1,i=w.filepath),"readFile"in w&&e(10,r=w.readFile),"onFileAction"in w&&e(2,l=w.onFileAction),"onCodeAction"in w&&e(3,s=w.onCodeAction),"fileActions"in w&&e(4,c=w.fileActions),"codeActions"in w&&e(5,u=w.codeActions),"expandable"in w&&e(6,d=w.expandable),"scrollContainer"in w&&e(7,C=w.scrollContainer)},n.$$.update=()=>{6147&n.$$.dirty&&(b6(a,p)&&L!==null&&S3(i,L)||(L!==null&&S3(i,L)||e(9,h=!1),e(12,L=i),e(11,p=a),async function(w){e(8,g=await r(w)),e(9,h=!0)}(i)))},[a,i,l,s,c,u,d,C,g,h,r,p,L]}class T6 extends O{constructor(t){super(),R(this,t,F6,A6,B,{suggestions:0,filepath:1,readFile:10,onFileAction:2,onCodeAction:3,fileActions:4,codeActions:5,expandable:6,scrollContainer:7})}}function I6(n){return async function(t){const e=await n.send({type:Y2.readFileRequest,data:{pathName:t}});if("error"in e.data)throw new Error(e.data.error);return e.data.content}}export{T6 as C,C0 as D,m4 as P,b0 as a,I3 as b,q3 as c,D6 as d,x0 as e,I6 as f,k0 as g,l3 as s};
