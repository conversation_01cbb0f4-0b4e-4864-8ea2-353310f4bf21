<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Phoenix System - Money Management</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        :root {
            /* Dark theme with purple accents - matching Neural Pulse */
            --phoenix-primary: #8e24aa;
            --phoenix-secondary: #7b1fa2;
            --phoenix-accent: #ab47bc;
            --phoenix-light: #e1bee7;
            --phoenix-dark: #4a148c;
            --phoenix-bg: linear-gradient(135deg, rgba(142, 36, 170, 0.1), rgba(142, 36, 170, 0.05));
            --phoenix-border: rgba(142, 36, 170, 0.3);
            --text-light: #ffffff;
            --text-dark: #f5f5f5;
            --text-medium: #bdbdbd;
            --dark-bg: #1e1e2f;
            --medium-bg: #27293d;
            --light-bg: #2c2c44;
            --success: #00c853;
            --danger: #f44336;
            --warning: #ffc107;
            --info: #2196f3;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            color: var(--text-light);
            background-color: transparent;
            overflow: hidden; /* Ensure no scrollbar on the body */
        }

        .phoenix-container {
            display: flex;
            flex-direction: column;
            gap: 15px;
            max-width: 100%;
            padding: 0;
            overflow: hidden; /* Ensure no scrollbar on the container */
        }

        .phoenix-header {
            color: var(--phoenix-dark);
            text-align: center;
            margin-bottom: 10px;
            font-size: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .phoenix-header i {
            color: var(--phoenix-primary);
        }

        .balance-display {
            margin-left: auto;
            font-size: 14px;
            color: var(--phoenix-dark);
            background-color: rgba(245, 158, 11, 0.1);
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: normal;
        }

        #balanceValue {
            font-weight: bold;
            color: var(--phoenix-primary);
        }

        .phoenix-sections {
            display: flex;
            flex-direction: column;
            gap: 15px;
            overflow: hidden; /* Ensure no scrollbar on the sections container */
        }

        .phoenix-section {
            background: var(--medium-bg);
            border: 1px solid var(--phoenix-border);
            border-radius: 8px;
            padding: 15px;
            overflow: hidden; /* Ensure no scrollbar on the section */
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        .section-title {
            font-size: 14px;
            font-weight: 600;
            color: var(--phoenix-accent);
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .section-title i {
            color: var(--phoenix-primary);
        }

        /* AI Prediction Section */
        .ai-prediction {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .prediction-display {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: var(--dark-bg);
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 10px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .prediction-direction {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 5px;
        }

        .direction-label {
            font-size: 12px;
            color: var(--text-medium);
        }

        .direction-value {
            font-size: 18px;
            font-weight: bold;
            color: var(--text-light);
        }

        .prediction-time {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 5px;
        }

        .time-label {
            font-size: 12px;
            color: var(--text-medium);
        }

        .time-value {
            font-size: 18px;
            font-weight: bold;
            color: var(--text-light);
        }

        .prediction-amount {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 5px;
        }

        .amount-label {
            font-size: 12px;
            color: var(--text-medium);
        }

        .amount-value {
            font-size: 18px;
            font-weight: bold;
            color: var(--phoenix-primary);
        }

        .ai-confidence {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .confidence-label {
            font-size: 12px;
            color: var(--text-medium);
            display: flex;
            justify-content: space-between;
        }

        .confidence-bar {
            height: 8px;
            background-color: rgba(245, 158, 11, 0.2);
            border-radius: 4px;
            overflow: hidden;
        }

        .confidence-fill {
            height: 100%;
            background-color: var(--phoenix-primary);
            width: 75%; /* Default confidence level */
            transition: width 0.5s ease;
        }

        .ai-controls {
            display: flex;
            gap: 10px;
            margin-top: 10px;
        }

        .ai-analyze-btn {
            flex: 1;
            padding: 10px;
            background-color: var(--phoenix-primary);
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
        }

        .ai-analyze-btn:hover {
            background-color: var(--phoenix-secondary);
            transform: translateY(-2px);
        }

        /* Tracking Sheet Section */
        .tracking-sheet {
            display: flex;
            flex-direction: column;
            gap: 12px;
            overflow: hidden; /* Ensure no scrollbar on the tracking sheet */
        }

        .trade-result-buttons {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
        }

        .result-btn {
            flex: 1;
            padding: 10px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
        }

        .win-btn {
            background-color: var(--success);
            color: white;
        }

        .win-btn:hover {
            background-color: #0d9668;
            transform: translateY(-2px);
        }

        .loss-btn {
            background-color: var(--danger);
            color: white;
        }

        .loss-btn:hover {
            background-color: #dc2626;
            transform: translateY(-2px);
        }

        .trade-sequence {
            background-color: var(--dark-bg);
            border: 1px solid var(--phoenix-border);
            border-radius: 6px;
            padding: 10px;
            font-size: 13px;
            margin-bottom: 10px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .sequence-title {
            font-weight: bold;
            margin-bottom: 8px;
            color: var(--phoenix-accent);
        }

        .sequence-list {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .sequence-item {
            padding: 5px 8px;
            background-color: rgba(245, 158, 11, 0.1);
            border-radius: 4px;
            font-size: 12px;
        }

        .sequence-item.current {
            background-color: var(--phoenix-primary);
            color: white;
            font-weight: bold;
        }

        .sequence-separator {
            width: 100%;
            padding: 5px 0;
            margin: 8px 0;
            font-weight: bold;
            color: var(--phoenix-dark);
            border-top: 1px solid var(--phoenix-border);
            border-bottom: 1px solid var(--phoenix-border);
        }

        .formula-item {
            width: 100%;
            padding: 10px;
            margin: 8px 0;
            background-color: rgba(245, 158, 11, 0.1);
            border-radius: 6px;
            font-size: 12px;
            line-height: 1.5;
            color: var(--phoenix-dark);
        }

        .masaniello-info {
            background-color: var(--dark-bg);
            border: 1px solid var(--phoenix-border);
            border-radius: 6px;
            padding: 10px;
            font-size: 13px;
            margin-bottom: 10px;
            overflow: hidden; /* Ensure no scrollbar on the info section */
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .info-title {
            font-size: 16px;
            font-weight: bold;
            color: var(--phoenix-accent);
            margin-bottom: 15px;
            text-align: center;
            border-bottom: 1px solid var(--phoenix-border);
            padding-bottom: 5px;
        }

        .info-section-title {
            font-size: 14px;
            font-weight: bold;
            color: var(--phoenix-accent);
            margin: 15px 0 10px 0;
            text-align: center;
            border-bottom: 1px solid var(--phoenix-border);
            padding-bottom: 5px;
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }

        .info-label {
            color: var(--phoenix-accent);
            font-weight: 500;
            flex: 2;
        }

        .info-value {
            color: var(--text-light);
            font-weight: bold;
            flex: 1;
            text-align: right;
        }

        .info-percentage {
            color: var(--success);
            font-weight: bold;
            margin-left: 10px;
            flex: 1;
            text-align: right;
        }

        .trade-history {
            max-height: 150px;
            overflow-y: auto;
            border: 1px solid var(--phoenix-border);
            border-radius: 6px;
        }

        .history-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 12px;
        }

        .history-table th,
        .history-table td {
            padding: 8px;
            text-align: center;
            border-bottom: 1px solid var(--phoenix-border);
            color: var(--text-light);
        }

        .history-table th {
            background-color: var(--dark-bg);
            color: var(--phoenix-accent);
            font-weight: 600;
        }

        .history-table tr:nth-child(even) {
            background-color: rgba(142, 36, 170, 0.1);
        }

        .win-result {
            color: var(--success);
            font-weight: bold;
        }

        .loss-result {
            color: var(--danger);
            font-weight: bold;
        }

        /* Settings Section */
        .phoenix-settings {
            display: flex;
            flex-direction: column;
            gap: 12px;
            overflow: hidden; /* Ensure no scrollbar on the settings section */
        }

        .settings-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 10px;
        }

        .settings-label {
            font-size: 13px;
            color: var(--text-light);
            font-weight: 500;
        }

        .settings-input {
            width: 100px;
            padding: 6px;
            border: 1px solid var(--phoenix-border);
            border-radius: 4px;
            background-color: var(--dark-bg);
            color: var(--text-light);
            font-weight: 500;
        }

        .settings-input:focus {
            outline: none;
            border-color: var(--phoenix-primary);
            box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.2);
        }

        .settings-apply-btn {
            width: 100%;
            padding: 8px;
            margin-top: 10px;
            background-color: var(--phoenix-primary);
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
        }

        .settings-apply-btn:hover {
            background-color: var(--phoenix-secondary);
            transform: translateY(-2px);
        }

        /* Responsive adjustments */
        @media (min-width: 768px) {
            .phoenix-sections {
                flex-direction: row;
            }

            .phoenix-section {
                flex: 1;
            }
        }
    </style>
</head>
<body>
    <div class="phoenix-container">
        <h3 class="phoenix-header"><i class="fas fa-crown"></i> Phoenix System <span class="balance-display">Balance: <span id="balanceValue">$0.00</span></span></h3>

        <div class="phoenix-sections">
            <!-- AI Prediction Section -->
            <div class="phoenix-section">
                <div class="section-title"><i class="fas fa-brain"></i> AI Trade Prediction</div>
                <div class="ai-prediction">
                    <div class="prediction-display">
                        <div class="prediction-direction">
                            <div class="direction-label">Direction</div>
                            <div class="direction-value" id="predictionDirection">--</div>
                        </div>
                        <div class="prediction-time">
                            <div class="time-label">Expiry</div>
                            <div class="time-value" id="predictionTime">--</div>
                        </div>
                        <div class="prediction-amount">
                            <div class="amount-label">Amount</div>
                            <div class="amount-value" id="predictionAmount">--</div>
                        </div>
                    </div>

                    <div class="ai-confidence">
                        <div class="confidence-label">
                            <span>AI Confidence</span>
                            <span id="confidenceValue">75%</span>
                        </div>
                        <div class="confidence-bar">
                            <div class="confidence-fill" id="confidenceFill"></div>
                        </div>
                    </div>

                    <div class="ai-controls">
                        <button class="ai-analyze-btn" id="analyzeBtn">
                            <i class="fas fa-chart-line"></i> Analyze Market
                        </button>
                    </div>
                </div>
            </div>

            <!-- Tracking Sheet Section -->
            <div class="phoenix-section">
                <div class="section-title"><i class="fas fa-clipboard-list"></i> Trade Tracking</div>
                <div class="tracking-sheet">
                    <div class="trade-result-buttons">
                        <button class="result-btn win-btn" id="winBtn">
                            <i class="fas fa-check-circle"></i> Win (W)
                        </button>
                        <button class="result-btn loss-btn" id="lossBtn">
                            <i class="fas fa-times-circle"></i> Loss (L)
                        </button>
                    </div>

                    <div class="trade-sequence">
                        <div class="sequence-title">Trade History</div>
                        <div class="sequence-list" id="sequenceList">
                            <!-- Will be populated by JavaScript -->
                        </div>
                    </div>

                    <div class="masaniello-info">
                        <div class="info-title">Money Management System</div>
                        <div class="info-row">
                            <div class="info-label">Initial Capital:</div>
                            <div class="info-value" id="initialCapitalDisplay">--</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">Target Final:</div>
                            <div class="info-value" id="capitalFinal">--</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">Target Profit:</div>
                            <div class="info-value" id="totalWin">--</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">Target %:</div>
                            <div class="info-value" id="profitRatio">--</div>
                        </div>
                        <div class="info-section-title">Current Session</div>
                        <div class="info-row">
                            <div class="info-label">Wins:</div>
                            <div class="info-value" id="eventsWin">--</div>
                            <div class="info-percentage" id="winPercentage">--</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">Losses:</div>
                            <div class="info-value" id="eventsLoose">--</div>
                            <div class="info-percentage" id="loosePercentage">--</div>
                        </div>
                        <div class="info-section-title">Current Portfolio</div>
                        <div class="info-row">
                            <div class="info-label">Current Value:</div>
                            <div class="info-value" id="currentPortfolio">--</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">Current Profit:</div>
                            <div class="info-value" id="currentProfit">--</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">Current %:</div>
                            <div class="info-value" id="currentProfitPercent">--</div>
                        </div>
                    </div>

                    <div class="trade-history">
                        <table class="history-table">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>Direction</th>
                                    <th>Amount</th>
                                    <th>Return</th>
                                </tr>
                            </thead>
                            <tbody id="historyTableBody">
                                <!-- Will be populated by JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Settings Section -->
        <div class="phoenix-section">
            <div class="section-title"><i class="fas fa-cog"></i> Trading Settings</div>
            <div class="phoenix-settings">
                <div class="settings-row">
                    <div class="settings-label">Initial Capital</div>
                    <input type="number" class="settings-input" id="initialCapitalInput" value="50" min="1" max="10000">
                </div>
                <div class="settings-row">
                    <div class="settings-label">Total Trades</div>
                    <input type="number" class="settings-input" id="totalTradesInput" value="10" min="5" max="30">
                </div>
                <div class="settings-row">
                    <div class="settings-label">Win Trades</div>
                    <input type="number" class="settings-input" id="winTradesInput" value="3" min="1" max="20">
                </div>
                <div class="settings-row">
                    <div class="settings-label">Payout Multiplier</div>
                    <input type="number" class="settings-input" id="payoutMultiplierInput" value="1.85" min="1.5" max="2.0" step="0.01">
                </div>
                <button class="settings-apply-btn" id="applySettingsBtn">
                    <i class="fas fa-check-circle"></i> Apply Settings
                </button>
            </div>
        </div>
    </div>

    <script src="phoenix-system.js"></script>
</body>
</html>
