import{R as c,a as i}from"./types-LfaCSdmF.js";import{C as m,a as M}from"./chat-types-NgqNgjwU.js";function T(n){return function(t){try{if(isNaN(t.getTime()))return"Unknown time";const e=new Date().getTime()-t.getTime(),a=Math.floor(e/1e3),r=Math.floor(a/60),s=Math.floor(r/60),o=Math.floor(s/24);return a<60?`${a}s ago`:r<60?`${r}m ago`:s<24?`${s}h ago`:o<30?`${o}d ago`:t.toLocaleDateString()}catch(e){return console.error("Error formatting date:",e),"Unknown time"}}(new Date(n))}function I(n,t){const e=setInterval(()=>{const a=n.getTime()-Date.now();if(a<=0)return void clearInterval(e);const r=Math.floor(a/1e3),s=Math.floor(r/60),o=Math.floor(s/60),u=Math.floor(o/24);t(r<60?`${r}s`:s<60?`${s}m ${r%60}s`:o<24?`${o}h`:u<30?`${u}d`:"1mo")},1e3);return()=>clearInterval(e)}function P(n){if(n===void 0)return"neutral";switch(n){case c.agentPending:case c.agentStarting:case c.agentRunning:return"info";case c.agentIdle:return"success";case c.agentFailed:return"error";default:return"neutral"}}function S(n){if(n===void 0)return"neutral";switch(n){case i.workspaceRunning:return"info";case i.workspacePausing:case i.workspacePaused:case i.workspaceResuming:default:return"neutral"}}function $(n){switch(n){case c.agentStarting:return"Starting";case c.agentRunning:return"Running";case c.agentIdle:return"Idle";case c.agentPending:return"Pending";case c.agentFailed:return"Failed";default:return"Unknown"}}function v(n){switch(n){case i.workspaceRunning:return"Running";case i.workspacePausing:return"Pausing";case i.workspacePaused:return"Paused";case i.workspaceResuming:return"Resuming";default:return"Unknown"}}const g=n=>{let t={};for(const e of n){const a=t[e.new_path];t[e.new_path]=a?{...a,new_contents:e.new_contents,new_path:e.new_path}:e}return Object.values(t).filter(e=>!e.old_path||!e.new_path||e.old_path!==e.new_path||e.old_contents!==e.new_contents)},x=n=>{const t=n.flatMap(e=>e.changed_files);return g(t)},E=(n,t)=>{var a;const e=w(n,t);return((a=n[e])==null?void 0:a.exchange.request_message)??""},w=(n,t)=>n.slice(0,t).findLastIndex(e=>e.exchange.request_message),p=(n,t)=>{const e=n.slice(t).findIndex(a=>a.exchange.request_message);if(e!==-1)return t+e},L=(n,t)=>{const e=((r,s)=>{const o=w(r,s);let u=p(r,s);return u===void 0&&(u=r.length),r.slice(o+1,u)})(n,t),a=e.flatMap(r=>r.changed_files);return g(a)},U=(n,t)=>{var u,f;const e=p(n,t),a=n.slice(t,e),r=(u=n[t].exchange.response_nodes)==null?void 0:u.find(l=>l.type===m.TOOL_USE);if(!r)return[];const s=(f=r.tool_use)==null?void 0:f.tool_use_id;if(!s)return[];if(!a.find(l=>{var d;return(d=l.exchange.request_nodes)==null?void 0:d.some(h=>{var _;return h.type===M.TOOL_RESULT&&((_=h.tool_result_node)==null?void 0:_.tool_use_id)===s})}))return[];const o=a.flatMap(l=>l.changed_files);return g(o)},O="STREAM_CANCELLED",D="STREAM_TIMEOUT";export{O as S,v as a,D as b,x as c,P as d,S as e,T as f,$ as g,U as h,E as i,L as j,I as s};
