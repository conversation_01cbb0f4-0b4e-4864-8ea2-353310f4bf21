"""
Money Management System for Binary Options Trading Bot

This module implements various  fiemoney management strategies for binary options trading,
including a modified Kelly Criterion and anti-martingale approach.
"""

import math
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('money_management')

class MoneyManager:
    """
    Money management system for binary options trading.
    Implements various position sizing strategies and risk management techniques.
    """
    
    def __init__(self, initial_balance=1000, max_risk_per_trade=0.02, 
                 win_rate=0.55, payout_ratio=0.85, strategy="kelly"):
        """
        Initialize the money management system.
        
        Args:
            initial_balance (float): Starting account balance
            max_risk_per_trade (float): Maximum percentage of balance to risk per trade (0.01 = 1%)
            win_rate (float): Expected win rate (0.55 = 55%)
            payout_ratio (float): Payout ratio for winning trades (0.85 = 85% profit)
            strategy (str): Money management strategy to use ('kelly', 'fixed', 'anti-martingale')
        """
        self.balance = initial_balance
        self.initial_balance = initial_balance
        self.max_risk_per_trade = max_risk_per_trade
        self.win_rate = win_rate
        self.payout_ratio = payout_ratio
        self.strategy = strategy
        
        # Trade history
        self.trades = []
        self.consecutive_wins = 0
        self.consecutive_losses = 0
        
        # Performance metrics
        self.total_trades = 0
        self.winning_trades = 0
        self.losing_trades = 0
        
        logger.info(f"Money Manager initialized with {initial_balance} balance and {strategy} strategy")
    
    def calculate_position_size(self):
        """
        Calculate the optimal position size based on the selected strategy.
        
        Returns:
            float: Amount to trade in account currency
        """
        if self.strategy == "kelly":
            return self._kelly_criterion()
        elif self.strategy == "fixed":
            return self._fixed_percentage()
        elif self.strategy == "anti-martingale":
            return self._anti_martingale()
        else:
            # Default to fixed percentage if strategy not recognized
            return self._fixed_percentage()
    
    def _kelly_criterion(self):
        """
        Calculate position size using a modified Kelly Criterion.
        
        The Kelly formula for binary options is:
        f* = (p(b) - q) / b
        
        Where:
        - f* is the fraction of the bankroll to wager
        - p is the probability of winning
        - q is the probability of losing (1-p)
        - b is the payout ratio (net odds received on the wager)
        
        Returns:
            float: Amount to trade in account currency
        """
        p = self.win_rate  # Probability of winning
        q = 1 - p  # Probability of losing
        b = self.payout_ratio  # Net odds (payout ratio)
        
        # Calculate Kelly percentage
        kelly_percentage = (p * (1 + b) - 1) / b
        
        # Apply a safety factor (using 1/4 Kelly is common for more conservative approach)
        safe_kelly = kelly_percentage * 0.25
        
        # Cap at maximum risk per trade
        position_size = min(safe_kelly, self.max_risk_per_trade) * self.balance
        
        logger.debug(f"Kelly calculation: {kelly_percentage:.4f}, Safe Kelly: {safe_kelly:.4f}")
        logger.info(f"Kelly position size: {position_size:.2f}")
        
        return round(position_size, 2)
    
    def _fixed_percentage(self):
        """
        Calculate position size using a fixed percentage of account balance.
        
        Returns:
            float: Amount to trade in account currency
        """
        position_size = self.balance * self.max_risk_per_trade
        logger.info(f"Fixed percentage position size: {position_size:.2f}")
        return round(position_size, 2)
    
    def _anti_martingale(self):
        """
        Calculate position size using an anti-martingale approach.
        Increases position size after wins, decreases after losses.
        
        Returns:
            float: Amount to trade in account currency
        """
        base_size = self.balance * self.max_risk_per_trade
        
        # Increase size after wins, decrease after losses
        if self.consecutive_wins > 0:
            # Increase by 50% for each consecutive win, up to 3 wins
            multiplier = min(1 + (0.5 * self.consecutive_wins), 2.5)
            position_size = base_size * multiplier
        elif self.consecutive_losses > 0:
            # Decrease by 30% for each consecutive loss
            multiplier = max(1 - (0.3 * self.consecutive_losses), 0.4)
            position_size = base_size * multiplier
        else:
            position_size = base_size
        
        logger.info(f"Anti-martingale position size: {position_size:.2f} " +
                   f"(Consecutive wins: {self.consecutive_wins}, losses: {self.consecutive_losses})")
        
        return round(position_size, 2)
    
    def update_balance(self, trade_result, amount):
        """
        Update account balance based on trade result.
        
        Args:
            trade_result (str): 'win' or 'loss'
            amount (float): Amount traded
            
        Returns:
            float: New account balance
        """
        self.total_trades += 1
        
        if trade_result == 'win':
            profit = amount * self.payout_ratio
            self.balance += profit
            self.winning_trades += 1
            self.consecutive_wins += 1
            self.consecutive_losses = 0
            logger.info(f"Trade won: +{profit:.2f}, New balance: {self.balance:.2f}")
        else:  # loss
            self.balance -= amount
            self.losing_trades += 1
            self.consecutive_losses += 1
            self.consecutive_wins = 0
            logger.info(f"Trade lost: -{amount:.2f}, New balance: {self.balance:.2f}")
        
        # Record trade
        self.trades.append({
            'result': trade_result,
            'amount': amount,
            'balance': self.balance
        })
        
        return self.balance
    
    def get_win_rate(self):
        """
        Calculate the current win rate.
        
        Returns:
            float: Win rate as a percentage
        """
        if self.total_trades == 0:
            return 0
        return (self.winning_trades / self.total_trades) * 100
    
    def get_profit_percentage(self):
        """
        Calculate the current profit/loss percentage.
        
        Returns:
            float: Profit/loss as a percentage of initial balance
        """
        return ((self.balance - self.initial_balance) / self.initial_balance) * 100
    
    def get_stats(self):
        """
        Get current trading statistics.
        
        Returns:
            dict: Dictionary containing trading statistics
        """
        return {
            'balance': round(self.balance, 2),
            'initial_balance': self.initial_balance,
            'total_trades': self.total_trades,
            'winning_trades': self.winning_trades,
            'losing_trades': self.losing_trades,
            'win_rate': round(self.get_win_rate(), 2),
            'profit_percentage': round(self.get_profit_percentage(), 2),
            'consecutive_wins': self.consecutive_wins,
            'consecutive_losses': self.consecutive_losses
        }
