/**
 * Phoenix System - Money Management System
 *
 * This script implements a money management system with AI direction
 * and time prediction for binary options trading.
 *
 * The system is based on a system of differential equations for error correction.
 * It calculates optimal stake amounts based on initial capital, expected number of trades,
 * expected win rate, and payout percentage.
 */

// Debug mode flag
const DEBUG = true;

// Debug logging function
function debugLog(...args) {
    if (DEBUG) {
        console.log('[Phoenix System]', ...args);
    }
}

// Global variables
let balance = 0; // Current balance
let initialBalance = 0; // Initial balance when trading session started
let initialCapital = 1000; // Initial capital for Masaniello calculations
let fixedInitialCapital = 0; // Fixed initial capital that doesn't change during trading
let profitLoss = 0; // Current profit/loss
let isTrading = false; // Trading status
let tradeCount = 0; // Number of trades placed
let winCount = 0; // Number of winning trades
let lossCount = 0; // Number of losing trades

// Money Management system variables
// No sequence variables needed as we're using the formula directly

// Money Management system parameters
let totalTrades = 10; // Total number of trades planned in the session
let winTrades = 3; // Number of win trades in the session
let payoutMultiplier = 1.85; // Payout multiplier (e.g., 85% payout = 1.85)
let currentTradeAmount = 0; // Current trade amount
let tradeResults = []; // Array to store W/L results for Masaniello calculations
let winProbability = 0.75; // Default win probability (75%)

// Trade history
let tradeHistory = [];

// DOM elements
const predictionDirection = document.getElementById('predictionDirection');
const predictionTime = document.getElementById('predictionTime');
const predictionAmount = document.getElementById('predictionAmount');
const confidenceValue = document.getElementById('confidenceValue');
const confidenceFill = document.getElementById('confidenceFill');
const analyzeBtn = document.getElementById('analyzeBtn');
const winBtn = document.getElementById('winBtn');
const lossBtn = document.getElementById('lossBtn');
const sequenceList = document.getElementById('sequenceList');
const historyTableBody = document.getElementById('historyTableBody');
const balanceValue = document.getElementById('balanceValue');
const initialCapitalInput = document.getElementById('initialCapitalInput');
const totalTradesInput = document.getElementById('totalTradesInput');
const winTradesInput = document.getElementById('winTradesInput');
const payoutMultiplierInput = document.getElementById('payoutMultiplierInput');
const applySettingsBtn = document.getElementById('applySettingsBtn');

// Money Management info elements
const capitalFinalElement = document.getElementById('capitalFinal');
const profitRatioElement = document.getElementById('profitRatio');
const totalWinElement = document.getElementById('totalWin');
const eventsWinElement = document.getElementById('eventsWin');
const eventsLooseElement = document.getElementById('eventsLoose');
const winPercentageElement = document.getElementById('winPercentage');
const loosePercentageElement = document.getElementById('loosePercentage');

// Initialize the interface
function initializeInterface() {
    debugLog('Initializing Phoenix System interface');

    // Set up event listeners
    analyzeBtn.addEventListener('click', analyzeMarket);
    winBtn.addEventListener('click', () => recordTradeResult('win'));
    lossBtn.addEventListener('click', () => recordTradeResult('loss'));
    applySettingsBtn.addEventListener('click', applySettings);

    // Get values from input fields if they exist, otherwise use defaults
    const storedInitialCapital = localStorage.getItem('phoenixInitialCapital');
    const storedTotalTrades = localStorage.getItem('phoenixTotalTrades');
    const storedWinTrades = localStorage.getItem('phoenixWinTrades');
    const storedPayoutMultiplier = localStorage.getItem('phoenixPayoutMultiplier');

    // Set values for Masaniello parameters (use stored values if available)
    initialCapital = storedInitialCapital ? parseFloat(storedInitialCapital) : 1000;
    totalTrades = storedTotalTrades ? parseInt(storedTotalTrades) : 10;
    winTrades = storedWinTrades ? parseInt(storedWinTrades) : 4;
    payoutMultiplier = storedPayoutMultiplier ? parseFloat(storedPayoutMultiplier) : 1.85;

    // Set values in the input fields
    initialCapitalInput.value = initialCapital;
    totalTradesInput.value = totalTrades;
    winTradesInput.value = winTrades;
    payoutMultiplierInput.value = payoutMultiplier;

    // Calculate and update Money Management info
    updateMasanielloInfo();

    // Initialize the trade amount display with "--" instead of a value
    currentTradeAmount = 0;

    // Display "--" for the amount before starting the bot
    predictionAmount.textContent = `--`;

    debugLog('Initial trade amount calculated with settings:', {
        initialCapital,
        totalTrades,
        winTrades,
        payoutMultiplier,
        currentTradeAmount
    });

    // Initialize the trade sequence display
    updateSequenceDisplay();

    // Disable result buttons initially
    winBtn.disabled = true;
    lossBtn.disabled = true;

    // Start a new trading session
    startTradingSession();

    // Send message to parent that we're ready
    window.parent.postMessage({
        action: 'phoenixSystemReady'
    }, '*');

    // Force height update after initialization
    setTimeout(notifyParentAboutHeight, 100);
}

// Calculate and update Money Management information
function updateMasanielloInfo() {
    // The Money Management System uses a fixed profit target
    // We'll use 10% of initial capital as the target profit

    // Use the fixed initial capital for calculations if trading has started
    // This ensures the initial capital remains constant during trading
    const calculationCapital = isTrading && fixedInitialCapital > 0 ? fixedInitialCapital : initialCapital;

    // Calculate the target profit (fixed percentage of initial capital)
    // For example, if we want 10% profit on initial capital
    const targetProfitPercentage = 0.10; // 10%
    const targetProfit = calculationCapital * targetProfitPercentage;

    // Calculate target profit percentage
    const targetProfitPercent = targetProfitPercentage * 100; // 10%

    // Calculate final capital
    const capitalFinal = calculationCapital + targetProfit;

    // Calculate required win percentage
    const requiredWinPercentage = (winTrades / totalTrades) * 100;

    // Calculate current stats
    const currentWins = tradeResults.filter(result => result === 'win').length;
    const currentLosses = tradeResults.filter(result => result === 'loss').length;
    const currentWinPercentage = tradeResults.length > 0 ? (currentWins / tradeResults.length) * 100 : 0;

    // For Current Portfolio, we'll use the actual balance
    // This ensures the Current Value always matches the real balance
    let currentPortfolio = balance;
    let currentProfit = 0;
    let currentProfitPercent = 0;

    // Use the fixed initial capital for profit calculations if trading has started
    const profitCalculationCapital = isTrading && fixedInitialCapital > 0 ? fixedInitialCapital : initialCapital;

    // Calculate profit based on the actual balance and fixed initial capital
    currentProfit = balance - profitCalculationCapital;
    currentProfitPercent = (currentProfit / profitCalculationCapital) * 100;

    debugLog('Portfolio calculations:', {
        balance,
        initialCapital,
        fixedInitialCapital,
        profitCalculationCapital,
        currentPortfolio,
        currentProfit,
        currentProfitPercent
    });

    // Update the UI
    const initialCapitalDisplay = document.getElementById('initialCapitalDisplay');
    // Use the fixed initial capital for display if trading has started
    const displayCapital = isTrading && fixedInitialCapital > 0 ? fixedInitialCapital : initialCapital;
    if (initialCapitalDisplay) initialCapitalDisplay.textContent = `$${displayCapital.toFixed(2)}`;
    if (capitalFinalElement) capitalFinalElement.textContent = `$${capitalFinal.toFixed(2)}`;
    if (profitRatioElement) profitRatioElement.textContent = `${targetProfitPercent.toFixed(2)}%`;
    if (totalWinElement) totalWinElement.textContent = `$${targetProfit.toFixed(2)}`;
    if (eventsWinElement) eventsWinElement.textContent = currentWins;
    if (winPercentageElement) winPercentageElement.textContent = `${currentWinPercentage.toFixed(0)}%`;
    if (eventsLooseElement) eventsLooseElement.textContent = currentLosses;
    if (loosePercentageElement) loosePercentageElement.textContent = `${(100 - currentWinPercentage).toFixed(0)}%`;

    // Update current portfolio information
    const currentPortfolioElement = document.getElementById('currentPortfolio');
    const currentProfitElement = document.getElementById('currentProfit');
    const currentProfitPercentElement = document.getElementById('currentProfitPercent');

    // Always update the current portfolio values with the actual balance
    if (currentPortfolioElement) {
        // Always use the actual balance for Current Value
        currentPortfolioElement.textContent = `$${balance.toFixed(2)}`;
    }

    if (currentProfitElement) {
        // Always use the calculated profit
        currentProfitElement.textContent = `$${currentProfit.toFixed(2)}`;
        if (currentProfit < 0) {
            currentProfitElement.classList.add('negative');
        } else {
            currentProfitElement.classList.remove('negative');
        }
    }

    if (currentProfitPercentElement) {
        // Always use the calculated profit percentage
        currentProfitPercentElement.textContent = `${currentProfitPercent.toFixed(2)}%`;
        if (currentProfitPercent < 0) {
            currentProfitPercentElement.classList.add('negative');
        } else {
            currentProfitPercentElement.classList.remove('negative');
        }
    }

    debugLog('Money Management info updated:', {
        initialCapital,
        totalTrades,
        winTrades,
        payoutMultiplier,
        returnPerUnit: payoutMultiplier - 1,
        targetProfitPercent,
        targetProfit,
        capitalFinal,
        requiredWinPercentage,
        currentWins,
        currentLosses,
        currentWinPercentage,
        currentPortfolio,
        currentProfit,
        currentProfitPercent
    });

    // Notify parent about height change since Money Management info has changed
    setTimeout(notifyParentAboutHeight, 100);
}

// Calculate the stake amount using the Money Management System
function calculateMasanielloStake() {
    // Current state
    const w = tradeResults.filter(result => result === 'win').length; // Wins so far
    const l = tradeResults.filter(result => result === 'loss').length; // Losses so far
    const i = tradeResults.length + 1; // Current trade number (1-based)
    const N = totalTrades; // Total trades in cycle
    const K = winTrades; // Target number of wins

    // Use the fixed initial capital for calculations if trading has started
    const B = isTrading && fixedInitialCapital > 0 ? fixedInitialCapital : initialCapital;

    // Get current portfolio value (actual balance)
    const currentPortfolio = balance;

    // Profit factor (from the payout multiplier)
    const profitFactor = payoutMultiplier;

    // Return per unit (payout - 1)
    const returnPerUnit = profitFactor - 1;

    // Calculate current profit
    const currentProfit = currentPortfolio - B;

    // Calculate the target profit (fixed percentage of initial capital)
    // For example, if we want 10% profit on initial capital
    const targetProfitPercentage = 0.10; // 10%
    const targetProfit = B * targetProfitPercentage;

    debugLog('Money Management calculation parameters:', {
        tradeNumber: i,
        totalTrades: N,
        targetWins: K,
        currentWins: w,
        currentLosses: l,
        initialCapital: B,
        currentPortfolio: currentPortfolio,
        currentProfit: currentProfit,
        targetProfit: targetProfit,
        profitFactor: profitFactor,
        returnPerUnit: returnPerUnit
    });

    // Edge cases
    if (i > N || w >= K) {
        // We've completed all trades in the cycle or reached target wins
        return 0;
    }

    // Special case: If this is the last win trade needed to reach the target
    if (w === K - 1) {
        // Calculate the remaining profit needed to reach the target
        const remainingProfit = targetProfit - currentProfit;

        // Calculate the stake needed to generate this profit
        // If we win, our profit will be: stake * returnPerUnit
        // So: stake = remainingProfit / returnPerUnit
        let finalWinStake = remainingProfit / returnPerUnit;

        debugLog('Final win trade calculation:', {
            currentProfit,
            targetProfit,
            remainingProfit,
            returnPerUnit,
            calculatedStake: finalWinStake
        });

        // Return the calculated stake
        return finalWinStake;
    }

    // Check if we've already reached the profit target
    if (currentProfit >= targetProfit) {
        // Show congratulation message
        window.parent.postMessage({
            action: 'showNotification',
            message: `🎉 Congratulations! 🎉\nYou've reached your profit target of $${targetProfit.toFixed(2)} (${targetProfitPercentage * 100}%)!\nCurrent profit: $${currentProfit.toFixed(2)}`,
            type: 'success'
        }, '*');

        debugLog('Profit target reached:', {
            targetProfit,
            currentProfit,
            currentPortfolio
        });

        // Return zero stake since we've reached the target
        return 0;
    }

    // Check if the previous trade was a loss
    let stake;
    if (tradeResults.length > 0 && tradeResults[tradeResults.length - 1] === 'loss') {
        // After a loss, calculate stake to recover half of the previous loss
        // Get the previous trade amount
        const previousTradeAmount = tradeHistory.length > 0 ? tradeHistory[tradeHistory.length - 1].amount : 0;

        // Calculate the amount needed to recover half of the previous loss
        const halfLoss = previousTradeAmount / 2;

        // Calculate the stake needed to win half the previous loss
        // If we win, our profit will be: stake * returnPerUnit = halfLoss
        // So: stake = halfLoss / returnPerUnit
        stake = halfLoss / returnPerUnit;

        debugLog('Recovery stake calculation after loss:', {
            previousTradeAmount,
            halfLoss,
            returnPerUnit,
            calculatedStake: stake
        });
    } else {
        // Normal case: Calculate risk percentage - fixed at 5% of current portfolio
        const riskPercentage = 0.05;

        // Calculate trade amount as a fixed percentage of the current portfolio
        // Trade Amount = Current Portfolio × Risk Percentage
        stake = currentPortfolio * riskPercentage;

        debugLog('Normal stake calculation:', {
            currentPortfolio,
            riskPercentage,
            calculatedStake: stake
        });
    }

    // Use the calculated stake directly
    const finalStake = stake;

    debugLog('Money Management calculation result:', {
        tradeNumber: i,
        currentWins: w,
        currentLosses: l,
        calculatedStake: stake,
        finalStake
    });

    return finalStake;
}

// Helper function to calculate binomial coefficient (n choose k)
function binomialCoefficient(n, k) {
    // Handle edge cases
    if (k < 0 || k > n) return 0;
    if (k === 0 || k === n) return 1;

    // Use symmetry to reduce calculations
    if (k > n - k) {
        k = n - k;
    }

    // Calculate using multiplicative formula
    let coefficient = 1;
    for (let i = 0; i < k; i++) {
        coefficient *= (n - i) / (i + 1);
    }

    return coefficient;
}

// Update the trade sequence display
function updateSequenceDisplay() {
    sequenceList.innerHTML = '';

    // Display the trade results (W/L) and amounts
    tradeResults.forEach((result, index) => {
        const item = document.createElement('div');
        item.className = 'sequence-item';
        if (index === tradeResults.length - 1) {
            item.className += ' current';
        }

        // Get the exact trade data for this specific trade
        const tradeData = tradeHistory[index];

        // Make sure we're using the exact amount that was used for this trade
        const amount = tradeData && typeof tradeData.amount === 'number' ? tradeData.amount : 0;

        item.textContent = `${result.toUpperCase()} $${amount.toFixed(2)}`;
        item.style.backgroundColor = result === 'win' ? 'rgba(16, 185, 129, 0.2)' : 'rgba(239, 68, 68, 0.2)';

        sequenceList.appendChild(item);
    });

    // Add the next predicted amount
    if (tradeResults.length < totalTrades) {
        // Get the next trade amount from the Money Management system
        const nextAmount = calculateMasanielloStake();
        debugLog('Money Management stake amount for sequence display:', nextAmount);

        const nextItem = document.createElement('div');
        nextItem.className = 'sequence-item current';

        // Format the amount to exactly match the spreadsheet values
        nextItem.textContent = `NEXT $${nextAmount.toFixed(2)}`;
        nextItem.style.backgroundColor = 'rgba(245, 158, 11, 0.3)';
        sequenceList.appendChild(nextItem);

        // Store the exact amount without rounding for calculations
        currentTradeAmount = nextAmount;

        // Only update the amount display if we've analyzed the market
        // This prevents overriding the initial "--" display
        if (analyzeBtn.disabled || winBtn.disabled === false || lossBtn.disabled === false) {
            predictionAmount.textContent = `$${nextAmount.toFixed(2)}`;
        }

        // We're hiding the formula parameters to avoid duplication with existing settings
        // The Masaniello formula is still being used for calculations, but we don't display it

        // These calculations are done in the calculateMasanielloStake function
        // No need to calculate them here since we're not displaying them
    }

    // Notify parent about height change since sequence display has changed
    setTimeout(notifyParentAboutHeight, 100);
}

// Analyze the market and generate a prediction
function analyzeMarket() {
    debugLog('Analyzing market');

    // Set trading state to true when analyzing market
    isTrading = true;

    // If this is the first analysis, set the fixed initial capital
    if (fixedInitialCapital === 0) {
        fixedInitialCapital = initialCapital;
        debugLog('Fixed initial capital set to:', fixedInitialCapital);
    }

    // Show analyzing state
    analyzeBtn.disabled = true;
    analyzeBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Analyzing...';
    predictionDirection.textContent = 'Analyzing';
    predictionTime.textContent = 'Analyzing';

    // Calculate the current trade amount using the progressive stake system
    const freshAmount = calculateMasanielloStake();
    debugLog('Progressive stake amount for analysis:', freshAmount);

    // Store the exact amount without rounding for calculations
    currentTradeAmount = freshAmount;

    // Format the amount to exactly match the spreadsheet values
    // This ensures we're using the exact amounts from the Money Management system
    predictionAmount.textContent = `$${freshAmount.toFixed(2)}`;

    debugLog('Fresh trade amount calculated for market analysis:', freshAmount);

    // Simulate AI analysis with a delay (1-3 seconds)
    const analysisTime = 1000 + Math.random() * 2000;

    setTimeout(() => {
        // AI market analysis simulation
        // In a real implementation, this would analyze market patterns

        // Generate direction based on "AI analysis"
        // For simulation, we'll use a random direction with a slight bias
        // In a real implementation, this would be based on technical indicators
        const marketTrend = Math.random(); // Simulated market trend indicator
        let direction;
        let confidence;

        if (marketTrend > 0.6) {
            // Strong uptrend detected
            direction = 'BUY';
            confidence = 80 + Math.floor(Math.random() * 15); // 80-95%
        } else if (marketTrend < 0.4) {
            // Strong downtrend detected
            direction = 'SELL';
            confidence = 80 + Math.floor(Math.random() * 15); // 80-95%
        } else {
            // Mixed signals - less confidence
            direction = Math.random() < 0.5 ? 'BUY' : 'SELL';
            confidence = 65 + Math.floor(Math.random() * 15); // 65-80%
        }

        // Generate expiry time based on "volatility analysis"
        // For simulation, we'll use a weighted random approach
        // In a real implementation, this would be based on volatility indicators
        const volatility = Math.random(); // Simulated volatility indicator
        let expiryMinutes;

        if (volatility > 0.7) {
            // High volatility - shorter expiry
            expiryMinutes = 1;
        } else if (volatility < 0.3) {
            // Low volatility - longer expiry
            expiryMinutes = 5;
        } else {
            // Medium volatility
            expiryMinutes = 2 + Math.floor(Math.random() * 3); // 2-4 minutes
        }

        // Update the UI with the prediction
        predictionDirection.textContent = direction;
        predictionDirection.style.color = direction === 'BUY' ? '#10b981' : '#ef4444';

        predictionTime.textContent = `${expiryMinutes}m`;
        confidenceValue.textContent = `${confidence}%`;
        confidenceFill.style.width = `${confidence}%`;

        // Re-enable the analyze button
        analyzeBtn.disabled = false;
        analyzeBtn.innerHTML = '<i class="fas fa-chart-line"></i> Analyze Market';

        // Enable result buttons
        winBtn.disabled = false;
        lossBtn.disabled = false;

        debugLog('AI Analysis complete', {
            marketTrend,
            volatility,
            direction,
            expiryMinutes,
            confidence,
            amount: currentTradeAmount
        });
    }, analysisTime);
}

// Record a trade result (win or loss)
function recordTradeResult(result) {
    debugLog('Recording trade result:', result);

    // Increment trade count
    tradeCount++;

    // Add the result to the trade results array
    tradeResults.push(result);

    // Update win/loss counts
    if (result === 'win') {
        winCount++;

        // Calculate return on win: Return on Win = Trade Amount × (Profit Factor - 1)
        const profit = currentTradeAmount * (payoutMultiplier - 1);
        profitLoss += profit;

        // Update balance: New Portfolio = Current Portfolio + (Trade Amount × (Profit Factor - 1))
        balance += profit;

        // Update balance display
        if (balanceValue) {
            balanceValue.textContent = `$${balance.toFixed(2)}`;
        }

        debugLog('WIN - Added profit:', profit, 'New balance:', balance);
    } else {
        lossCount++;

        // Update profit/loss
        profitLoss -= currentTradeAmount;

        // Update balance: New Portfolio = Current Portfolio - Trade Amount
        balance -= currentTradeAmount;

        // Update balance display
        if (balanceValue) {
            balanceValue.textContent = `$${balance.toFixed(2)}`;
        }

        debugLog('LOSS - Subtracted:', currentTradeAmount, 'New balance:', balance);
    }

    // Store the current trade amount before calculating the next one
    const tradeAmount = currentTradeAmount;

    // Add to trade history with the EXACT amount used for this trade
    tradeHistory.push({
        id: tradeCount,
        direction: predictionDirection.textContent,
        amount: tradeAmount,
        result: result,
        balance: balance
    });

    // Log the trade with its specific amount
    debugLog('Trade added to history with specific amount:', {
        tradeNumber: tradeCount,
        direction: predictionDirection.textContent,
        exactAmount: tradeAmount,
        result: result
    });

    debugLog('Trade recorded:', {
        id: tradeCount,
        direction: predictionDirection.textContent,
        amount: tradeAmount,
        result: result,
        balance: balance
    });

    // Update the UI
    updateTradeHistory();
    updateSequenceDisplay();

    // Update Money Management info with current results
    const currentWinPercentage = (winCount / tradeCount) * 100;
    eventsWinElement.textContent = winCount;
    winPercentageElement.textContent = `${currentWinPercentage.toFixed(0)}%`;
    eventsLooseElement.textContent = lossCount;
    loosePercentageElement.textContent = `${(100 - currentWinPercentage).toFixed(0)}%`;

    // Calculate the target profit (fixed percentage of initial capital)
    const targetProfitPercentage = 0.10; // 10%
    const calculationCapital = fixedInitialCapital > 0 ? fixedInitialCapital : initialCapital;
    const targetProfit = calculationCapital * targetProfitPercentage;

    // Calculate current profit
    const currentProfit = balance - calculationCapital;

    // Check if we've reached or exceeded the profit target
    if (currentProfit >= targetProfit) {
        // Show congratulation message
        const currentProfitPercent = (currentProfit / calculationCapital) * 100;

        // First send a notification
        window.parent.postMessage({
            action: 'showNotification',
            message: `🎉 Congratulations! 🎉\nYou've reached your profit target of $${targetProfit.toFixed(2)} (${targetProfitPercentage * 100}%)!\nCurrent profit: $${currentProfit.toFixed(2)} (${currentProfitPercent.toFixed(2)}%)`,
            type: 'success'
        }, '*');

        // Then send a message to show the congratulations popup
        window.parent.postMessage({
            action: 'showCongratulationsPopup',
            profit: currentProfit
        }, '*');

        debugLog('Profit target reached after trade:', {
            targetProfit,
            currentProfit,
            currentPortfolio: balance
        });

        // Disable buttons to prevent further trading
        if (analyzeBtn) analyzeBtn.disabled = true;
        if (winBtn) winBtn.disabled = true;
        if (lossBtn) lossBtn.disabled = true;

        // Reset trading session after a delay to allow popup to be shown
        setTimeout(() => {
            resetTradingSession();
            debugLog('Trading session reset after profit target reached');
        }, 3000);

        // Return early to prevent further processing
        return;
    }

    // Calculate next trade amount using the progressive stake system
    const nextAmount = calculateMasanielloStake();
    debugLog('Progressive stake amount for next trade:', nextAmount);

    // Store the exact amount without rounding for calculations
    currentTradeAmount = nextAmount;

    // Format the amount to exactly match the spreadsheet values
    // This ensures we're using the exact amounts from the Money Management system
    predictionAmount.textContent = `$${nextAmount.toFixed(2)}`;

    debugLog('Updated trade amount:', {
        previousAmount: tradeAmount,
        newAmount: nextAmount,
        change: nextAmount - tradeAmount
    });

    // Disable result buttons until next analysis
    winBtn.disabled = true;
    lossBtn.disabled = true;

    // Check if we've reached the target number of wins
    if (winCount >= winTrades) {
        // Calculate the target profit using the Masaniello formula
        const profitFactor = payoutMultiplier; // Use the payout multiplier as the profit factor
        const lossTrades = totalTrades - winTrades;

        // Use the fixed initial capital for calculations
        // This ensures the initial capital remains constant during trading
        const calculationCapital = fixedInitialCapital > 0 ? fixedInitialCapital : initialCapital;

        // Calculate the average loss per trade (as a percentage of initial capital)
        const averageLossPercent = 0.025; // 2.5% of initial capital per loss

        // Calculate the expected gross loss
        const grossLoss = calculationCapital * averageLossPercent * lossTrades;

        // Calculate the win profit using the Masaniello formula
        // Win Profit = (Profit Factor - 1) × Gross Loss
        const targetProfit = (profitFactor - 1) * grossLoss;

        // Calculate target profit percentage (Ratio)
        const targetProfitPercent = (targetProfit / calculationCapital) * 100;

        // Calculate final capital
        const targetFinal = calculationCapital + targetProfit;

        // We don't set the balance to the target final capital anymore
        // Instead, we'll use the actual balance from the trades
        // This ensures we're showing the real profit, not the theoretical target
        const actualProfit = balance - calculationCapital;
        debugLog('Target final capital:', targetFinal, 'Actual final capital:', balance,
                'Target profit:', targetProfit, 'Actual profit:', actualProfit);

        // Check if we're in profit or loss
        if (actualProfit >= 0) {
            // In profit - show congratulation message
            const actualProfitPercent = (actualProfit / calculationCapital) * 100;
            const message = `🎉 Congratulations! 🎉\nTarget of ${winTrades} wins reached with profit!\n` +
                          `Profit: $${actualProfit.toFixed(2)} (${actualProfitPercent.toFixed(2)}%)`;

            // Send notification
            window.parent.postMessage({
                action: 'showNotification',
                message: message,
                type: 'success'
            }, '*');

            // Show congratulations popup
            window.parent.postMessage({
                action: 'showCongratulationsPopup',
                profit: actualProfit
            }, '*');
        } else {
            // In loss - show encouraging message
            const lossPercent = (Math.abs(actualProfit) / calculationCapital) * 100;
            const message = `Target of ${winTrades} wins reached, but with a loss.\n` +
                          `Loss: $${Math.abs(actualProfit).toFixed(2)} (${lossPercent.toFixed(2)}%)\n` +
                          `Don't worry! Trading has ups and downs. Try adjusting your strategy and try again!`;

            // Send notification
            window.parent.postMessage({
                action: 'showNotification',
                message: message,
                type: 'warning'
            }, '*');

            // Show encouraging popup
            window.parent.postMessage({
                action: 'showEncouragementPopup',
                loss: Math.abs(actualProfit)
            }, '*');
        }

        debugLog('Win target reached with profit/loss:', actualProfit);

        // Disable buttons to prevent further trading
        if (analyzeBtn) analyzeBtn.disabled = true;
        if (winBtn) winBtn.disabled = true;
        if (lossBtn) lossBtn.disabled = true;

        // Reset trading session after a delay to allow popup to be shown
        setTimeout(() => {
            resetTradingSession();
            debugLog('Trading session reset after win target reached');
        }, 3000);
    }
    // Check if we've completed all planned trades without reaching the target wins
    else if (tradeCount >= totalTrades) {
        // Use the fixed initial capital for calculations
        const calculationCapital = fixedInitialCapital > 0 ? fixedInitialCapital : initialBalance;

        // Calculate final profit
        const finalProfit = balance - calculationCapital;
        const finalProfitPercent = (finalProfit / calculationCapital) * 100;

        // Determine message type and popup based on profit/loss
        if (finalProfit >= 0) {
            // In profit - show congratulation message
            const message = `🎉 Trading session complete with profit!\n` +
                          `Profit: $${finalProfit.toFixed(2)} (${finalProfitPercent.toFixed(2)}%)`;

            // Send notification
            window.parent.postMessage({
                action: 'showNotification',
                message: message,
                type: 'success'
            }, '*');

            // Show congratulations popup
            window.parent.postMessage({
                action: 'showCongratulationsPopup',
                profit: finalProfit
            }, '*');
        } else {
            // In loss - show encouraging message
            const lossPercent = (Math.abs(finalProfit) / calculationCapital) * 100;
            const message = `Trading session complete, but with a loss.\n` +
                          `Loss: $${Math.abs(finalProfit).toFixed(2)} (${lossPercent.toFixed(2)}%)\n` +
                          `Don't worry! Trading has ups and downs. Try adjusting your strategy and try again!`;

            // Send notification
            window.parent.postMessage({
                action: 'showNotification',
                message: message,
                type: 'warning'
            }, '*');

            // Show encouraging popup
            window.parent.postMessage({
                action: 'showEncouragementPopup',
                loss: Math.abs(finalProfit)
            }, '*');
        }

        // Disable buttons to prevent further trading
        if (analyzeBtn) analyzeBtn.disabled = true;
        if (winBtn) winBtn.disabled = true;
        if (lossBtn) lossBtn.disabled = true;

        // Reset trading session after a delay to allow popup to be shown
        setTimeout(() => {
            resetTradingSession();
            debugLog('Trading session reset after all trades completed');
        }, 3000);
    }

    // Send trade result to parent
    window.parent.postMessage({
        action: 'phoenixTradeResult',
        result: result,
        amount: currentTradeAmount,
        profitLoss: profitLoss,
        nextAmount: nextAmount,
        balance: balance
    }, '*');
}

// Update the trade history table
function updateTradeHistory() {
    historyTableBody.innerHTML = '';

    // Display the most recent trades first (up to 10)
    const recentTrades = [...tradeHistory].reverse().slice(0, 10);

    // Log the trade history for debugging
    debugLog('Updating trade history display with trades:', recentTrades);

    recentTrades.forEach(trade => {
        const row = document.createElement('tr');

        const idCell = document.createElement('td');
        idCell.textContent = trade.id;
        row.appendChild(idCell);

        const directionCell = document.createElement('td');
        directionCell.textContent = trade.direction;
        directionCell.style.color = trade.direction === 'BUY' ? '#10b981' : '#ef4444';
        row.appendChild(directionCell);

        const amountCell = document.createElement('td');
        // Ensure we're using the exact amount stored for this specific trade
        const displayAmount = trade.amount || 0;
        amountCell.textContent = `$${displayAmount.toFixed(2)}`;
        row.appendChild(amountCell);

        const resultCell = document.createElement('td');
        // Calculate the return value based on the result
        if (trade.result === 'win') {
            const returnValue = trade.amount * (payoutMultiplier - 1);
            resultCell.textContent = `+$${returnValue.toFixed(2)}`;
            resultCell.className = 'win-result';
        } else {
            resultCell.textContent = `-$${trade.amount.toFixed(2)}`;
            resultCell.className = 'loss-result';
        }
        row.appendChild(resultCell);

        historyTableBody.appendChild(row);
    });

    // Notify parent about height change since trade history has changed
    setTimeout(notifyParentAboutHeight, 100);
}

// Apply settings from the input fields
function applySettings() {
    // Get values from input fields
    const newInitialCapital = parseFloat(initialCapitalInput.value);
    const newTotalTrades = parseInt(totalTradesInput.value);
    const newWinTrades = parseInt(winTradesInput.value);
    const newPayoutMultiplier = parseFloat(payoutMultiplierInput.value);

    // Validate initial capital
    if (isNaN(newInitialCapital) || newInitialCapital < 1 || newInitialCapital > 10000) {
        initialCapitalInput.value = 50; // Reset to default
        return;
    }

    // Validate total trades
    if (isNaN(newTotalTrades) || newTotalTrades < 5 || newTotalTrades > 30) {
        totalTradesInput.value = 10; // Reset to default
        return;
    }

    // Validate win trades
    if (isNaN(newWinTrades) || newWinTrades < 1 || newWinTrades > 20) {
        winTradesInput.value = 3; // Reset to default
        return;
    }

    // Validate payout multiplier
    if (isNaN(newPayoutMultiplier) || newPayoutMultiplier < 1.0 || newPayoutMultiplier > 2.0) {
        payoutMultiplierInput.value = 1.85; // Reset to default
        return;
    }

    // Ensure win trades is not greater than total trades
    if (newWinTrades > newTotalTrades) {
        winTradesInput.value = Math.floor(newTotalTrades * 0.3); // Set to 30% of total trades
        return;
    }

    // Check if any values have changed
    const settingsChanged =
        initialCapital !== newInitialCapital ||
        totalTrades !== newTotalTrades ||
        winTrades !== newWinTrades ||
        payoutMultiplier !== newPayoutMultiplier;

    // Update the Masaniello parameters
    initialCapital = newInitialCapital;
    initialBalance = newInitialCapital; // Also update initialBalance to match
    balance = newInitialCapital; // Update current balance to match initial capital

    // Set the fixed initial capital only if not trading
    if (!isTrading) {
        fixedInitialCapital = newInitialCapital;
    }

    totalTrades = newTotalTrades;
    winTrades = newWinTrades;
    payoutMultiplier = newPayoutMultiplier;

    // Update balance display
    if (balanceValue) {
        balanceValue.textContent = `$${balance.toFixed(2)}`;
    }

    // Save settings to localStorage for persistence
    localStorage.setItem('phoenixInitialCapital', initialCapital);
    localStorage.setItem('phoenixTotalTrades', totalTrades);
    localStorage.setItem('phoenixWinTrades', winTrades);
    localStorage.setItem('phoenixPayoutMultiplier', payoutMultiplier);

    // Log the updated parameters
    debugLog('Applied new Masaniello parameters:', {
        initialCapital,
        totalTrades,
        winTrades,
        payoutMultiplier,
        settingsChanged
    });

    // Calculate and update Masaniello info
    updateMasanielloInfo();

    // Reset trade results if settings have changed
    if (settingsChanged && tradeResults.length > 0) {
        // Ask for confirmation before resetting
        if (confirm('Changing settings will reset your current trading session. Continue?')) {
            tradeResults = [];
            tradeHistory = [];
            tradeCount = 0;
            winCount = 0;
            lossCount = 0;
            profitLoss = 0;

            // Update UI
            updateTradeHistory();
            updateSequenceDisplay();
            updateMasanielloInfo();

            debugLog('Trade results reset due to settings change');
        }
    }

    // Calculate new trade amount with the updated settings
    const newAmount = calculateMasanielloStake();

    // Store the exact amount without rounding for calculations
    currentTradeAmount = newAmount;

    // Format the amount to exactly match the spreadsheet values
    // This ensures we're using the exact amounts from the Masaniello system
    predictionAmount.textContent = `$${newAmount.toFixed(2)}`;

    debugLog('New trade amount calculated after settings change:', newAmount);

    // Show notification
    window.parent.postMessage({
        action: 'showNotification',
        message: 'Settings applied successfully!',
        type: 'success'
    }, '*');

    debugLog('Settings applied:', {
        initialCapital,
        totalTrades,
        winTrades,
        payoutMultiplier,
        settingsChanged
    });
}

// Update Masaniello parameters based on balance
function updateMasanielloParameters() {
    // If we have a balance, use it to set the initial capital
    if (balance > 0) {
        initialCapital = balance;
        initialBalance = balance; // Also update initialBalance to match
        initialCapitalInput.value = initialCapital;

        // Set the fixed initial capital only if it hasn't been set yet or if not trading
        if (fixedInitialCapital === 0 || !isTrading) {
            fixedInitialCapital = balance;
        }
    } else {
        // Set a default initial capital if balance is not available
        initialCapital = 1000; // Use a larger default value
        initialBalance = 1000; // Also set initialBalance
        initialCapitalInput.value = initialCapital;

        // Set the fixed initial capital only if it hasn't been set yet or if not trading
        if (fixedInitialCapital === 0 || !isTrading) {
            fixedInitialCapital = 1000;
        }
    }

    // Ensure initial capital is at least 100 to make Masaniello calculations more meaningful
    if (initialCapital < 100) {
        initialCapital = 100;
        initialBalance = 100; // Also update initialBalance
        initialCapitalInput.value = initialCapital;

        // Set the fixed initial capital only if it hasn't been set yet or if not trading
        if (fixedInitialCapital === 0 || !isTrading) {
            fixedInitialCapital = 100;
        }
    }

    // Log the updated parameters
    debugLog('Updated Masaniello parameters from balance:', {
        initialCapital,
        fixedInitialCapital,
        totalTrades,
        winTrades,
        payoutMultiplier
    });

    // Update the Masaniello info with the current parameters
    updateMasanielloInfo();

    // Calculate the initial trade amount
    currentTradeAmount = calculateMasanielloStake();

    debugLog('Updated Masaniello parameters:', {
        initialCapital,
        fixedInitialCapital,
        totalTrades,
        winTrades,
        payoutMultiplier,
        winProbability,
        currentTradeAmount
    });
}

// Get the current balance from the parent window
function getCurrentBalance() {
    // If we already have a balance, use it
    if (balance > 0) {
        return balance;
    }

    // Otherwise, use a default value
    return 1000;
}

// Start a new trading session
function startTradingSession() {
    // Reset trading session variables
    resetTradingSession();

    // Get the current balance
    balance = getCurrentBalance();
    initialBalance = balance;

    // Update the balance display
    if (balanceValue) {
        balanceValue.textContent = `$${balance.toFixed(2)}`;
    }

    // Set the initial capital for Masaniello calculations
    initialCapital = balance;

    // Update the Masaniello info display
    updateMasanielloInfo();

    // Enable the analyze button
    analyzeBtn.disabled = false;
    analyzeBtn.innerHTML = '<i class="fas fa-brain"></i> Analyze Market';

    // Disable the win/loss buttons until analysis is complete
    winBtn.disabled = true;
    lossBtn.disabled = true;

    // Set trading state to false until first analysis
    isTrading = false;

    debugLog('Started new trading session with balance:', balance);
}

// Reset the trading session
function resetTradingSession() {
    isTrading = false;
    tradeCount = 0;
    winCount = 0;
    lossCount = 0;
    profitLoss = 0;
    tradeHistory = [];
    tradeResults = [];

    // Reset fixed initial capital since trading has ended
    fixedInitialCapital = 0;
    debugLog('Reset fixed initial capital');

    // Update UI
    updateTradeHistory();
    updateSequenceDisplay();

    // Reset prediction display
    predictionDirection.textContent = '--';
    predictionDirection.style.color = '';
    predictionTime.textContent = '--';
    predictionAmount.textContent = '--';

    // Reset current trade amount
    currentTradeAmount = 0;

    confidenceValue.textContent = '75%';
    confidenceFill.style.width = '75%';

    // Update Masaniello info display with consistent calculations
    updateMasanielloInfo();

    // Disable result buttons
    winBtn.disabled = true;
    lossBtn.disabled = true;
}

// Listen for messages from the parent window
window.addEventListener('message', function(event) {
    try {
        const message = event.data;

        // Skip if message is null or not an object
        if (!message || typeof message !== 'object') {
            return;
        }

        debugLog('Received message:', message);

        if (message.action === 'updateBalance') {
            balance = message.balance;

            // Update the balance display
            if (balanceValue) {
                balanceValue.textContent = `$${balance.toFixed(2)}`;
            }

            // Only update initialBalance and initialCapital if not currently trading
            if (!isTrading) {
                initialBalance = balance;
                initialCapital = balance;
                initialCapitalInput.value = initialCapital;

                // Set the fixed initial capital only if not trading
                fixedInitialCapital = balance;

                // Start a new trading session with the updated balance
                startTradingSession();
            } else {
                // Just update the Masaniello parameters
                updateMasanielloParameters();
            }

            debugLog('Balance updated:', balance, 'Fixed Initial Capital:', fixedInitialCapital);
        } else if (message.action === 'tradeResult') {
            // Handle trade result from parent
            debugLog('Received trade result from parent:', message);

            // Record the trade result
            recordTradeResult(message.result);
        } else if (message.action === 'forceHeightUpdate') {
            // Force a height update
            debugLog('Received force height update request');
            notifyParentAboutHeight();

            // Force multiple height updates to ensure proper sizing
            setTimeout(notifyParentAboutHeight, 100);
            setTimeout(notifyParentAboutHeight, 500);
            setTimeout(notifyParentAboutHeight, 1000);
        }
    } catch (error) {
        console.error('Error processing message:', error);
    }
});

// Function to notify parent about content height
function notifyParentAboutHeight() {
    // Get the actual content height
    const contentHeight = document.body.scrollHeight;

    // Ensure a minimum height of 1200px to show all content
    const height = Math.max(contentHeight, 1200);

    debugLog('Notifying parent about content height:', height, '(actual content height:', contentHeight, ')');
    window.parent.postMessage({
        action: 'resizeIframe',
        height: height
    }, '*');
}

// Initialize when the document is loaded
document.addEventListener('DOMContentLoaded', function() {
    debugLog('DOM content loaded, initializing Phoenix System');
    initializeInterface();

    // Force multiple height checks at different intervals to ensure proper sizing
    setTimeout(notifyParentAboutHeight, 100);
    setTimeout(notifyParentAboutHeight, 500);
    setTimeout(notifyParentAboutHeight, 1000);
    setTimeout(notifyParentAboutHeight, 2000);

    // Set up a periodic check for height changes
    setInterval(notifyParentAboutHeight, 1000);
});

// Also check height when window loads (after all resources are loaded)
window.addEventListener('load', function() {
    debugLog('Window loaded, checking height');
    notifyParentAboutHeight();

    // Force additional checks after window load
    setTimeout(notifyParentAboutHeight, 500);
    setTimeout(notifyParentAboutHeight, 1000);
})