var Ne=Object.defineProperty;var ze=(i,e,n)=>e in i?Ne(i,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):i[e]=n;var L=(i,e,n)=>ze(i,typeof e!="symbol"?e+"":e,n);import{S as O,i as U,s as G,a as ce,b as Te,I as We,J as Le,K as Oe,L as Ue,h as $,d as fe,M as Ge,g as Je,n as Q,j as he,T as z,Q as k,y as w,D as I,c as v,e as p,z as _,f as q,u as m,t as u,B as S,_ as je,G as D,H as J,E as T,q as P,r as H,a2 as Z,ae as le,ag as Fe,a4 as Ke,ap as ye,a0 as Qe}from"./SpinnerAugment-BRymMBwV.js";import"./design-system-init-BYyh-Ygh.js";import{s as Ve}from"./index-yERhhNs7.js";import"./design-system-init-kXy0_QGq.js";import{W as h,e as F,u as V,o as X,h as Xe}from"./BaseButton-rKFNr-KO.js";import{T as ee,M as Ye}from"./TextTooltipAugment-VmEmcMVL.js";import{S as de,a as Ze,T as et,b as tt,v as nt,c as st}from"./StatusIndicator-bsJDs3Ra.js";import{R as C}from"./types-LfaCSdmF.js";import{T as te}from"./Content-CZt_q_72.js";import{C as rt}from"./CardAugment-BpvKVhgc.js";import{I as ge}from"./IconButtonAugment-5yqT_m78.js";import{T as De}from"./terminal-BinWa3Yp.js";import{S as at,b as ot}from"./types-DvVg976p.js";import{A as it}from"./augment-logo-Cb6FLr8P.js";import"./index-DNgdG9gK.js";import"./globals-D0QH3NT1.js";import"./chat-types-NgqNgjwU.js";class ct{constructor(e,n=void 0,t,s){L(this,"subscribers",new Set);this._msgBroker=e,this._state=n,this.validateState=t,this._storeId=s,n&&this.setStateInternal(n)}subscribe(e){return this.subscribers.add(e),e(this),()=>{this.subscribers.delete(e)}}notifySubscribers(){this.subscribers.forEach(e=>e(this))}get state(){return this._state}get storeId(){return this._storeId}shouldAcceptMessage(e,n){return e.id===this.storeId&&this.validateState(n)}update(e){const n=e(this._state);n!==void 0&&this.setStateInternal(n)}setState(e){this.setStateInternal(e)}async setStateInternal(e){JSON.stringify(this._state)!==JSON.stringify(e)&&(this._state=e,this._msgBroker.postMessage({type:h.updateSharedWebviewState,data:e,id:this.storeId}))}async fetchStateFromExtension(){const e=await this._msgBroker.send({type:h.getSharedWebviewState,id:this.storeId,data:{}});e.type===h.getSharedWebviewStateResponse&&this.shouldAcceptMessage(e,e.data)&&(this._state=e.data,this.notifySubscribers())}handleMessageFromExtension(e){switch(e.data.type){case h.updateSharedWebviewState:case h.getSharedWebviewStateResponse:return!!this.shouldAcceptMessage(e.data,e.data.data)&&(this._state=e.data.data,this.notifySubscribers(),!0);default:return!1}}}function lt(i){let e,n,t=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 640 512"},i[0]],s={};for(let r=0;r<t.length;r+=1)s=ce(s,t[r]);return{c(){e=Te("svg"),n=new We(!0),this.h()},l(r){e=Le(r,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var o=Oe(e);n=Ue(o,!0),o.forEach($),this.h()},h(){n.a=null,fe(e,s)},m(r,o){Ge(r,e,o),n.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M38.8 5.1C28.4-3.1 13.3-1.2 5.1 9.2s-6.3 25.5 4.1 33.7l592 464c10.4 8.2 25.5 6.3 33.7-4.1s6.3-25.5-4.1-33.7L481.4 352c9.4-.4 18.1-4.9 23.9-12.3 6.1-7.8 8.2-17.9 5.8-27.5l-6.2-25c-10.3-41.3-35.4-75.7-68.7-98.3L428.9 96l-3.7-48H456c4.4 0 8.6-1.2 12.2-3.3 7-4.2 11.8-11.9 11.8-20.7 0-13.3-10.7-24-24-24H184c-13.3 0-24 10.7-24 24 0 8.8 4.8 16.5 11.8 20.7 3.6 2.1 7.7 3.3 12.2 3.3h30.8l-3.7 48-3.2 41.6zm214.5 168.1 9.3-121.5c.1-1.2.1-2.5.1-3.7h114.5c0 1.2 0 2.5.1 3.7l10.8 140.9c1.1 14.6 8.8 27.8 20.9 36 23.9 16.2 41.7 40.8 49.1 70.2l1.3 5.1H420l-76-59.6V216c0-13.3-10.7-24-24-24-10.4 0-19.2 6.6-22.6 15.8l-44.2-34.6zM344 367l-80-63h-83.5l1.3-5.1c4-16.1 11.2-30.7 20.7-43.3l-37.7-29.7c-13.7 17.8-23.9 38.6-29.6 61.4l-6.2 25c-2.4 9.6-.2 19.7 5.8 27.5s15.4 12.3 25.2 12.3h136v136c0 13.3 10.7 24 24 24s24-10.7 24-24v-121z"/>',e)},p(r,[o]){fe(e,s=Je(t,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 640 512"},1&o&&r[0]]))},i:Q,o:Q,d(r){r&&$(e)}}}function dt(i,e,n){return i.$$set=t=>{n(0,e=ce(ce({},e),he(t)))},[e=he(e)]}class gt extends O{constructor(e){super(),U(this,e,dt,lt,G,{})}}class mt extends Error{constructor(e){super(e),this.name="StreamRetryExhaustedError"}}class ut{constructor(e,n,t,s,r=5,o=4e3){L(this,"_isCancelled",!1);L(this,"streamId");this.agentId=e,this.lastProcessedSequenceId=n,this.startStreamFn=t,this.cancelStreamFn=s,this.maxRetries=r,this.baseDelay=o,this.streamId=crypto.randomUUID()}get isCancelled(){return this._isCancelled}async cancel(){this._isCancelled=!0,await this.cancelStreamFn(this.streamId)}async*getStream(){let e=0;for(;!this._isCancelled;){const n=this.startStreamFn(this.agentId,this.streamId,this.lastProcessedSequenceId);try{for await(const t of n){if(this._isCancelled)return;e=0,yield t}return}catch(t){const s=t instanceof Error?t.message:String(t);if(s===at&&(this._isCancelled=!0),this._isCancelled)return;if(e++,e>this.maxRetries)throw new mt(`Failed after ${this.maxRetries} attempts: ${s}`);let r=this.baseDelay*2**(e-1);s===ot?r=0:yield{errorMessage:"There was an error connecting to the remote agent.",retryAt:new Date(Date.now()+r)},console.warn(`Retrying remote agent history stream in ${r/1e3} seconds... (Attempt ${e} of ${this.maxRetries})`),await new Promise(o=>setTimeout(o,r));continue}}}}class ne{constructor(e){L(this,"_msgBroker");L(this,"_activeRetryStreams",new Map);this._msgBroker=e}hasActiveHistoryStream(e){return this._activeRetryStreams.has(e)}getActiveHistoryStream(e){return this._activeRetryStreams.get(e)}get activeHistoryStreams(){return this._activeRetryStreams}async sshToRemoteAgent(e){const n=await this._msgBroker.send({type:h.remoteAgentSshRequest,data:{agentId:e}},1e4);return!!n.data.success||(console.error("Failed to connect to remote agent:",n.data.error),!1)}async deleteRemoteAgent(e){return(await this._msgBroker.send({type:h.deleteRemoteAgentRequest,data:{agentId:e}},1e4)).data.success}showRemoteAgentHomePanel(){this._msgBroker.postMessage({type:h.showRemoteAgentHomePanel})}closeRemoteAgentHomePanel(){this._msgBroker.postMessage({type:h.closeRemoteAgentHomePanel})}async getRemoteAgentNotificationEnabled(e){return(await this._msgBroker.send({type:h.getRemoteAgentNotificationEnabledRequest,data:{agentIds:e}})).data}async setRemoteAgentNotificationEnabled(e,n){await this._msgBroker.send({type:h.setRemoteAgentNotificationEnabled,data:{agentId:e,enabled:n}})}async deleteRemoteAgentNotificationEnabled(e){await this._msgBroker.send({type:h.deleteRemoteAgentNotificationEnabled,data:{agentId:e}})}async notifyRemoteAgentReady(e){await this._msgBroker.send({type:h.remoteAgentNotifyReady,data:{agentId:e}})}showRemoteAgentDiffPanel(e){this._msgBroker.postMessage({type:h.showRemoteAgentDiffPanel,data:e})}closeRemoteAgentDiffPanel(){this._msgBroker.postMessage({type:h.closeRemoteAgentDiffPanel})}async getRemoteAgentChatHistory(e,n,t=1e4){return await this._msgBroker.send({type:h.getRemoteAgentChatHistoryRequest,data:{agentId:e,lastProcessedSequenceId:n}},t)}async sendRemoteAgentChatRequest(e,n,t=1e4){return this._msgBroker.send({type:h.remoteAgentChatRequest,data:{agentId:e,requestDetails:n}},t)}async interruptRemoteAgent(e,n=1e4){return await this._msgBroker.send({type:h.remoteAgentInterruptRequest,data:{agentId:e}},n)}async createRemoteAgent(e,n,t,s,r,o,l=1e4){return await this._msgBroker.send({type:h.createRemoteAgentRequest,data:{prompt:e,workspaceSetup:n,setupScript:t,isSetupScriptAgent:s,modelId:r,remoteAgentCreationMetrics:o}},l)}async getRemoteAgentOverviews(e=1e4){return await this._msgBroker.send({type:h.getRemoteAgentOverviewsRequest},e)}async listSetupScripts(e=5e3){return await this._msgBroker.send({type:h.listSetupScriptsRequest},e)}async saveSetupScript(e,n,t,s=5e3){return await this._msgBroker.send({type:h.saveSetupScriptRequest,data:{name:e,content:n,location:t}},s)}async deleteSetupScript(e,n,t=5e3){return await this._msgBroker.send({type:h.deleteSetupScriptRequest,data:{name:e,location:n}},t)}async renameSetupScript(e,n,t,s=5e3){return await this._msgBroker.send({type:h.renameSetupScriptRequest,data:{oldName:e,newName:n,location:t}},s)}async getRemoteAgentWorkspaceLogs(e,n,t,s=1e4){return await this._msgBroker.send({type:h.remoteAgentWorkspaceLogsRequest,data:{agentId:e,lastProcessedStep:n,lastProcessedSequenceId:t}},s)}async saveLastRemoteAgentSetup(e,n,t){return await this._msgBroker.send({type:h.saveLastRemoteAgentSetupRequest,data:{lastRemoteAgentGitRepoUrl:e,lastRemoteAgentGitBranch:n,lastRemoteAgentSetupScript:t}})}async getLastRemoteAgentSetup(){return await this._msgBroker.send({type:h.getLastRemoteAgentSetupRequest})}async*startRemoteAgentHistoryStream(e,n,t,s=6e4,r=3e5){const o={type:h.remoteAgentHistoryStreamRequest,data:{streamId:n,agentId:e,lastProcessedSequenceId:t}},l=this._msgBroker.stream(o,s,r);for await(const c of l)yield c.data}async*startRemoteAgentHistoryStreamWithRetry(e,n,t=5,s=4e3){var o;const r=new ut(e,n,(l,c,a)=>this.startRemoteAgentHistoryStream(l,c,a),l=>this._closeRemoteAgentHistoryStream(l),t,s);(o=this._activeRetryStreams.get(e))==null||o.cancel(),this._activeRetryStreams.set(e,r);try{yield*r.getStream()}finally{r.isCancelled||this._activeRetryStreams.delete(e)}}cancelRemoteAgentHistoryStream(e){const n=this._activeRetryStreams.get(e);n&&(n.cancel(),this._activeRetryStreams.delete(e))}async _closeRemoteAgentHistoryStream(e){await this._msgBroker.send({type:h.cancelRemoteAgentHistoryStreamRequest,data:{streamId:e}})}cancelAllRemoteAgentHistoryStreams(){this._activeRetryStreams.forEach(e=>{e.cancel()}),this._activeRetryStreams.clear()}dispose(){this.cancelAllRemoteAgentHistoryStreams()}async getPinnedAgentsFromStore(){try{return(await this._msgBroker.send({type:h.getRemoteAgentPinnedStatusRequest,data:{}})).data}catch(e){return console.error("Failed to get pinned agents from store:",e),{}}}async savePinnedAgentToStore(e,n){try{await this._msgBroker.send({type:h.setRemoteAgentPinnedStatus,data:{agentId:e,isPinned:n}})}catch(t){console.error("Failed to save pinned agent to store:",t)}}async deletePinnedAgentFromStore(e){try{await this._msgBroker.send({type:h.deleteRemoteAgentPinnedStatus,data:{agentId:e}})}catch(n){console.error("Failed to delete pinned agent from store:",n)}}async openDiffInBuffer(e,n,t){return await this._msgBroker.send({type:h.openDiffInBuffer,data:{oldContents:e,newContents:n,filePath:t}})}async pauseRemoteAgentWorkspace(e){return await this._msgBroker.send({type:h.remoteAgentPauseRequest,data:{agentId:e}},3e4)}async resumeRemoteAgentWorkspace(e){return await this._msgBroker.send({type:h.remoteAgentResumeRequest,data:{agentId:e}},9e4)}async reportRemoteAgentEvent(e){await this._msgBroker.send({type:h.reportRemoteAgentEvent,data:e})}}L(ne,"key","remoteAgentsClient");function we(i){return function(e){try{if(isNaN(e.getTime()))return"Unknown time";const n=new Date().getTime()-e.getTime(),t=Math.floor(n/1e3),s=Math.floor(t/60),r=Math.floor(s/60),o=Math.floor(r/24);return t<60?`${t}s ago`:s<60?`${s}m ago`:r<24?`${r}h ago`:o<30?`${o}d ago`:e.toLocaleDateString()}catch(n){return console.error("Error formatting date:",n),"Unknown time"}}(new Date(i))}function $t(i){let e,n=i[0]?"Running in the cloud":"Running locally";return{c(){e=D(n)},m(t,s){p(t,e,s)},p(t,s){1&s&&n!==(n=t[0]?"Running in the cloud":"Running locally")&&J(e,n)},d(t){t&&$(e)}}}function pt(i){let e;return{c(){e=D("Unknown time")},m(n,t){p(n,e,t)},p:Q,d(n){n&&$(e)}}}function ft(i){let e;return{c(){e=D(i[3])},m(n,t){p(n,e,t)},p(n,t){8&t&&J(e,n[3])},d(n){n&&$(e)}}}function ht(i){let e,n,t,s=i[1]===C.agentRunning?"Last updated":"Started";function r(c,a){return c[2]?ft:pt}let o=r(i),l=o(i);return{c(){e=D(s),n=I(),l.c(),t=T()},m(c,a){p(c,e,a),p(c,n,a),l.m(c,a),p(c,t,a)},p(c,a){2&a&&s!==(s=c[1]===C.agentRunning?"Last updated":"Started")&&J(e,s),o===(o=r(c))&&l?l.p(c,a):(l.d(1),l=o(c),l&&(l.c(),l.m(t.parentNode,t)))},d(c){c&&($(e),$(n),$(t)),l.d(c)}}}function yt(i){let e,n,t,s,r,o;return n=new z({props:{size:1,color:"secondary",class:"location-text",$$slots:{default:[$t]},$$scope:{ctx:i}}}),r=new z({props:{size:1,color:"secondary",class:"time-text",$$slots:{default:[ht]},$$scope:{ctx:i}}}),{c(){e=k("div"),w(n.$$.fragment),t=I(),s=k("div"),w(r.$$.fragment),v(s,"class","time-container"),v(e,"class","agent-card-footer svelte-1qwlkoj")},m(l,c){p(l,e,c),_(n,e,null),q(e,t),q(e,s),_(r,s,null),o=!0},p(l,[c]){const a={};33&c&&(a.$$scope={dirty:c,ctx:l}),n.$set(a);const d={};46&c&&(d.$$scope={dirty:c,ctx:l}),r.$set(d)},i(l){o||(m(n.$$.fragment,l),m(r.$$.fragment,l),o=!0)},o(l){u(n.$$.fragment,l),u(r.$$.fragment,l),o=!1},d(l){l&&$(e),S(n),S(r)}}}function wt(i,e,n){let{isRemote:t=!1}=e,{status:s}=e,{timestamp:r}=e,o=we(r);const l=function(c,a){let d=1e3;const g=new Date(c),R=setInterval(()=>{const x=Math.floor((new Date().getTime()-g.getTime())/1e3/60);x>=1&&(d=6e4),x>=60&&(d=36e5),x>=1440&&(d=864e5),a(we(c))},d);return()=>clearInterval(R)}(r,c=>{n(3,o=c)});return je(()=>{l()}),i.$$set=c=>{"isRemote"in c&&n(0,t=c.isRemote),"status"in c&&n(1,s=c.status),"timestamp"in c&&n(2,r=c.timestamp)},[t,s,r,o]}class _t extends O{constructor(e){super(),U(this,e,wt,yt,G,{isRemote:0,status:1,timestamp:2})}}function St(i){let e;return{c(){e=D(i[0])},m(n,t){p(n,e,t)},p(n,t){1&t&&J(e,n[0])},d(n){n&&$(e)}}}function vt(i){let e,n,t;return n=new z({props:{size:1,color:"secondary",$$slots:{default:[St]},$$scope:{ctx:i}}}),{c(){e=k("div"),w(n.$$.fragment),v(e,"class","task-text-container svelte-1tatwxk")},m(s,r){p(s,e,r),_(n,e,null),t=!0},p(s,r){const o={};9&r&&(o.$$scope={dirty:r,ctx:s}),n.$set(o)},i(s){t||(m(n.$$.fragment,s),t=!0)},o(s){u(n.$$.fragment,s),t=!1},d(s){s&&$(e),S(n)}}}function _e(i){let e,n,t;return n=new z({props:{size:1,color:i[1]==="error"?"error":"neutral",$$slots:{default:[Rt]},$$scope:{ctx:i}}}),{c(){e=k("div"),w(n.$$.fragment),v(e,"class","task-status-indicator svelte-1tatwxk")},m(s,r){p(s,e,r),_(n,e,null),t=!0},p(s,r){const o={};2&r&&(o.color=s[1]==="error"?"error":"neutral"),10&r&&(o.$$scope={dirty:r,ctx:s}),n.$set(o)},i(s){t||(m(n.$$.fragment,s),t=!0)},o(s){u(n.$$.fragment,s),t=!1},d(s){s&&$(e),S(n)}}}function Rt(i){let e,n=i[1]==="error"?"!":i[1]==="warning"?"⚠":"";return{c(){e=D(n)},m(t,s){p(t,e,s)},p(t,s){2&s&&n!==(n=t[1]==="error"?"!":t[1]==="warning"?"⚠":"")&&J(e,n)},d(t){t&&$(e)}}}function At(i){let e,n,t,s,r,o,l;r=new ee({props:{content:i[0],triggerOn:[te.Hover],maxWidth:"400px",$$slots:{default:[vt]},$$scope:{ctx:i}}});let c=(i[1]==="error"||i[1]==="warning")&&_e(i);return{c(){e=k("div"),n=k("div"),s=I(),w(r.$$.fragment),o=I(),c&&c.c(),v(n,"class",t="bullet-point "+i[2]+" svelte-1tatwxk"),v(e,"class","task-item svelte-1tatwxk")},m(a,d){p(a,e,d),q(e,n),q(e,s),_(r,e,null),q(e,o),c&&c.m(e,null),l=!0},p(a,[d]){(!l||4&d&&t!==(t="bullet-point "+a[2]+" svelte-1tatwxk"))&&v(n,"class",t);const g={};1&d&&(g.content=a[0]),9&d&&(g.$$scope={dirty:d,ctx:a}),r.$set(g),a[1]==="error"||a[1]==="warning"?c?(c.p(a,d),2&d&&m(c,1)):(c=_e(a),c.c(),m(c,1),c.m(e,null)):c&&(P(),u(c,1,1,()=>{c=null}),H())},i(a){l||(m(r.$$.fragment,a),m(c),l=!0)},o(a){u(r.$$.fragment,a),u(c),l=!1},d(a){a&&$(e),S(r),c&&c.d()}}}function kt(i,e,n){let t,{text:s}=e,{status:r="info"}=e;return i.$$set=o=>{"text"in o&&n(0,s=o.text),"status"in o&&n(1,r=o.status)},i.$$.update=()=>{2&i.$$.dirty&&n(2,t=function(o){switch(o){case"success":return"task-success";case"warning":return"task-warning";case"error":return"task-error";default:return"task-info"}}(r))},[s,r,t]}class xt extends O{constructor(e){super(),U(this,e,kt,At,G,{text:0,status:1})}}function Se(i,e,n){const t=i.slice();return t[20]=e[n],t[22]=n,t}function It(i){let e,n;return e=new z({props:{size:2,weight:"medium",class:"session-text",$$slots:{default:[Bt]},$$scope:{ctx:i}}}),{c(){w(e.$$.fragment)},m(t,s){_(e,t,s),n=!0},p(t,s){const r={};8388609&s&&(r.$$scope={dirty:s,ctx:t}),e.$set(r)},i(t){n||(m(e.$$.fragment,t),n=!0)},o(t){u(e.$$.fragment,t),n=!1},d(t){S(e,t)}}}function bt(i){let e,n,t,s,r,o;return t=new De({}),r=new z({props:{size:2,weight:"medium",$$slots:{default:[qt]},$$scope:{ctx:i}}}),{c(){e=k("div"),n=k("div"),w(t.$$.fragment),s=I(),w(r.$$.fragment),v(n,"class","setup-script-badge svelte-td16df"),v(e,"class","setup-script-title-container svelte-td16df")},m(l,c){p(l,e,c),q(e,n),_(t,n,null),q(e,s),_(r,e,null),o=!0},p(l,c){const a={};8388608&c&&(a.$$scope={dirty:c,ctx:l}),r.$set(a)},i(l){o||(m(t.$$.fragment,l),m(r.$$.fragment,l),o=!0)},o(l){u(t.$$.fragment,l),u(r.$$.fragment,l),o=!1},d(l){l&&$(e),S(t),S(r)}}}function Bt(i){let e,n=i[0].session_summary+"";return{c(){e=D(n)},m(t,s){p(t,e,s)},p(t,s){1&s&&n!==(n=t[0].session_summary+"")&&J(e,n)},d(t){t&&$(e)}}}function qt(i){let e;return{c(){e=k("span"),e.textContent="Generate a setup script",v(e,"class","setup-script-title svelte-td16df")},m(n,t){p(n,e,t)},p:Q,d(n){n&&$(e)}}}function ve(i){let e,n,t=[],s=new Map,r=F(i[6].slice(0,3));const o=l=>l[22];for(let l=0;l<r.length;l+=1){let c=Se(i,r,l),a=o(c);s.set(a,t[l]=Re(a,c))}return{c(){e=k("div");for(let l=0;l<t.length;l+=1)t[l].c();v(e,"class","tasks-list svelte-td16df")},m(l,c){p(l,e,c);for(let a=0;a<t.length;a+=1)t[a]&&t[a].m(e,null);n=!0},p(l,c){64&c&&(r=F(l[6].slice(0,3)),P(),t=V(t,c,o,1,l,r,s,e,X,Re,null,Se),H())},i(l){if(!n){for(let c=0;c<r.length;c+=1)m(t[c]);n=!0}},o(l){for(let c=0;c<t.length;c+=1)u(t[c]);n=!1},d(l){l&&$(e);for(let c=0;c<t.length;c+=1)t[c].d()}}}function Re(i,e){let n,t,s;return t=new xt({props:{text:e[20],status:"success"}}),{key:i,first:null,c(){n=T(),w(t.$$.fragment),this.first=n},m(r,o){p(r,n,o),_(t,r,o),s=!0},p(r,o){e=r;const l={};64&o&&(l.text=e[20]),t.$set(l)},i(r){s||(m(t.$$.fragment,r),s=!0)},o(r){u(t.$$.fragment,r),s=!1},d(r){r&&$(n),S(t,r)}}}function Pt(i){let e,n;return e=new tt({}),{c(){w(e.$$.fragment)},m(t,s){_(e,t,s),n=!0},i(t){n||(m(e.$$.fragment,t),n=!0)},o(t){u(e.$$.fragment,t),n=!1},d(t){S(e,t)}}}function Ht(i){let e,n;return e=new gt({}),{c(){w(e.$$.fragment)},m(t,s){_(e,t,s),n=!0},i(t){n||(m(e.$$.fragment,t),n=!0)},o(t){u(e.$$.fragment,t),n=!1},d(t){S(e,t)}}}function Mt(i){let e,n,t,s;const r=[Ht,Pt],o=[];function l(c,a){return c[5]?0:1}return e=l(i),n=o[e]=r[e](i),{c(){n.c(),t=T()},m(c,a){o[e].m(c,a),p(c,t,a),s=!0},p(c,a){let d=e;e=l(c),e!==d&&(P(),u(o[d],1,1,()=>{o[d]=null}),H(),n=o[e],n||(n=o[e]=r[e](c),n.c()),m(n,1),n.m(t.parentNode,t))},i(c){s||(m(n),s=!0)},o(c){u(n),s=!1},d(c){c&&$(t),o[e].d(c)}}}function Et(i){let e,n;return e=new ge({props:{variant:"ghost",color:"neutral",size:1,$$slots:{default:[Mt]},$$scope:{ctx:i}}}),e.$on("click",i[13]),{c(){w(e.$$.fragment)},m(t,s){_(e,t,s),n=!0},p(t,s){const r={};8388640&s&&(r.$$scope={dirty:s,ctx:t}),e.$set(r)},i(t){n||(m(e.$$.fragment,t),n=!0)},o(t){u(e.$$.fragment,t),n=!1},d(t){S(e,t)}}}function Ct(i){let e,n;return e=new De({}),{c(){w(e.$$.fragment)},m(t,s){_(e,t,s),n=!0},i(t){n||(m(e.$$.fragment,t),n=!0)},o(t){u(e.$$.fragment,t),n=!1},d(t){S(e,t)}}}function Ft(i){let e,n;return e=new ge({props:{disabled:!i[3],variant:"ghost",color:"neutral",size:1,title:i[3]?"SSH to agent":"SSH to agent (agent must be running or idle)",$$slots:{default:[Ct]},$$scope:{ctx:i}}}),e.$on("click",i[14]),{c(){w(e.$$.fragment)},m(t,s){_(e,t,s),n=!0},p(t,s){const r={};8&s&&(r.disabled=!t[3]),8&s&&(r.title=t[3]?"SSH to agent":"SSH to agent (agent must be running or idle)"),8388608&s&&(r.$$scope={dirty:s,ctx:t}),e.$set(r)},i(t){n||(m(e.$$.fragment,t),n=!0)},o(t){u(e.$$.fragment,t),n=!1},d(t){S(e,t)}}}function Dt(i){let e,n;return e=new et({}),{c(){w(e.$$.fragment)},m(t,s){_(e,t,s),n=!0},i(t){n||(m(e.$$.fragment,t),n=!0)},o(t){u(e.$$.fragment,t),n=!1},d(t){S(e,t)}}}function Nt(i){let e,n;return e=new ge({props:{variant:"ghost",color:"neutral",size:1,title:"Delete agent",$$slots:{default:[Dt]},$$scope:{ctx:i}}}),e.$on("click",i[15]),{c(){w(e.$$.fragment)},m(t,s){_(e,t,s),n=!0},p(t,s){const r={};8388608&s&&(r.$$scope={dirty:s,ctx:t}),e.$set(r)},i(t){n||(m(e.$$.fragment,t),n=!0)},o(t){u(e.$$.fragment,t),n=!1},d(t){S(e,t)}}}function zt(i){let e,n,t,s,r,o,l,c,a,d,g,R,x,b,M,j,y,W,E,K;const me=[bt,It],N=[];function ue(f,A){return f[0].is_setup_script_agent?0:1}t=ue(i),s=N[t]=me[t](i),c=new Ze({props:{status:i[0].status,workspaceStatus:i[0].workspace_status,isExpanded:!0}});let B=i[6].length>0&&ve(i);return x=new ee({props:{content:i[5]?"Unpin agent":"Pin agent",triggerOn:[te.Hover],side:"top",$$slots:{default:[Et]},$$scope:{ctx:i}}}),M=new ee({props:{content:"SSH to agent",triggerOn:[te.Hover],side:"top",$$slots:{default:[Ft]},$$scope:{ctx:i}}}),y=new ee({props:{content:"Delete agent",triggerOn:[te.Hover],side:"top",$$slots:{default:[Nt]},$$scope:{ctx:i}}}),E=new _t({props:{isRemote:i[4],status:i[0].status,timestamp:i[0].updated_at||i[0].started_at}}),{c(){e=k("div"),n=k("div"),s.c(),o=I(),l=k("div"),w(c.$$.fragment),a=I(),d=k("div"),B&&B.c(),g=I(),R=k("div"),w(x.$$.fragment),b=I(),w(M.$$.fragment),j=I(),w(y.$$.fragment),W=I(),w(E.$$.fragment),v(n,"class","session-summary-container svelte-td16df"),v(n,"title",r=i[0].is_setup_script_agent?"Generate a setup script":i[0].session_summary),v(l,"class","card-info"),v(e,"class","card-header svelte-td16df"),v(d,"class","card-content svelte-td16df"),v(R,"class","card-actions svelte-td16df")},m(f,A){p(f,e,A),q(e,n),N[t].m(n,null),q(e,o),q(e,l),_(c,l,null),p(f,a,A),p(f,d,A),B&&B.m(d,null),p(f,g,A),p(f,R,A),_(x,R,null),q(R,b),_(M,R,null),q(R,j),_(y,R,null),p(f,W,A),_(E,f,A),K=!0},p(f,A){let ae=t;t=ue(f),t===ae?N[t].p(f,A):(P(),u(N[ae],1,1,()=>{N[ae]=null}),H(),s=N[t],s?s.p(f,A):(s=N[t]=me[t](f),s.c()),m(s,1),s.m(n,null)),(!K||1&A&&r!==(r=f[0].is_setup_script_agent?"Generate a setup script":f[0].session_summary))&&v(n,"title",r);const oe={};1&A&&(oe.status=f[0].status),1&A&&(oe.workspaceStatus=f[0].workspace_status),c.$set(oe),f[6].length>0?B?(B.p(f,A),64&A&&m(B,1)):(B=ve(f),B.c(),m(B,1),B.m(d,null)):B&&(P(),u(B,1,1,()=>{B=null}),H());const ie={};32&A&&(ie.content=f[5]?"Unpin agent":"Pin agent"),8388641&A&&(ie.$$scope={dirty:A,ctx:f}),x.$set(ie);const $e={};8388616&A&&($e.$$scope={dirty:A,ctx:f}),M.$set($e);const pe={};8388609&A&&(pe.$$scope={dirty:A,ctx:f}),y.$set(pe);const Y={};16&A&&(Y.isRemote=f[4]),1&A&&(Y.status=f[0].status),1&A&&(Y.timestamp=f[0].updated_at||f[0].started_at),E.$set(Y)},i(f){K||(m(s),m(c.$$.fragment,f),m(B),m(x.$$.fragment,f),m(M.$$.fragment,f),m(y.$$.fragment,f),m(E.$$.fragment,f),K=!0)},o(f){u(s),u(c.$$.fragment,f),u(B),u(x.$$.fragment,f),u(M.$$.fragment,f),u(y.$$.fragment,f),u(E.$$.fragment,f),K=!1},d(f){f&&($(e),$(a),$(d),$(g),$(R),$(W)),N[t].d(),S(c),B&&B.d(),S(x),S(M),S(y),S(E,f)}}}function Tt(i){let e,n,t;return n=new rt({props:{variant:"surface",size:2,interactive:!0,class:"agent-card",$$slots:{default:[zt]},$$scope:{ctx:i}}}),n.$on("click",i[16]),n.$on("keydown",i[17]),{c(){e=k("div"),w(n.$$.fragment),v(e,"class","card-wrapper svelte-td16df"),Z(e,"selected-card",i[1]),Z(e,"setup-script-card",i[0].is_setup_script_agent)},m(s,r){p(s,e,r),_(n,e,null),t=!0},p(s,[r]){const o={};8388729&r&&(o.$$scope={dirty:r,ctx:s}),n.$set(o),(!t||2&r)&&Z(e,"selected-card",s[1]),(!t||1&r)&&Z(e,"setup-script-card",s[0].is_setup_script_agent)},i(s){t||(m(n.$$.fragment,s),t=!0)},o(s){u(n.$$.fragment,s),t=!1},d(s){s&&$(e),S(n)}}}function Wt(i,e,n){let t,s,r,o,l,c,{agent:a}=e,{selected:d=!1}=e,{onSelect:g}=e;const R=le(ne.key),x=le(de);Fe(i,x,y=>n(12,c=y));async function b(y){await R.deleteRemoteAgent(y)}async function M(y){try{r?await R.deletePinnedAgentFromStore(y):await R.savePinnedAgentToStore(y,!0);const W=await R.getPinnedAgentsFromStore();x.update(E=>{if(E)return{...E,pinnedAgents:W}})}catch(W){console.error("Failed to toggle pinned status:",W)}}function j(){o&&(async y=>{await R.sshToRemoteAgent(y.remote_agent_id)})(a)}return i.$$set=y=>{"agent"in y&&n(0,a=y.agent),"selected"in y&&n(1,d=y.selected),"onSelect"in y&&n(2,g=y.onSelect)},i.$$.update=()=>{var y;1&i.$$.dirty&&n(6,t=a.turn_summaries||[]),4096&i.$$.dirty&&n(11,s=((y=c.state)==null?void 0:y.pinnedAgents)||{}),2049&i.$$.dirty&&n(5,r=(s==null?void 0:s[a.remote_agent_id])===!0),1&i.$$.dirty&&n(3,o=a.status===C.agentRunning||a.status===C.agentIdle)},n(4,l=!0),[a,d,g,o,!0,r,t,x,b,M,j,s,c,y=>{y.stopPropagation(),M(a.remote_agent_id)},y=>{y.stopPropagation(),j()},y=>{y.stopPropagation(),b(a.remote_agent_id)},()=>g(a.remote_agent_id),y=>y.key==="Enter"&&g(a.remote_agent_id)]}class se extends O{constructor(e){super(),U(this,e,Wt,Tt,G,{agent:0,selected:1,onSelect:2})}}function Lt(i){let e;return{c(){e=D(i[0])},m(n,t){p(n,e,t)},p(n,t){1&t&&J(e,n[0])},d(n){n&&$(e)}}}function Ot(i){let e,n,t;return n=new z({props:{size:2,color:"secondary",$$slots:{default:[Lt]},$$scope:{ctx:i}}}),{c(){e=k("div"),w(n.$$.fragment),v(e,"class","section-header svelte-1tegnqi")},m(s,r){p(s,e,r),_(n,e,null),t=!0},p(s,[r]){const o={};3&r&&(o.$$scope={dirty:r,ctx:s}),n.$set(o)},i(s){t||(m(n.$$.fragment,s),t=!0)},o(s){u(n.$$.fragment,s),t=!1},d(s){s&&$(e),S(n)}}}function Ut(i,e,n){let{title:t}=e;return i.$$set=s=>{"title"in s&&n(0,t=s.title)},[t]}class re extends O{constructor(e){super(),U(this,e,Ut,Ot,G,{title:0})}}function Ae(i,e,n){const t=i.slice();return t[10]=e[n],t[12]=n,t}function ke(i,e,n){const t=i.slice();return t[10]=e[n],t[12]=n,t}function xe(i,e,n){const t=i.slice();return t[10]=e[n],t[12]=n,t}function Ie(i,e,n){const t=i.slice();return t[10]=e[n],t[12]=n,t}function Gt(i){let e,n,t,s,r,o=i[4].length>0&&be(i),l=i[3].length>0&&qe(i),c=i[2].length>0&&He(i),a=i[1].length>0&&Ee(i);return{c(){o&&o.c(),e=I(),l&&l.c(),n=I(),c&&c.c(),t=I(),a&&a.c(),s=T()},m(d,g){o&&o.m(d,g),p(d,e,g),l&&l.m(d,g),p(d,n,g),c&&c.m(d,g),p(d,t,g),a&&a.m(d,g),p(d,s,g),r=!0},p(d,g){d[4].length>0?o?(o.p(d,g),16&g&&m(o,1)):(o=be(d),o.c(),m(o,1),o.m(e.parentNode,e)):o&&(P(),u(o,1,1,()=>{o=null}),H()),d[3].length>0?l?(l.p(d,g),8&g&&m(l,1)):(l=qe(d),l.c(),m(l,1),l.m(n.parentNode,n)):l&&(P(),u(l,1,1,()=>{l=null}),H()),d[2].length>0?c?(c.p(d,g),4&g&&m(c,1)):(c=He(d),c.c(),m(c,1),c.m(t.parentNode,t)):c&&(P(),u(c,1,1,()=>{c=null}),H()),d[1].length>0?a?(a.p(d,g),2&g&&m(a,1)):(a=Ee(d),a.c(),m(a,1),a.m(s.parentNode,s)):a&&(P(),u(a,1,1,()=>{a=null}),H())},i(d){r||(m(o),m(l),m(c),m(a),r=!0)},o(d){u(o),u(l),u(c),u(a),r=!1},d(d){d&&($(e),$(n),$(t),$(s)),o&&o.d(d),l&&l.d(d),c&&c.d(d),a&&a.d(d)}}}function Jt(i){let e,n,t;return n=new z({props:{size:3,color:"secondary",$$slots:{default:[jt]},$$scope:{ctx:i}}}),{c(){e=k("div"),w(n.$$.fragment),v(e,"class","empty-state svelte-1rqt2ni")},m(s,r){p(s,e,r),_(n,e,null),t=!0},p(s,r){const o={};65536&r&&(o.$$scope={dirty:r,ctx:s}),n.$set(o)},i(s){t||(m(n.$$.fragment,s),t=!0)},o(s){u(n.$$.fragment,s),t=!1},d(s){s&&$(e),S(n)}}}function be(i){let e,n,t,s,r=[],o=new Map;e=new re({props:{title:"Pinned"}});let l=F(i[4]);const c=a=>a[10].remote_agent_id+a[12];for(let a=0;a<l.length;a+=1){let d=Ie(i,l,a),g=c(d);o.set(g,r[a]=Be(g,d))}return{c(){w(e.$$.fragment),n=I(),t=k("div");for(let a=0;a<r.length;a+=1)r[a].c();v(t,"class","agent-grid svelte-1rqt2ni")},m(a,d){_(e,a,d),p(a,n,d),p(a,t,d);for(let g=0;g<r.length;g+=1)r[g]&&r[g].m(t,null);s=!0},p(a,d){81&d&&(l=F(a[4]),P(),r=V(r,d,c,1,a,l,o,t,X,Be,null,Ie),H())},i(a){if(!s){m(e.$$.fragment,a);for(let d=0;d<l.length;d+=1)m(r[d]);s=!0}},o(a){u(e.$$.fragment,a);for(let d=0;d<r.length;d+=1)u(r[d]);s=!1},d(a){a&&($(n),$(t)),S(e,a);for(let d=0;d<r.length;d+=1)r[d].d()}}}function Be(i,e){var r;let n,t,s;return t=new se({props:{agent:e[10],selected:e[10].remote_agent_id===((r=e[0].state)==null?void 0:r.selectedAgentId),onSelect:e[6]}}),{key:i,first:null,c(){n=T(),w(t.$$.fragment),this.first=n},m(o,l){p(o,n,l),_(t,o,l),s=!0},p(o,l){var a;e=o;const c={};16&l&&(c.agent=e[10]),17&l&&(c.selected=e[10].remote_agent_id===((a=e[0].state)==null?void 0:a.selectedAgentId)),t.$set(c)},i(o){s||(m(t.$$.fragment,o),s=!0)},o(o){u(t.$$.fragment,o),s=!1},d(o){o&&$(n),S(t,o)}}}function qe(i){let e,n,t,s,r=[],o=new Map;e=new re({props:{title:"Ready to review"}});let l=F(i[3]);const c=a=>a[10].remote_agent_id+a[12];for(let a=0;a<l.length;a+=1){let d=xe(i,l,a),g=c(d);o.set(g,r[a]=Pe(g,d))}return{c(){w(e.$$.fragment),n=I(),t=k("div");for(let a=0;a<r.length;a+=1)r[a].c();v(t,"class","agent-grid svelte-1rqt2ni")},m(a,d){_(e,a,d),p(a,n,d),p(a,t,d);for(let g=0;g<r.length;g+=1)r[g]&&r[g].m(t,null);s=!0},p(a,d){73&d&&(l=F(a[3]),P(),r=V(r,d,c,1,a,l,o,t,X,Pe,null,xe),H())},i(a){if(!s){m(e.$$.fragment,a);for(let d=0;d<l.length;d+=1)m(r[d]);s=!0}},o(a){u(e.$$.fragment,a);for(let d=0;d<r.length;d+=1)u(r[d]);s=!1},d(a){a&&($(n),$(t)),S(e,a);for(let d=0;d<r.length;d+=1)r[d].d()}}}function Pe(i,e){var r;let n,t,s;return t=new se({props:{agent:e[10],selected:e[10].remote_agent_id===((r=e[0].state)==null?void 0:r.selectedAgentId),onSelect:e[6]}}),{key:i,first:null,c(){n=T(),w(t.$$.fragment),this.first=n},m(o,l){p(o,n,l),_(t,o,l),s=!0},p(o,l){var a;e=o;const c={};8&l&&(c.agent=e[10]),9&l&&(c.selected=e[10].remote_agent_id===((a=e[0].state)==null?void 0:a.selectedAgentId)),t.$set(c)},i(o){s||(m(t.$$.fragment,o),s=!0)},o(o){u(t.$$.fragment,o),s=!1},d(o){o&&$(n),S(t,o)}}}function He(i){let e,n,t,s,r=[],o=new Map;e=new re({props:{title:"Running agents"}});let l=F(i[2]);const c=a=>a[10].remote_agent_id+a[12];for(let a=0;a<l.length;a+=1){let d=ke(i,l,a),g=c(d);o.set(g,r[a]=Me(g,d))}return{c(){w(e.$$.fragment),n=I(),t=k("div");for(let a=0;a<r.length;a+=1)r[a].c();v(t,"class","agent-grid svelte-1rqt2ni")},m(a,d){_(e,a,d),p(a,n,d),p(a,t,d);for(let g=0;g<r.length;g+=1)r[g]&&r[g].m(t,null);s=!0},p(a,d){69&d&&(l=F(a[2]),P(),r=V(r,d,c,1,a,l,o,t,X,Me,null,ke),H())},i(a){if(!s){m(e.$$.fragment,a);for(let d=0;d<l.length;d+=1)m(r[d]);s=!0}},o(a){u(e.$$.fragment,a);for(let d=0;d<r.length;d+=1)u(r[d]);s=!1},d(a){a&&($(n),$(t)),S(e,a);for(let d=0;d<r.length;d+=1)r[d].d()}}}function Me(i,e){var r;let n,t,s;return t=new se({props:{agent:e[10],selected:e[10].remote_agent_id===((r=e[0].state)==null?void 0:r.selectedAgentId),onSelect:e[6]}}),{key:i,first:null,c(){n=T(),w(t.$$.fragment),this.first=n},m(o,l){p(o,n,l),_(t,o,l),s=!0},p(o,l){var a;e=o;const c={};4&l&&(c.agent=e[10]),5&l&&(c.selected=e[10].remote_agent_id===((a=e[0].state)==null?void 0:a.selectedAgentId)),t.$set(c)},i(o){s||(m(t.$$.fragment,o),s=!0)},o(o){u(t.$$.fragment,o),s=!1},d(o){o&&$(n),S(t,o)}}}function Ee(i){let e,n,t,s,r=[],o=new Map;e=new re({props:{title:"Failed agents"}});let l=F(i[1]);const c=a=>a[10].remote_agent_id+a[12];for(let a=0;a<l.length;a+=1){let d=Ae(i,l,a),g=c(d);o.set(g,r[a]=Ce(g,d))}return{c(){w(e.$$.fragment),n=I(),t=k("div");for(let a=0;a<r.length;a+=1)r[a].c();v(t,"class","agent-grid svelte-1rqt2ni")},m(a,d){_(e,a,d),p(a,n,d),p(a,t,d);for(let g=0;g<r.length;g+=1)r[g]&&r[g].m(t,null);s=!0},p(a,d){67&d&&(l=F(a[1]),P(),r=V(r,d,c,1,a,l,o,t,X,Ce,null,Ae),H())},i(a){if(!s){m(e.$$.fragment,a);for(let d=0;d<l.length;d+=1)m(r[d]);s=!0}},o(a){u(e.$$.fragment,a);for(let d=0;d<r.length;d+=1)u(r[d]);s=!1},d(a){a&&($(n),$(t)),S(e,a);for(let d=0;d<r.length;d+=1)r[d].d()}}}function Ce(i,e){var r;let n,t,s;return t=new se({props:{agent:e[10],selected:e[10].remote_agent_id===((r=e[0].state)==null?void 0:r.selectedAgentId),onSelect:e[6]}}),{key:i,first:null,c(){n=T(),w(t.$$.fragment),this.first=n},m(o,l){p(o,n,l),_(t,o,l),s=!0},p(o,l){var a;e=o;const c={};2&l&&(c.agent=e[10]),3&l&&(c.selected=e[10].remote_agent_id===((a=e[0].state)==null?void 0:a.selectedAgentId)),t.$set(c)},i(o){s||(m(t.$$.fragment,o),s=!0)},o(o){u(t.$$.fragment,o),s=!1},d(o){o&&$(n),S(t,o)}}}function jt(i){let e;return{c(){e=D("No agents available")},m(n,t){p(n,e,t)},d(n){n&&$(e)}}}function Kt(i){let e,n,t,s;const r=[Jt,Gt],o=[];function l(c,a){var d;return((d=c[0].state)==null?void 0:d.agentOverviews.length)===0?0:1}return n=l(i),t=o[n]=r[n](i),{c(){e=k("div"),t.c(),v(e,"class","agent-list svelte-1rqt2ni")},m(c,a){p(c,e,a),o[n].m(e,null),s=!0},p(c,[a]){let d=n;n=l(c),n===d?o[n].p(c,a):(P(),u(o[d],1,1,()=>{o[d]=null}),H(),t=o[n],t?t.p(c,a):(t=o[n]=r[n](c),t.c()),m(t,1),t.m(e,null))},i(c){s||(m(t),s=!0)},o(c){u(t),s=!1},d(c){c&&$(e),o[n].d()}}}function Qt(i,e,n){let t,s,r,o,l,c,a,d;const g=le(de);return Fe(i,g,R=>n(0,d=R)),i.$$.update=()=>{var R,x;1&i.$$.dirty&&n(9,t=Ve(((R=d.state)==null?void 0:R.agentOverviews)||[])),1&i.$$.dirty&&n(8,s=((x=d.state)==null?void 0:x.pinnedAgents)||{}),768&i.$$.dirty&&n(4,r=t.filter(b=>(s==null?void 0:s[b.remote_agent_id])===!0)),768&i.$$.dirty&&n(7,o=t.filter(b=>(s==null?void 0:s[b.remote_agent_id])!==!0)),128&i.$$.dirty&&n(3,l=o.filter(b=>b.status===C.agentIdle)),128&i.$$.dirty&&n(2,c=o.filter(b=>b.status===C.agentRunning||b.status===C.agentStarting)),128&i.$$.dirty&&n(1,a=o.filter(b=>b.status!==C.agentRunning&&b.status!==C.agentStarting&&b.status!==C.agentIdle))},[d,a,c,l,r,g,function(R){g.update(x=>{if(x)return{...x,selectedAgentId:R}})},o,s,t]}class Vt extends O{constructor(e){super(),U(this,e,Qt,Kt,G,{})}}function Xt(i){let e,n,t,s,r,o,l,c,a,d;return s=new it({}),l=new Vt({}),{c(){e=k("div"),n=k("h1"),t=k("span"),w(s.$$.fragment),r=D(`
    Remote Agents`),o=I(),w(l.$$.fragment),v(t,"class","l-main__title-logo svelte-1941nw6"),v(n,"class","l-main__title svelte-1941nw6"),v(e,"class","l-main svelte-1941nw6")},m(g,R){p(g,e,R),q(e,n),q(n,t),_(s,t,null),q(n,r),q(e,o),_(l,e,null),c=!0,a||(d=Ke(window,"message",i[0].onMessageFromExtension),a=!0)},p:Q,i(g){c||(m(s.$$.fragment,g),m(l.$$.fragment,g),c=!0)},o(g){u(s.$$.fragment,g),u(l.$$.fragment,g),c=!1},d(g){g&&$(e),S(s),S(l),a=!1,d()}}}function Yt(i){const e=new Ye(Xe),n=new ct(e,void 0,nt,st);e.registerConsumer(n),ye(de,n);const t=new ne(e);return ye(ne.key,t),Qe(()=>(n.fetchStateFromExtension().then(()=>{n.update(s=>{if(!s)return;const r=[...s.activeWebviews,"home"];return s.pinnedAgents?{...s,activeWebviews:r}:{...s,activeWebviews:r,pinnedAgents:{}}})}),()=>{e.dispose(),t.dispose()})),[e]}new class extends O{constructor(i){super(),U(this,i,Yt,Xt,G,{})}}({target:document.getElementById("app")});
