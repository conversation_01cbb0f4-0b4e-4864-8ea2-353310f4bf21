var de=Object.defineProperty;var le=(a,e,t)=>e in a?de(a,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):a[e]=t;var o=(a,e,t)=>le(a,typeof e!="symbol"?e+"":e,t);import{b as G,A as D,a as ge,R as M}from"./types-LfaCSdmF.js";import{W as d}from"./BaseButton-rKFNr-KO.js";import{a as U,C as te,P as k}from"./chat-types-NgqNgjwU.js";import{c as he,f as ue,A as pe,a as me,p as fe,T as P,W as _e,b as S,d as j,g as ye,r as V,s as ve,e as z,S as Se,h as Ae}from"./test_service_pb-B6vKXZrG.js";import{n as we}from"./file-paths-BcSg4gks.js";import{S as Ce,b as Re,c as be}from"./types-DvVg976p.js";import{ai as J,ae as Ie,ap as Me,S as se,i as ne,s as ae,a as H,b as B,I as xe,J as Ee,K as Pe,L as Fe,h as N,d as X,M as qe,g as ke,n as T,j as K,c as w,e as Te,f as Le}from"./SpinnerAugment-BRymMBwV.js";var I;function Z(a){const e=I[a];return typeof e!="string"?a.toString():e[0].toLowerCase()+e.substring(1).replace(/[A-Z]/g,t=>"_"+t.toLowerCase())}(function(a){a[a.Canceled=1]="Canceled",a[a.Unknown=2]="Unknown",a[a.InvalidArgument=3]="InvalidArgument",a[a.DeadlineExceeded=4]="DeadlineExceeded",a[a.NotFound=5]="NotFound",a[a.AlreadyExists=6]="AlreadyExists",a[a.PermissionDenied=7]="PermissionDenied",a[a.ResourceExhausted=8]="ResourceExhausted",a[a.FailedPrecondition=9]="FailedPrecondition",a[a.Aborted=10]="Aborted",a[a.OutOfRange=11]="OutOfRange",a[a.Unimplemented=12]="Unimplemented",a[a.Internal=13]="Internal",a[a.Unavailable=14]="Unavailable",a[a.DataLoss=15]="DataLoss",a[a.Unauthenticated=16]="Unauthenticated"})(I||(I={}));class b extends Error{constructor(e,t=I.Unknown,s,n,r){super(function(i,c){return i.length?`[${Z(c)}] ${i}`:`[${Z(c)}]`}(e,t)),this.name="ConnectError",Object.setPrototypeOf(this,new.target.prototype),this.rawMessage=e,this.code=t,this.metadata=new Headers(s??{}),this.details=n??[],this.cause=r}static from(e,t=I.Unknown){return e instanceof b?e:e instanceof Error?e.name=="AbortError"?new b(e.message,I.Canceled):new b(e.message,t,void 0,void 0,e):new b(String(e),t,void 0,void 0,e)}static[Symbol.hasInstance](e){return e instanceof Error&&(Object.getPrototypeOf(e)===b.prototype||e.name==="ConnectError"&&"code"in e&&typeof e.code=="number"&&"metadata"in e&&"details"in e&&Array.isArray(e.details)&&"rawMessage"in e&&typeof e.rawMessage=="string"&&"cause"in e)}findDetails(e){const t=e.kind==="message"?{getMessage:n=>n===e.typeName?e:void 0}:e,s=[];for(const n of this.details){if("desc"in n){t.getMessage(n.desc.typeName)&&s.push(he(n.desc,n.value));continue}const r=t.getMessage(n.type);if(r)try{s.push(ue(r,n.value))}catch{}}return s}}var De=function(a){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var e,t=a[Symbol.asyncIterator];return t?t.call(a):(a=typeof __values=="function"?__values(a):a[Symbol.iterator](),e={},s("next"),s("throw"),s("return"),e[Symbol.asyncIterator]=function(){return this},e);function s(n){e[n]=a[n]&&function(r){return new Promise(function(i,c){(function(g,h,u,l){Promise.resolve(l).then(function(p){g({value:p,done:u})},h)})(i,c,(r=a[n](r)).done,r.value)})}}},q=function(a){return this instanceof q?(this.v=a,this):new q(a)},Oe=function(a,e,t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var s,n=t.apply(a,e||[]),r=[];return s=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),i("next"),i("throw"),i("return",function(l){return function(p){return Promise.resolve(p).then(l,h)}}),s[Symbol.asyncIterator]=function(){return this},s;function i(l,p){n[l]&&(s[l]=function(m){return new Promise(function(y,C){r.push([l,m,y,C])>1||c(l,m)})},p&&(s[l]=p(s[l])))}function c(l,p){try{(m=n[l](p)).value instanceof q?Promise.resolve(m.value.v).then(g,h):u(r[0][2],m)}catch(y){u(r[0][3],y)}var m}function g(l){c("next",l)}function h(l){c("throw",l)}function u(l,p){l(p),r.shift(),r.length&&c(r[0][0],r[0][1])}},Ue=function(a){var e,t;return e={},s("next"),s("throw",function(n){throw n}),s("return"),e[Symbol.iterator]=function(){return this},e;function s(n,r){e[n]=a[n]?function(i){return(t=!t)?{value:q(a[n](i)),done:!1}:r?r(i):i}:r}},re=function(a){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var e,t=a[Symbol.asyncIterator];return t?t.call(a):(a=typeof __values=="function"?__values(a):a[Symbol.iterator](),e={},s("next"),s("throw"),s("return"),e[Symbol.asyncIterator]=function(){return this},e);function s(n){e[n]=a[n]&&function(r){return new Promise(function(i,c){(function(g,h,u,l){Promise.resolve(l).then(function(p){g({value:p,done:u})},h)})(i,c,(r=a[n](r)).done,r.value)})}}},x=function(a){return this instanceof x?(this.v=a,this):new x(a)},He=function(a){var e,t;return e={},s("next"),s("throw",function(n){throw n}),s("return"),e[Symbol.iterator]=function(){return this},e;function s(n,r){e[n]=a[n]?function(i){return(t=!t)?{value:x(a[n](i)),done:!1}:r?r(i):i}:r}},Be=function(a,e,t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var s,n=t.apply(a,e||[]),r=[];return s=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),i("next"),i("throw"),i("return",function(l){return function(p){return Promise.resolve(p).then(l,h)}}),s[Symbol.asyncIterator]=function(){return this},s;function i(l,p){n[l]&&(s[l]=function(m){return new Promise(function(y,C){r.push([l,m,y,C])>1||c(l,m)})},p&&(s[l]=p(s[l])))}function c(l,p){try{(m=n[l](p)).value instanceof x?Promise.resolve(m.value.v).then(g,h):u(r[0][2],m)}catch(y){u(r[0][3],y)}var m}function g(l){c("next",l)}function h(l){c("throw",l)}function u(l,p){l(p),r.shift(),r.length&&c(r[0][0],r[0][1])}};function Ne(a,e){return function(t,s){const n={};for(const r of t.methods){const i=s(r);i!=null&&(n[r.localName]=i)}return n}(a,t=>{switch(t.methodKind){case"unary":return function(s,n){return async function(r,i){var c,g;const h=await s.unary(n,i==null?void 0:i.signal,i==null?void 0:i.timeoutMs,i==null?void 0:i.headers,r,i==null?void 0:i.contextValues);return(c=i==null?void 0:i.onHeader)===null||c===void 0||c.call(i,h.header),(g=i==null?void 0:i.onTrailer)===null||g===void 0||g.call(i,h.trailer),h.message}}(e,t);case"server_streaming":return function(s,n){return function(r,i){return Q(s.stream(n,i==null?void 0:i.signal,i==null?void 0:i.timeoutMs,i==null?void 0:i.headers,function(c){return Oe(this,arguments,function*(){yield q(yield*Ue(De(c)))})}([r]),i==null?void 0:i.contextValues),i)}}(e,t);case"client_streaming":return function(s,n){return async function(r,i){var c,g,h,u,l,p;const m=await s.stream(n,i==null?void 0:i.signal,i==null?void 0:i.timeoutMs,i==null?void 0:i.headers,r,i==null?void 0:i.contextValues);let y;(l=i==null?void 0:i.onHeader)===null||l===void 0||l.call(i,m.header);let C=0;try{for(var f,E=!0,L=re(m.message);!(c=(f=await L.next()).done);E=!0)u=f.value,E=!1,y=u,C++}catch(ce){g={error:ce}}finally{try{E||c||!(h=L.return)||await h.call(L)}finally{if(g)throw g.error}}if(!y)throw new b("protocol error: missing response message",I.Unimplemented);if(C>1)throw new b("protocol error: received extra messages for client streaming method",I.Unimplemented);return(p=i==null?void 0:i.onTrailer)===null||p===void 0||p.call(i,m.trailer),y}}(e,t);case"bidi_streaming":return function(s,n){return function(r,i){return Q(s.stream(n,i==null?void 0:i.signal,i==null?void 0:i.timeoutMs,i==null?void 0:i.headers,r,i==null?void 0:i.contextValues),i)}}(e,t);default:return null}})}function Q(a,e){const t=function(){return Be(this,arguments,function*(){var s,n;const r=yield x(a);(s=e==null?void 0:e.onHeader)===null||s===void 0||s.call(e,r.header),yield x(yield*He(re(r.message))),(n=e==null?void 0:e.onTrailer)===null||n===void 0||n.call(e,r.trailer)})}()[Symbol.asyncIterator]();return{[Symbol.asyncIterator]:()=>({next:()=>t.next()})}}const At="augment-welcome";var _=(a=>(a.draft="draft",a.sent="sent",a.failed="failed",a.success="success",a.cancelled="cancelled",a))(_||{}),$e=(a=>(a.running="running",a.awaitingUserAction="awaiting-user-action",a.notRunning="not-running",a))($e||{}),A=(a=>(a.seen="seen",a.unseen="unseen",a))(A||{}),We=(a=>(a.signInWelcome="sign-in-welcome",a.generateCommitMessage="generate-commit-message",a.summaryResponse="summary-response",a.summaryTitle="summary-title",a.educateFeatures="educate-features",a.autofixMessage="autofix-message",a.autofixSteeringMessage="autofix-steering-message",a.autofixStage="autofix-stage",a.agentOnboarding="agent-onboarding",a.agenticTurnDelimiter="agentic-turn-delimiter",a.agenticRevertDelimiter="agentic-revert-delimiter",a.agenticCheckpointDelimiter="agentic-checkpoint-delimiter",a.exchange="exchange",a))(We||{});function Ge(a){return!!a&&(a.chatItemType===void 0||a.chatItemType==="agent-onboarding")}function wt(a){return Ge(a)&&a.status==="success"}function Ct(a){return a.chatItemType==="autofix-message"}function Rt(a){return a.chatItemType==="autofix-steering-message"}function bt(a){return a.chatItemType==="autofix-stage"}function It(a){return a.chatItemType==="sign-in-welcome"}function Mt(a){return a.chatItemType==="generate-commit-message"}function xt(a){return a.chatItemType==="summary-response"}function Et(a){return a.chatItemType==="educate-features"}function Pt(a){return a.chatItemType==="agent-onboarding"}function Ft(a){return a.chatItemType==="agentic-turn-delimiter"}function qt(a){return a.chatItemType==="agentic-checkpoint-delimiter"}function kt(a){return a.revertTarget!==void 0}function Tt(a){var e;return((e=a.structured_output_nodes)==null?void 0:e.some(t=>t.type===te.TOOL_USE))??!1}function Lt(a){var e;return((e=a.structured_request_nodes)==null?void 0:e.some(t=>t.type===U.TOOL_RESULT))??!1}function Dt(a){return!(!a||typeof a!="object")&&(!("request_id"in a)||typeof a.request_id=="string")&&(!("seen_state"in a)||a.seen_state==="seen"||a.seen_state==="unseen")}async function*je(a,e=1e3){for(;a>0;)yield a,await new Promise(t=>setTimeout(t,Math.min(e,a))),a-=e}class Ve{constructor(e,t,s,n=5,r=4e3,i){o(this,"_isCancelled",!1);this.requestId=e,this.chatMessage=t,this.startStreamFn=s,this.maxRetries=n,this.baseDelay=r,this.flags=i}cancel(){this._isCancelled=!0}async*getStream(){let e=0,t=!1;try{for(;!this._isCancelled;){const s=this.startStreamFn({...this.chatMessage,createdTimestamp:Date.now()},this.flags?{flags:this.flags}:void 0);let n,r=!1,i="";for await(const g of s){if(g.status===_.failed){if(g.isRetriable!==!0||t)return yield g;r=!0,i=g.display_error_message||"Service is currently unavailable",n=g.request_id;break}t=!0,yield g}if(!r)return;if(this._isCancelled)return yield this.createCancelledStatus();if(e++,e>this.maxRetries)return void(yield{request_id:n??this.requestId,seen_state:A.unseen,status:_.failed,display_error_message:i,isRetriable:!1});const c=this.baseDelay*2**(e-1);for await(const g of je(c))yield{request_id:this.requestId,status:_.sent,display_error_message:`Service temporarily unavailable. Retrying in ${Math.floor(g/1e3)} seconds... (Attempt ${e} of ${this.maxRetries})`,isRetriable:!0};yield{request_id:this.requestId,status:_.sent,display_error_message:"Generating response...",isRetriable:!0}}this._isCancelled&&(yield this.createCancelledStatus())}catch(s){yield{request_id:this.requestId,seen_state:A.unseen,status:_.failed,display_error_message:s instanceof Error?s.message:String(s)}}}createCancelledStatus(){return{request_id:this.requestId,seen_state:A.unseen,status:_.cancelled}}}function v(a,e){return e in a&&a[e]!==void 0}function ze(a){return v(a,"file")}function Je(a){return v(a,"recentFile")}function Xe(a){return v(a,"folder")}function Ke(a){return v(a,"sourceFolder")}function Ot(a){return v(a,"sourceFolderGroup")}function Ut(a){return v(a,"selection")}function Ze(a){return v(a,"externalSource")}function Ht(a){return v(a,"allDefaultContext")}function Bt(a){return v(a,"clearContext")}function Nt(a){return v(a,"userGuidelines")}function $t(a){return v(a,"agentMemories")}function Qe(a){return v(a,"personality")}function Ye(a){return v(a,"rule")}const Wt={allDefaultContext:!0,label:"Default Context",id:"allDefaultContext"},Gt={clearContext:!0,label:"Clear Context",id:"clearContext"},jt={userGuidelines:{enabled:!1,overLimit:!1,contents:"",lengthLimit:2e3},label:"User Guidelines",id:"userGuidelines"},Vt={agentMemories:{},label:"Agent Memories",id:"agentMemories"},Y=[{personality:{type:k.DEFAULT,description:"Expert software engineer - trusted coding agent, at your service!"},label:"Agent Auggie",name:"auggie-personality-agent-default",id:"auggie-personality-agent-default"},{personality:{type:k.PROTOTYPER,description:"Fast and loose - let's get it done, boss!"},label:"Prototyper Auggie",name:"auggie-personality-prototyper",id:"auggie-personality-prototyper"},{personality:{type:k.BRAINSTORM,description:"Thoughtful and creative - thinking through all possibilities..."},label:"Brainstorm Auggie",name:"auggie-personality-brainstorm",id:"auggie-personality-brainstorm"},{personality:{type:k.REVIEWER,description:"Code detective - finding issues and analyzing implications"},label:"Reviewer Auggie",name:"auggie-personality-reviewer",id:"auggie-personality-reviewer"}];function zt(a){return v(a,"group")}function Jt(a){const e=new Map;return a.forEach(t=>{ze(t)?e.set("file",[...e.get("file")??[],t]):Je(t)?e.set("recentFile",[...e.get("recentFile")??[],t]):Xe(t)?e.set("folder",[...e.get("folder")??[],t]):Ze(t)?e.set("externalSource",[...e.get("externalSource")??[],t]):Ke(t)?e.set("sourceFolder",[...e.get("sourceFolder")??[],t]):Qe(t)?e.set("personality",[...e.get("personality")??[],t]):Ye(t)&&e.set("rule",[...e.get("rule")??[],t])}),[{label:"Personalities",id:"personalities",group:{type:"personality",materialIcon:"person",items:e.get("personality")??[]}},{label:"Files",id:"files",group:{type:"file",materialIcon:"insert_drive_file",items:e.get("file")??[]}},{label:"Folders",id:"folders",group:{type:"folder",materialIcon:"folder",items:e.get("folder")??[]}},{label:"Source Folders",id:"sourceFolders",group:{type:"sourceFolder",materialIcon:"folder_managed",items:e.get("sourceFolder")??[]}},{label:"Recently Opened Files",id:"recentlyOpenedFiles",group:{type:"recentFile",materialIcon:"insert_drive_file",items:e.get("recentFile")??[]}},{label:"Documentation",id:"externalSources",group:{type:"externalSource",materialIcon:"link",items:e.get("externalSource")??[]}},{label:"Rules",id:"rules",group:{type:"rule",materialIcon:"rule",items:e.get("rule")??[]}}].filter(t=>t.group.items.length>0)}function et(a){const e={label:we(a.pathName).split("/").filter(t=>t.trim()!=="").pop()||"",name:a.pathName,id:fe({rootPath:a.repoRoot,relPath:a.pathName})};if(a.fullRange){const t=`:L${a.fullRange.startLineNumber}-${a.fullRange.endLineNumber}`;e.label+=t,e.name+=t,e.id+=t}else if(a.range){const t=`:L${a.range.start}-${a.range.stop}`;e.label+=t,e.name+=t,e.id+=t}return e}function tt(a){const e=a.path.split("/"),t=e[e.length-1],s=t.endsWith(".md")?t.slice(0,-3):t,n=`${pe}/${me}/${a.path}`;return{label:s,name:n,id:n}}class st{constructor(e){o(this,"getHydratedTask",async e=>{const t={type:P.getHydratedTaskRequest,data:{uuid:e}};return(await this._asyncMsgSender.sendToSidecar(t,3e4)).data.task});o(this,"createTask",async(e,t,s)=>{const n={type:P.createTaskRequest,data:{name:e,description:t,parentTaskUuid:s}};return(await this._asyncMsgSender.sendToSidecar(n,3e4)).data.uuid});o(this,"updateTask",async(e,t,s)=>{const n={type:P.updateTaskRequest,data:{uuid:e,updates:t,updatedBy:s}};await this._asyncMsgSender.sendToSidecar(n,3e4)});o(this,"setCurrentRootTaskUuid",e=>{const t={type:P.setCurrentRootTaskUuid,data:{uuid:e}};this._asyncMsgSender.sendToSidecar(t)});o(this,"updateHydratedTask",async(e,t)=>{const s={type:P.updateHydratedTaskRequest,data:{task:e,updatedBy:t}};return(await this._asyncMsgSender.sendToSidecar(s,3e4)).data});this._asyncMsgSender=e}}class nt{constructor(e,t,s){o(this,"_taskClient");o(this,"getChatInitData",async()=>{const e=await this._asyncMsgSender.send({type:d.chatLoaded},3e4);if(e.data.enableDebugFeatures)try{console.log("Running hello world test...");const t=await async function(s){return(await Ne(Ae,new Se({sendMessage:r=>{s.postMessage(r)},onReceiveMessage:r=>{const i=c=>{r(c.data)};return window.addEventListener("message",i),()=>{window.removeEventListener("message",i)}}})).testMethod({foo:"bar"},{timeoutMs:1e3})).result}(this._host);console.log("Hello world result:",t)}catch(t){console.error("Hello world error:",t)}return e.data});o(this,"reportWebviewClientEvent",e=>{this._asyncMsgSender.send({type:d.reportWebviewClientMetric,data:{webviewName:_e.chat,client_metric:e,value:1}})});o(this,"reportAgentSessionEvent",e=>{this._asyncMsgSender.sendToSidecar({type:S.reportAgentSessionEvent,data:e})});o(this,"reportAgentRequestEvent",e=>{this._asyncMsgSender.sendToSidecar({type:S.reportAgentRequestEvent,data:e})});o(this,"getSuggestions",async(e,t=!1)=>{const s={rootPath:"",relPath:e},n=this.findFiles(s,6),r=this.findRecentlyOpenedFiles(s,6),i=this.findFolders(s,3),c=this.findExternalSources(e,t),g=this.findRules(e,6),[h,u,l,p,m]=await Promise.all([F(n,[]),F(r,[]),F(i,[]),F(c,[]),F(g,[])]),y=(f,E)=>({...et(f),[E]:f}),C=[...h.map(f=>y(f,"file")),...l.map(f=>y(f,"folder")),...u.map(f=>y(f,"recentFile")),...p.map(f=>({label:f.name,name:f.name,id:f.id,externalSource:f})),...m.map(f=>({...tt(f),rule:f}))];if(this._flags.enablePersonalities){const f=this.getPersonalities(e);f.length>0&&C.push(...f)}return C});o(this,"getPersonalities",e=>{if(!this._flags.enablePersonalities)return[];if(e==="")return Y;const t=e.toLowerCase();return Y.filter(s=>{const n=s.personality.description.toLowerCase(),r=s.label.toLowerCase();return n.includes(t)||r.includes(t)})});o(this,"sendAction",e=>{this._host.postMessage({type:d.mainPanelPerformAction,data:e})});o(this,"showAugmentPanel",()=>{this._asyncMsgSender.send({type:d.showAugmentPanel})});o(this,"openConfirmationModal",async e=>(await this._asyncMsgSender.send({type:d.openConfirmationModal,data:e},1e9)).data.ok);o(this,"clearMetadataFor",e=>{this._host.postMessage({type:d.chatClearMetadata,data:e})});o(this,"resolvePath",async(e,t=void 0)=>{const s=await this._asyncMsgSender.send({type:d.resolveFileRequest,data:{...e,exactMatch:!0,maxResults:1,searchScope:t}},5e3);if(s.data)return s.data});o(this,"resolveSymbols",async(e,t)=>(await this._asyncMsgSender.send({type:d.findSymbolRequest,data:{query:e,searchScope:t}},3e4)).data);o(this,"getDiagnostics",async()=>(await this._asyncMsgSender.send({type:d.getDiagnosticsRequest},1e3)).data);o(this,"findFiles",async(e,t=12)=>(await this._asyncMsgSender.send({type:d.findFileRequest,data:{...e,maxResults:t}},5e3)).data);o(this,"findFolders",async(e,t=12)=>(await this._asyncMsgSender.send({type:d.findFolderRequest,data:{...e,maxResults:t}},5e3)).data);o(this,"findRecentlyOpenedFiles",async(e,t=12)=>(await this._asyncMsgSender.send({type:d.findRecentlyOpenedFilesRequest,data:{...e,maxResults:t}},5e3)).data);o(this,"findExternalSources",async(e,t=!1)=>this._flags.enableExternalSourcesInChat?t?[]:(await this._asyncMsgSender.send({type:d.findExternalSourcesRequest,data:{query:e,source_types:[]}},5e3)).data.sources??[]:[]);o(this,"findRules",async(e,t=12)=>this._flags.enableRules?(await this._asyncMsgSender.send({type:d.getRulesListRequest,data:{query:e,maxResults:t}},5e3)).data:[]);o(this,"openFile",e=>{this._host.postMessage({type:d.openFile,data:e})});o(this,"saveFile",e=>this._host.postMessage({type:d.saveFile,data:e}));o(this,"loadFile",e=>this._host.postMessage({type:d.loadFile,data:e}));o(this,"openMemoriesFile",()=>{this._host.postMessage({type:d.openMemoriesFile})});o(this,"createFile",(e,t)=>{this._host.postMessage({type:d.chatCreateFile,data:{code:e,relPath:t}})});o(this,"openScratchFile",async(e,t="shellscript")=>{await this._asyncMsgSender.send({type:d.openScratchFileRequest,data:{content:e,language:t}},1e4)});o(this,"resolveWorkspaceFileChunk",async e=>{try{return(await this._asyncMsgSender.send({type:d.resolveWorkspaceFileChunkRequest,data:e},5e3)).data}catch{return}});o(this,"smartPaste",e=>{this._host.postMessage({type:d.chatSmartPaste,data:e})});o(this,"getHydratedTask",async e=>this._taskClient.getHydratedTask(e));o(this,"updateHydratedTask",async(e,t)=>this._taskClient.updateHydratedTask(e,t));o(this,"setCurrentRootTaskUuid",e=>{this._taskClient.setCurrentRootTaskUuid(e)});o(this,"createTask",async(e,t,s)=>this._taskClient.createTask(e,t,s));o(this,"updateTask",async(e,t,s)=>this._taskClient.updateTask(e,t,s));o(this,"saveChat",async(e,t,s)=>this._asyncMsgSender.send({type:d.saveChat,data:{conversationId:e,chatHistory:t,title:s}}));o(this,"launchAutofixPanel",async(e,t,s)=>this._asyncMsgSender.send({type:d.chatLaunchAutofixPanel,data:{conversationId:e,iterationId:t,stage:s}}));o(this,"updateUserGuidelines",e=>{this._host.postMessage({type:d.updateUserGuidelines,data:e})});o(this,"updateWorkspaceGuidelines",e=>{this._host.postMessage({type:d.updateWorkspaceGuidelines,data:e})});o(this,"updateRuleFile",(e,t)=>{this._host.postMessage({type:d.updateRuleFile,data:{rulePath:e,content:t}})});o(this,"openSettingsPage",e=>{this._host.postMessage({type:d.openSettingsPage,data:e})});o(this,"_activeRetryStreams",new Map);o(this,"cancelChatStream",async e=>{var t;(t=this._activeRetryStreams.get(e))==null||t.cancel(),await this._asyncMsgSender.send({type:d.chatUserCancel,data:{requestId:e}},1e4)});o(this,"sendUserRating",async(e,t,s,n="")=>{const r={requestId:e,rating:s,note:n,mode:t},i={type:d.chatRating,data:r};return(await this._asyncMsgSender.send(i,3e4)).data});o(this,"triggerUsedChatMetric",()=>{this._host.postMessage({type:d.usedChat})});o(this,"createProject",e=>{this._host.postMessage({type:d.mainPanelCreateProject,data:{name:e}})});o(this,"openProjectFolder",()=>{this._host.postMessage({type:d.mainPanelPerformAction,data:"open-folder"})});o(this,"closeProjectFolder",()=>{this._host.postMessage({type:d.mainPanelPerformAction,data:"close-folder"})});o(this,"cloneRepository",()=>{this._host.postMessage({type:d.mainPanelPerformAction,data:"clone-repository"})});o(this,"grantSyncPermission",()=>{this._host.postMessage({type:d.mainPanelPerformAction,data:"grant-sync-permission"})});o(this,"callTool",async(e,t,s,n,r,i)=>{const c={type:d.callTool,data:{chatRequestId:e,toolUseId:t,name:s,input:n,chatHistory:r,conversationId:i}};return(await this._asyncMsgSender.send(c,0)).data});o(this,"cancelToolRun",async(e,t)=>{const s={type:d.cancelToolRun,data:{requestId:e,toolUseId:t}};await this._asyncMsgSender.send(s,0)});o(this,"checkSafe",async(e,t)=>{const s={type:d.toolCheckSafe,data:{name:e,input:t}};return(await this._asyncMsgSender.send(s,0)).data.isSafe});o(this,"closeAllToolProcesses",async()=>{await this._asyncMsgSender.sendToSidecar({type:j.closeAllToolProcesses},0)});o(this,"getToolIdentifier",async e=>{const t={type:j.getToolIdentifierRequest,data:{toolName:e}};return(await this._asyncMsgSender.sendToSidecar(t,0)).data});o(this,"executeCommand",async(e,t,s)=>{try{const n=await this._asyncMsgSender.send({type:d.chatAutofixExecuteCommandRequest,data:{iterationId:e,command:t,args:s}},6e5);return{output:n.data.output,returnCode:n.data.returnCode}}catch(n){throw console.error("[ExtensionClient] Execute command failed:",n),n}});o(this,"sendAutofixStateUpdate",async e=>{await this._asyncMsgSender.send({type:d.chatAutofixStateUpdate,data:e})});o(this,"autofixPlan",async(e,t)=>(await this._asyncMsgSender.send({type:d.chatAutofixPlanRequest,data:{command:e,steeringHistory:t}},6e4)).data.plan);o(this,"setChatMode",e=>{this._asyncMsgSender.send({type:d.chatModeChanged,data:{mode:e}})});o(this,"getAgentEditList",async(e,t)=>{const s={type:S.getEditListRequest,data:{fromTimestamp:e,toTimestamp:t}};return(await this._asyncMsgSender.sendToSidecar(s,3e4)).data});o(this,"hasChangesSince",async e=>{const t={type:S.getEditListRequest,data:{fromTimestamp:e,toTimestamp:Number.MAX_SAFE_INTEGER}};return(await this._asyncMsgSender.sendToSidecar(t,3e4)).data.edits.filter(s=>{var n,r;return((n=s.changesSummary)==null?void 0:n.totalAddedLines)||((r=s.changesSummary)==null?void 0:r.totalRemovedLines)}).length>0});o(this,"getToolCallCheckpoint",async e=>{const t={type:d.getToolCallCheckpoint,data:{requestId:e}};return(await this._asyncMsgSender.send(t,3e4)).data.checkpointNumber});o(this,"setCurrentConversation",e=>{this._asyncMsgSender.sendToSidecar({type:S.setCurrentConversation,data:{conversationId:e}})});o(this,"migrateConversationId",async(e,t)=>{await this._asyncMsgSender.sendToSidecar({type:S.migrateConversationId,data:{oldConversationId:e,newConversationId:t}},3e4)});o(this,"showAgentReview",(e,t,s,n=!0)=>{this._asyncMsgSender.sendToSidecar({type:S.chatReviewAgentFile,data:{qualifiedPathName:e,fromTimestamp:t,toTimestamp:s,retainFocus:n}})});o(this,"acceptAllAgentEdits",async()=>(await this._asyncMsgSender.sendToSidecar({type:S.chatAgentEditAcceptAll}),!0));o(this,"revertToTimestamp",async(e,t)=>(await this._asyncMsgSender.sendToSidecar({type:S.revertToTimestamp,data:{timestamp:e,qualifiedPathNames:t}}),!0));o(this,"getAgentOnboardingPrompt",async()=>(await this._asyncMsgSender.send({type:d.chatGetAgentOnboardingPromptRequest,data:{}},3e4)).data.prompt);o(this,"getAgentEditChangesByRequestId",async e=>{const t={type:S.getEditChangesByRequestIdRequest,data:{requestId:e}};return(await this._asyncMsgSender.sendToSidecar(t,3e4)).data});o(this,"getAgentEditContentsByRequestId",async e=>{const t={type:S.getAgentEditContentsByRequestId,data:{requestId:e}};return(await this._asyncMsgSender.sendToSidecar(t,3e4)).data});o(this,"triggerInitialOrientation",()=>{this._host.postMessage({type:d.triggerInitialOrientation})});o(this,"getWorkspaceInfo",async()=>{try{return(await this._asyncMsgSender.send({type:d.getWorkspaceInfoRequest},5e3)).data}catch(e){return console.error("Error getting workspace info:",e),{}}});o(this,"getRemoteAgentStatus",async()=>{try{return(await this._asyncMsgSender.send({type:d.getRemoteAgentStatus},5e3)).data}catch(e){return console.error("Error getting remote agent status:",e),{isRemoteAgentWindow:!1}}});o(this,"toggleCollapseUnchangedRegions",()=>{this._host.postMessage({type:d.toggleCollapseUnchangedRegions})});o(this,"checkAgentAutoModeApproval",async()=>(await this._asyncMsgSender.send({type:d.checkAgentAutoModeApproval},5e3)).data);o(this,"setAgentAutoModeApproved",async e=>{await this._asyncMsgSender.send({type:d.setAgentAutoModeApproved,data:e},5e3)});o(this,"checkHasEverUsedAgent",async()=>(await this._asyncMsgSender.sendToSidecar({type:S.checkHasEverUsedAgent},5e3)).data);o(this,"setHasEverUsedAgent",async e=>{await this._asyncMsgSender.sendToSidecar({type:S.setHasEverUsedAgent,data:e},5e3)});o(this,"getChatRequestIdeState",async()=>{const e={type:d.getChatRequestIdeStateRequest};return(await this._asyncMsgSender.send(e,3e4)).data});o(this,"reportError",e=>{this._host.postMessage({type:d.reportError,data:e})});this._host=e,this._asyncMsgSender=t,this._flags=s,this._taskClient=new st(t)}async*generateCommitMessage(){const e={type:d.generateCommitMessage},t=this._asyncMsgSender.stream(e,3e4,6e4);yield*O(t)}async*sendInstructionMessage(e,t){const s={instruction:e.request_message??"",selectedCodeDetails:t,requestId:e.request_id},n={type:d.chatInstructionMessage,data:s},r=this._asyncMsgSender.stream(n,3e4,6e4);yield*async function*(i){let c;try{for await(const g of i)c=g.data.requestId,yield{request_id:c,response_text:g.data.text,seen_state:A.unseen,status:_.sent};yield{request_id:c,seen_state:A.unseen,status:_.success}}catch{yield{request_id:c,seen_state:A.unseen,status:_.failed}}}(r)}async openGuidelines(e){this._host.postMessage({type:d.openGuidelines,data:e})}async*getExistingChatStream(e,t){if(!e.request_id)return;const s=t==null?void 0:t.flags.enablePreferenceCollection,n=s?1e9:6e4,r=s?1e9:3e5,i={type:d.chatGetStreamRequest,data:{requestId:e.request_id}},c=this._asyncMsgSender.stream(i,n,r);yield*O(c,this.reportError)}async*startChatStream(e,t){const s=t==null?void 0:t.flags.enablePreferenceCollection,n=s?1e9:6e4,r=s?1e9:3e5,i={type:d.chatUserMessage,data:e},c=this._asyncMsgSender.stream(i,n,r);yield*O(c,this.reportError)}async checkToolExists(e){return(await this._asyncMsgSender.send({type:d.checkToolExists,toolName:e},0)).exists}async saveImage(e,t){const s=ye(await V(e)),n=t??`${await ve(await z(s))}.${e.name.split(".").at(-1)}`;return(await this._asyncMsgSender.send({type:d.chatSaveImageRequest,data:{filename:n,data:s}},1e4)).data}async loadImage(e){const t=await this._asyncMsgSender.send({type:d.chatLoadImageRequest,data:e},1e4),s=t.data?await z(t.data):void 0;if(!s)return;let n="application/octet-stream";const r=e.split(".").at(-1);r==="png"?n="image/png":r!=="jpg"&&r!=="jpeg"||(n="image/jpeg");const i=new File([s],e,{type:n});return await V(i)}async deleteImage(e){await this._asyncMsgSender.send({type:d.chatDeleteImageRequest,data:e},1e4)}async*startChatStreamWithRetry(e,t,s){const n=new Ve(e,t,(r,i)=>this.startChatStream(r,i),(s==null?void 0:s.maxRetries)??5,4e3,s==null?void 0:s.flags);this._activeRetryStreams.set(e,n);try{yield*n.getStream()}finally{this._activeRetryStreams.delete(e)}}async getSubscriptionInfo(){return await this._asyncMsgSender.send({type:d.getSubscriptionInfo},5e3)}}async function*O(a,e=()=>{}){let t;try{for await(const s of a){if(t=s.data.requestId,s.data.error)return yield{request_id:t,seen_state:A.unseen,status:_.failed,display_error_message:s.data.error.displayErrorMessage,isRetriable:s.data.error.isRetriable};yield{request_id:t,response_text:s.data.text,workspace_file_chunks:s.data.workspaceFileChunks,structured_output_nodes:at(s.data.nodes),seen_state:A.unseen,status:_.sent}}yield{request_id:t,seen_state:A.unseen,status:_.success}}catch(s){e({originalRequestId:t||"",sanitizedMessage:s instanceof Error?s.message:String(s),stackTrace:s instanceof Error&&s.stack||"",diagnostics:[{key:"error_class",value:"Extension-WebView Error"}]}),yield{request_id:t,seen_state:A.unseen,status:_.failed}}}async function F(a,e){try{return await a}catch(t){return console.warn(`Error while resolving promise: ${t}`),e}}function at(a){if(!a)return a;let e=!1;return a.filter(t=>t.type!==te.TOOL_USE||!e&&(e=!0,!0))}var R=(a=>(a[a.unknown=0]="unknown",a[a.new=1]="new",a[a.checkingSafety=2]="checkingSafety",a[a.runnable=3]="runnable",a[a.running=4]="running",a[a.completed=5]="completed",a[a.error=6]="error",a[a.cancelling=7]="cancelling",a[a.cancelled=8]="cancelled",a))(R||{});function Xt(a){return a.requestId+";"+a.toolUseId}function Kt(a){const[e,t]=a.split(";");return{requestId:e,toolUseId:t}}function Zt(a,e){return a==null?e:typeof a=="string"?a:e}class ie{constructor(e){o(this,"_applyingFilePaths",J([]));o(this,"_appliedFilePaths",J([]));this._asyncMsgSender=e}get applyingFilePaths(){let e=[];return this._applyingFilePaths.subscribe(t=>{e=t})(),e}get appliedFilePaths(){let e=[];return this._appliedFilePaths.subscribe(t=>{e=t})(),e}async getDiffExplanation(e,t,s=3e4){try{return(await this._asyncMsgSender.send({type:d.diffExplanationRequest,data:{changedFiles:e,apikey:t}},s)).data.explanation}catch(n){return console.error("Failed to get diff explanation:",n),[]}}async groupChanges(e,t=!1,s){try{return(await this._asyncMsgSender.send({type:d.diffGroupChangesRequest,data:{changedFiles:e,changesById:t,apikey:s}})).data.groupedChanges}catch(n){return console.error("Failed to group changes:",n),[]}}async getDescriptions(e,t){try{return(await this._asyncMsgSender.send({type:d.diffDescriptionsRequest,data:{groupedChanges:e,apikey:t}})).data.explanation}catch(s){return console.error("Failed to get descriptions:",s),[]}}async applyChanges(e,t,s){this._applyingFilePaths.update(n=>[...n.filter(r=>r!==e),e]);try{(await this._asyncMsgSender.send({type:d.applyChangesRequest,data:{path:e,originalCode:t,newCode:s}},3e4)).data.success&&this._appliedFilePaths.update(n=>[...n.filter(r=>r!==e),e])}catch(n){console.error("applyChanges error",n)}finally{this._applyingFilePaths.update(n=>n.filter(r=>r!==e))}}}o(ie,"key","remoteAgentsDiffOpsModel");async function Qt(a,e){a.length&&e&&await Promise.all(a.map(async t=>await e(t.path,t.originalCode,t.newCode)))}function $(a){return a.sort((e,t)=>{const s=new Date(e.updated_at||e.started_at);return new Date(t.updated_at||t.started_at).getTime()-s.getTime()})}class W extends Error{constructor(e){super(e),this.name="StreamRetryExhaustedError"}}class rt{constructor(e,t,s,n,r=5,i=4e3){o(this,"_isCancelled",!1);o(this,"streamId");this.agentId=e,this.lastProcessedSequenceId=t,this.startStreamFn=s,this.cancelStreamFn=n,this.maxRetries=r,this.baseDelay=i,this.streamId=crypto.randomUUID()}get isCancelled(){return this._isCancelled}async cancel(){this._isCancelled=!0,await this.cancelStreamFn(this.streamId)}async*getStream(){let e=0;for(;!this._isCancelled;){const t=this.startStreamFn(this.agentId,this.streamId,this.lastProcessedSequenceId);try{for await(const s of t){if(this._isCancelled)return;e=0,yield s}return}catch(s){const n=s instanceof Error?s.message:String(s);if(n===Ce&&(this._isCancelled=!0),this._isCancelled)return;if(e++,e>this.maxRetries)throw new W(`Failed after ${this.maxRetries} attempts: ${n}`);let r=this.baseDelay*2**(e-1);n===Re?r=0:yield{errorMessage:"There was an error connecting to the remote agent.",retryAt:new Date(Date.now()+r)},console.warn(`Retrying remote agent history stream in ${r/1e3} seconds... (Attempt ${e} of ${this.maxRetries})`),await new Promise(i=>setTimeout(i,r));continue}}}}class oe{constructor(e){o(this,"_msgBroker");o(this,"_activeRetryStreams",new Map);this._msgBroker=e}hasActiveHistoryStream(e){return this._activeRetryStreams.has(e)}getActiveHistoryStream(e){return this._activeRetryStreams.get(e)}get activeHistoryStreams(){return this._activeRetryStreams}async sshToRemoteAgent(e){const t=await this._msgBroker.send({type:d.remoteAgentSshRequest,data:{agentId:e}},1e4);return!!t.data.success||(console.error("Failed to connect to remote agent:",t.data.error),!1)}async deleteRemoteAgent(e){return(await this._msgBroker.send({type:d.deleteRemoteAgentRequest,data:{agentId:e}},1e4)).data.success}showRemoteAgentHomePanel(){this._msgBroker.postMessage({type:d.showRemoteAgentHomePanel})}closeRemoteAgentHomePanel(){this._msgBroker.postMessage({type:d.closeRemoteAgentHomePanel})}async getRemoteAgentNotificationEnabled(e){return(await this._msgBroker.send({type:d.getRemoteAgentNotificationEnabledRequest,data:{agentIds:e}})).data}async setRemoteAgentNotificationEnabled(e,t){await this._msgBroker.send({type:d.setRemoteAgentNotificationEnabled,data:{agentId:e,enabled:t}})}async deleteRemoteAgentNotificationEnabled(e){await this._msgBroker.send({type:d.deleteRemoteAgentNotificationEnabled,data:{agentId:e}})}async notifyRemoteAgentReady(e){await this._msgBroker.send({type:d.remoteAgentNotifyReady,data:{agentId:e}})}showRemoteAgentDiffPanel(e){this._msgBroker.postMessage({type:d.showRemoteAgentDiffPanel,data:e})}closeRemoteAgentDiffPanel(){this._msgBroker.postMessage({type:d.closeRemoteAgentDiffPanel})}async getRemoteAgentChatHistory(e,t,s=1e4){return await this._msgBroker.send({type:d.getRemoteAgentChatHistoryRequest,data:{agentId:e,lastProcessedSequenceId:t}},s)}async sendRemoteAgentChatRequest(e,t,s=1e4){return this._msgBroker.send({type:d.remoteAgentChatRequest,data:{agentId:e,requestDetails:t}},s)}async interruptRemoteAgent(e,t=1e4){return await this._msgBroker.send({type:d.remoteAgentInterruptRequest,data:{agentId:e}},t)}async createRemoteAgent(e,t,s,n,r,i,c=1e4){return await this._msgBroker.send({type:d.createRemoteAgentRequest,data:{prompt:e,workspaceSetup:t,setupScript:s,isSetupScriptAgent:n,modelId:r,remoteAgentCreationMetrics:i}},c)}async getRemoteAgentOverviews(e=1e4){return await this._msgBroker.send({type:d.getRemoteAgentOverviewsRequest},e)}async listSetupScripts(e=5e3){return await this._msgBroker.send({type:d.listSetupScriptsRequest},e)}async saveSetupScript(e,t,s,n=5e3){return await this._msgBroker.send({type:d.saveSetupScriptRequest,data:{name:e,content:t,location:s}},n)}async deleteSetupScript(e,t,s=5e3){return await this._msgBroker.send({type:d.deleteSetupScriptRequest,data:{name:e,location:t}},s)}async renameSetupScript(e,t,s,n=5e3){return await this._msgBroker.send({type:d.renameSetupScriptRequest,data:{oldName:e,newName:t,location:s}},n)}async getRemoteAgentWorkspaceLogs(e,t,s,n=1e4){return await this._msgBroker.send({type:d.remoteAgentWorkspaceLogsRequest,data:{agentId:e,lastProcessedStep:t,lastProcessedSequenceId:s}},n)}async saveLastRemoteAgentSetup(e,t,s){return await this._msgBroker.send({type:d.saveLastRemoteAgentSetupRequest,data:{lastRemoteAgentGitRepoUrl:e,lastRemoteAgentGitBranch:t,lastRemoteAgentSetupScript:s}})}async getLastRemoteAgentSetup(){return await this._msgBroker.send({type:d.getLastRemoteAgentSetupRequest})}async*startRemoteAgentHistoryStream(e,t,s,n=6e4,r=3e5){const i={type:d.remoteAgentHistoryStreamRequest,data:{streamId:t,agentId:e,lastProcessedSequenceId:s}},c=this._msgBroker.stream(i,n,r);for await(const g of c)yield g.data}async*startRemoteAgentHistoryStreamWithRetry(e,t,s=5,n=4e3){var i;const r=new rt(e,t,(c,g,h)=>this.startRemoteAgentHistoryStream(c,g,h),c=>this._closeRemoteAgentHistoryStream(c),s,n);(i=this._activeRetryStreams.get(e))==null||i.cancel(),this._activeRetryStreams.set(e,r);try{yield*r.getStream()}finally{r.isCancelled||this._activeRetryStreams.delete(e)}}cancelRemoteAgentHistoryStream(e){const t=this._activeRetryStreams.get(e);t&&(t.cancel(),this._activeRetryStreams.delete(e))}async _closeRemoteAgentHistoryStream(e){await this._msgBroker.send({type:d.cancelRemoteAgentHistoryStreamRequest,data:{streamId:e}})}cancelAllRemoteAgentHistoryStreams(){this._activeRetryStreams.forEach(e=>{e.cancel()}),this._activeRetryStreams.clear()}dispose(){this.cancelAllRemoteAgentHistoryStreams()}async getPinnedAgentsFromStore(){try{return(await this._msgBroker.send({type:d.getRemoteAgentPinnedStatusRequest,data:{}})).data}catch(e){return console.error("Failed to get pinned agents from store:",e),{}}}async savePinnedAgentToStore(e,t){try{await this._msgBroker.send({type:d.setRemoteAgentPinnedStatus,data:{agentId:e,isPinned:t}})}catch(s){console.error("Failed to save pinned agent to store:",s)}}async deletePinnedAgentFromStore(e){try{await this._msgBroker.send({type:d.deleteRemoteAgentPinnedStatus,data:{agentId:e}})}catch(t){console.error("Failed to delete pinned agent from store:",t)}}async openDiffInBuffer(e,t,s){return await this._msgBroker.send({type:d.openDiffInBuffer,data:{oldContents:e,newContents:t,filePath:s}})}async pauseRemoteAgentWorkspace(e){return await this._msgBroker.send({type:d.remoteAgentPauseRequest,data:{agentId:e}},3e4)}async resumeRemoteAgentWorkspace(e){return await this._msgBroker.send({type:d.remoteAgentResumeRequest,data:{agentId:e}},9e4)}async reportRemoteAgentEvent(e){await this._msgBroker.send({type:d.reportRemoteAgentEvent,data:e})}}o(oe,"key","remoteAgentsClient");function it(a,e){if(a.length===0)return e;if(e.length===0)return a;const t=[];let s=0,n=0;for(;s<a.length&&n<e.length;){const r=a[s].sequence_id,i=e[n].sequence_id;i!==void 0?r!==void 0?r<i?(t.push(a[s]),s++):r>i?(t.push(e[n]),n++):(t.push(e[n]),s++,n++):(console.warn("Existing history has an exchange with an undefined sequence ID"),s++):(console.warn("New history has an exchange with an undefined sequence ID"),n++)}for(;s<a.length;)t.push(a[s]),s++;for(;n<e.length;)t.push(e[n]),n++;return t}class ee{constructor(e){o(this,"_pollingTimers",new Map);o(this,"_pollingInterval");o(this,"_failedAttempts",0);o(this,"_lastSuccessfulFetch",0);this._config=e,this._pollingInterval=e.defaultInterval}start(e){e&&this._pollingTimers.has(e)?this.stop(e):!e&&this._pollingTimers.has("global")&&this.stop("global"),this.refresh(e);const t=setInterval(()=>{this.refresh(e)},this._pollingInterval);e?this._pollingTimers.set(e,t):this._pollingTimers.set("global",t)}stop(e){if(e){const t=this._pollingTimers.get(e);t&&(clearInterval(t),this._pollingTimers.delete(e))}else for(const[t,s]of this._pollingTimers.entries())clearInterval(s),this._pollingTimers.delete(t)}async refresh(e){try{const t=await this._config.refreshFn(e);return this._failedAttempts=0,this._lastSuccessfulFetch=Date.now(),this._pollingInterval=this._config.defaultInterval,this._config.stopCondition&&e&&this._config.stopCondition(t,e)&&this.stop(e),t}catch{return this._failedAttempts++,this._failedAttempts>3?this._pollingInterval=1e4:this._pollingInterval=Math.min(1e3*Math.pow(2,this._failedAttempts),1e4),null}}isPolling(e){return e?this._pollingTimers.has(e):this._pollingTimers.size>0}get timeSinceLastSuccessfulFetch(){return Date.now()-this._lastSuccessfulFetch}get failedAttempts(){return this._failedAttempts}resetFailedAttempts(){this._failedAttempts=0}}class ot{constructor(e,t){o(this,"_state",{agentOverviews:[],agentConversations:new Map,agentLogs:new Map,maxRemoteAgents:0,maxActiveRemoteAgents:0,overviewError:void 0,conversationError:void 0,logsError:void 0,isOverviewsLoading:!1,isConversationLoading:!1,isLogsLoading:!1,logPollFailedCount:0});o(this,"_loggingMaxRetries",8);o(this,"_overviewsPollingManager");o(this,"_logsPollingManager");o(this,"_isInitialOverviewFetch",!0);o(this,"_stateUpdateSubscribers",new Set);this._flagsModel=e,this._remoteAgentsClient=t,this._overviewsPollingManager=new ee({defaultInterval:5e3,refreshFn:async()=>this.refreshAgentOverviews()}),this._logsPollingManager=new ee({defaultInterval:1e3,refreshFn:async s=>{if(!s)throw new Error("Agent ID is required for logs polling");return this.refreshAgentLogs(s)},stopCondition:(s,n)=>{if(!n)return!0;if(!this._state.agentOverviews.find(c=>c.remote_agent_id===n))return this._state.logPollFailedCount++,this._state.logPollFailedCount>this._loggingMaxRetries&&(this._state.logPollFailedCount=0,!0);const r=this.state.agentLogs.get(n),i=r==null?void 0:r.steps.at(-1);return(i==null?void 0:i.step_description)==="Indexing"&&i.status===G.success}}),this._flagsModel.subscribe(s=>{const n=this._overviewsPollingManager.isPolling()||this._remoteAgentsClient.activeHistoryStreams.size>0||this._logsPollingManager.isPolling(),r=s.enableBackgroundAgents;r&&!n?this.startStateUpdates():!r&&n&&this.stopStateUpdates()})}get state(){return this._state}startStateUpdates(e){var t,s;this._flagsModel.enableBackgroundAgents&&(e?(e.overviews&&this._overviewsPollingManager.start(),(t=e.conversation)!=null&&t.agentId&&this.startConversationStream(e.conversation.agentId),(s=e.logs)!=null&&s.agentId&&(this._state.logPollFailedCount=0,this._logsPollingManager.start(e.logs.agentId))):this._overviewsPollingManager.start())}stopStateUpdates(e){var t,s;if(!e)return this._overviewsPollingManager.stop(),this._logsPollingManager.stop(),void this.stopAllConversationStreams();e.overviews&&this._overviewsPollingManager.stop(),(t=e.conversation)!=null&&t.agentId&&this.stopConversationStream(e.conversation.agentId),(s=e.logs)!=null&&s.agentId&&this._logsPollingManager.stop(e.logs.agentId)}async refreshCurrentAgent(e){this.startConversationStream(e)}async refreshAgentOverviews(){this._isInitialOverviewFetch&&(this._state.overviewError=void 0,this._state.isOverviewsLoading=!0,this._isInitialOverviewFetch=!1);try{const e=await this._remoteAgentsClient.getRemoteAgentOverviews();if(e.data.error)throw new Error(e.data.error);e.data.maxRemoteAgents!==void 0&&(this._state.maxRemoteAgents=e.data.maxRemoteAgents),e.data.maxActiveRemoteAgents!==void 0&&(this._state.maxActiveRemoteAgents=e.data.maxActiveRemoteAgents);const t=$(e.data.overviews);return this._state.agentOverviews=t,this._state.overviewError=void 0,this._state.isOverviewsLoading=!1,this.notifySubscribers({type:"overviews",data:t}),t}catch(e){this._state.isOverviewsLoading=!1;const t=e instanceof Error?e.message:String(e);return this._isInitialOverviewFetch||this._state.agentOverviews.length===0?this._state.overviewError={errorMessage:t}:(console.warn("Background refresh failed:",e),this._overviewsPollingManager.timeSinceLastSuccessfulFetch>3e4&&this._overviewsPollingManager.failedAttempts>1&&(this._state.overviewError||(this._state.overviewError={errorMessage:`Using cached data. Refresh failed: ${t}`}))),this.notifySubscribers({type:"overviews",data:this._state.agentOverviews,error:this._state.overviewError}),this._state.agentOverviews}}async refreshAgentLogs(e){try{const t=this.state.agentLogs.get(e);let s,n;const r=t==null?void 0:t.steps.at(-1);r?(s=r.step_number,n=r.step_number===0?0:r.sequence_id+1):(s=0,n=0);const i=await this._remoteAgentsClient.getRemoteAgentWorkspaceLogs(e,s,n);if(!i.data.workspaceSetupStatus)return;const c=i.data.workspaceSetupStatus;if(c.steps.length===0)return t;const g=function(u,l){return{steps:[...u.steps,...l.steps].sort((p,m)=>p.step_number!==m.step_number?p.step_number-m.step_number:p.sequence_id-m.sequence_id)}}(t??{steps:[]},c),h={steps:g.steps.reduce((u,l)=>{const p=u[u.length-1];return p&&p.step_number===l.step_number?(p.status!==G.success&&(p.status=l.status),p.step_number===0?p.logs=l.logs:p.sequence_id<l.sequence_id&&(p.logs+=`
${l.logs}`,p.sequence_id=l.sequence_id)):u.push(l),u},[])};return this._state.agentLogs.set(e,h),this._state.logsError=void 0,this.notifySubscribers({type:"logs",agentId:e,data:h}),h}catch(t){const s=t instanceof Error?t.message:String(t);return this._state.logsError={errorMessage:s},this.notifySubscribers({type:"logs",agentId:e,data:this._state.agentLogs.get(e)||{steps:[]},error:this._state.logsError}),this._state.agentLogs.get(e)}}onStateUpdate(e){return this._stateUpdateSubscribers.add(e),e({type:"all",data:this._state}),()=>{this._stateUpdateSubscribers.delete(e)}}dispose(){this.stopStateUpdates(),this._stateUpdateSubscribers.clear()}notifySubscribers(e){this._stateUpdateSubscribers.forEach(t=>t(e))}async startConversationStream(e){this._remoteAgentsClient.hasActiveHistoryStream(e)&&this.stopConversationStream(e);const t=this._state.agentConversations.get(e)||[];let s=0;t.length>0&&(s=Math.max(...t.filter(n=>n.sequence_id!==void 0).map(n=>n.sequence_id||0))-1,s<0&&(s=0)),this._state.isConversationLoading=!0,this._state.conversationError=void 0,this.notifySubscribers({type:"conversation",agentId:e,data:t,error:this._state.conversationError});try{const n=this._remoteAgentsClient.startRemoteAgentHistoryStreamWithRetry(e,s);(async()=>{var r;try{for await(const i of n){if(!this._remoteAgentsClient.hasActiveHistoryStream(e)||(r=this._remoteAgentsClient.getActiveHistoryStream(e))!=null&&r.isCancelled)break;this.processHistoryStreamUpdate(e,i)}}catch(i){if(this._remoteAgentsClient.hasActiveHistoryStream(e)){let c;i instanceof W?(c=`Failed to connect: ${i.message}`,console.error(`Stream retry exhausted for agent ${e}: ${i.message}`)):(c=i instanceof Error?i.message:String(i),console.error(`Stream error for agent ${e}: ${c}`)),this._state.conversationError={errorMessage:c},this._state.isConversationLoading=!1;const g=this._state.agentConversations.get(e)||[];this.notifySubscribers({type:"conversation",agentId:e,data:g,error:this._state.conversationError})}}finally{this._state.isConversationLoading=!1}})()}catch(n){let r;r=n instanceof W?`Failed to connect: ${n.message}`:n instanceof Error?n.message:String(n),this._state.conversationError={errorMessage:r},this._state.isConversationLoading=!1,this.notifySubscribers({type:"conversation",agentId:e,data:t,error:this._state.conversationError})}}stopConversationStream(e){this._remoteAgentsClient.cancelRemoteAgentHistoryStream(e)}stopAllConversationStreams(){this._remoteAgentsClient.cancelAllRemoteAgentHistoryStreams()}processHistoryStreamUpdate(e,t){var s;if((n=>n.updates!==void 0)(t)){this._state.conversationError=void 0;for(const n of t.updates){const r=this._state.agentConversations.get(e)||[];switch(n.type){case D.AGENT_HISTORY_EXCHANGE:if(n.exchange){const i=it(r,[n.exchange]);this._state.agentConversations.set(e,i)}break;case D.AGENT_HISTORY_EXCHANGE_UPDATE:if(n.exchange_update){const i=n.exchange_update.sequence_id,c=r.findIndex(g=>g.sequence_id===i);if(c>=0){const g=r[c],h=((s=g.exchange)==null?void 0:s.response_text)||"";g.exchange.response_text=h+n.exchange_update.appended_text;const u=n.exchange_update.appended_nodes;if(u&&u.length>0){const l=g.exchange.response_nodes??[];g.exchange.response_nodes=[...l,...u]}}}break;case D.AGENT_HISTORY_AGENT_STATUS:if(n.agent){const i=this._state.agentOverviews.findIndex(c=>c.remote_agent_id===e);i>=0?this._state.agentOverviews[i]=n.agent:(this._state.agentOverviews.push(n.agent),this._state.agentOverviews=$(this._state.agentOverviews)),this.notifySubscribers({type:"overviews",data:this._state.agentOverviews,error:this._state.overviewError})}}}this._state.isConversationLoading=!1,this.notifySubscribers({type:"conversation",agentId:e,data:this._state.agentConversations.get(e)||[],error:this._state.conversationError})}else{this.state.conversationError=t;const n=this._state.agentConversations.get(e)||[];this.notifySubscribers({type:"conversation",agentId:e,data:n,error:this._state.conversationError})}}}class ct{constructor({msgBroker:e,isActive:t,flagsModel:s,host:n,stateModel:r,chatModel:i}){o(this,"_state",{isActive:!1,isPanelFocused:!1,currentAgentId:void 0,currentConversation:void 0,currentAgent:void 0,agentOverviews:[],chatConversations:[],localAgentConversations:[],isLoading:!1,isCurrentAgentDetailsLoading:!1,lastSuccessfulOverviewFetch:0,failedRefreshAttempts:0,maxRemoteAgents:0,maxActiveRemoteAgents:0,isDiffPanelOpen:!1,diffPanelAgentId:void 0,focusedFilePath:null,isCreatingAgent:!1,error:void 0,agentThreadsError:void 0,agentLogsError:void 0,agentChatHistoryError:void 0,remoteAgentCreationError:null,newAgentDraft:null,notificationSettings:{},pinnedAgents:{},setCurrentAgent:this.setCurrentAgent.bind(this),clearCurrentAgent:this.clearCurrentAgent.bind(this),sendMessage:this.sendMessage.bind(this),interruptAgent:this.interruptAgent.bind(this),createRemoteAgent:this.createRemoteAgent.bind(this),createRemoteAgentFromDraft:this.createRemoteAgentFromDraft.bind(this),deleteAgent:this.deleteAgent.bind(this),setNewAgentDraft:this.setNewAgentDraft.bind(this),setRemoteAgentCreationError:this.setRemoteAgentCreationError.bind(this),hasFetchedOnce:!1,showRemoteAgentDiffPanel:this.showRemoteAgentDiffPanel.bind(this),closeRemoteAgentDiffPanel:this.closeRemoteAgentDiffPanel.bind(this),setIsCreatingAgent:this.setIsCreatingAgent.bind(this),toggleAgentPinned:this.toggleAgentPinned.bind(this),setPinnedAgents:this.setPinnedAgents.bind(this),pauseRemoteAgentWorkspace:this.pauseRemoteAgentWorkspace.bind(this),resumeRemoteAgentWorkspace:this.resumeRemoteAgentWorkspace.bind(this)});o(this,"_agentConversations",new Map);o(this,"_initialPrompts",new Map);o(this,"_agentSetupLogsCache",new Map);o(this,"_creationMetrics");o(this,"_preloadedDiffExplanations",new Map);o(this,"maxCacheEntries",10);o(this,"maxCacheSizeBytes",10485760);o(this,"_diffOpsModel");o(this,"subscribers",new Set);o(this,"agentSetupLogs");o(this,"_remoteAgentsClient");o(this,"_stateModel");o(this,"_extensionClient");o(this,"_flagsModel");o(this,"_cachedUrls",new Map);o(this,"_externalRefCount",0);o(this,"_chatModel");o(this,"dispose",()=>{this._stateModel.dispose(),this._remoteAgentsClient.dispose(),this._agentConversations.clear(),this._agentSetupLogsCache.clear(),this._preloadedDiffExplanations.clear(),this.subscribers.clear()});this._state.isActive=t,this._flagsModel=s,this._diffOpsModel=new ie(e),this._remoteAgentsClient=new oe(e),this._chatModel=i,this._extensionClient=new nt(n,e,s),this._stateModel=r||new ot(this._flagsModel,this._remoteAgentsClient),this._stateModel.onStateUpdate(this.handleStateUpdate.bind(this)),t&&this._stateModel.startStateUpdates(),this.loadPinnedAgentsFromStore()}_setChatModel(e){this._chatModel=e}handleOverviewsUpdate(e){const t=e.data,s=this._state.agentOverviews,n=t;this._state.currentAgentId&&(this._state.currentAgent=this._state.agentOverviews.find(i=>i.remote_agent_id===this._state.currentAgentId)),this.maybeSendNotifications(n,s);const r=$(n);this._state.agentOverviews=r,this._state.hasFetchedOnce=!0,this._state.agentThreadsError=e.error,this._state.lastSuccessfulOverviewFetch=e.error?this._state.lastSuccessfulOverviewFetch:Date.now(),r.findIndex(i=>i.remote_agent_id===this._state.currentAgentId)===-1&&this.clearCurrentAgent()}handleConversationUpdate(e){if(e.agentId===this._state.currentAgentId){const t={exchanges:e.data,lastFetched:new Date};this._agentConversations.set(e.agentId,t),this._state.currentConversation=t,this._state.agentChatHistoryError=e.error,this._state.isCurrentAgentDetailsLoading=!1}}handleLogsUpdate(e){e.agentId===this._state.currentAgentId&&(this.agentSetupLogs=e.data,this._agentSetupLogsCache.set(e.agentId,e.data))}handleStateUpdate(e){switch(this._state.maxRemoteAgents=this._stateModel.state.maxRemoteAgents,this._state.maxActiveRemoteAgents=this._stateModel.state.maxActiveRemoteAgents,e.type){case"overviews":this.handleOverviewsUpdate(e);break;case"conversation":this.handleConversationUpdate(e);break;case"logs":this.handleLogsUpdate(e);break;case"all":this.handleOverviewsUpdate({type:"overviews",data:e.data.agentOverviews,error:e.data.overviewError}),e.data.agentConversations.forEach((t,s)=>{this._agentConversations.set(s,{exchanges:t,lastFetched:new Date})}),e.data.agentLogs.forEach((t,s)=>{t&&(this._agentSetupLogsCache.set(s,t),s===this._state.currentAgentId&&(this.agentSetupLogs=t))}),this._state.hasFetchedOnce=!0,this._state.agentThreadsError=e.data.overviewError,this._state.agentChatHistoryError=e.data.conversationError,this._state.agentLogsError=e.data.logsError}this.notifySubscribers()}subscribe(e){return this.subscribers.add(e),e(this),()=>{this.subscribers.delete(e)}}notifySubscribers(){this.subscribers.forEach(e=>e(this))}showRemoteAgentDiffPanel(e){const t=this._state.currentAgentId;if(t&&e.changedFiles.length>0&&e.turnIdx===-1&&e.isShowingAggregateChanges){const s=`${t}-${this.generateChangedFilesHash(e.changedFiles)}`,n=this._preloadedDiffExplanations.get(s);if(n)return n.lastAccessed=Date.now(),this._preloadedDiffExplanations.set(s,n),this._remoteAgentsClient.showRemoteAgentDiffPanel({...e,preloadedExplanation:n.explanation}),this._state.isDiffPanelOpen=!0,this._state.diffPanelAgentId=t,void this.notifySubscribers()}this._remoteAgentsClient.showRemoteAgentDiffPanel(e),this._state.isDiffPanelOpen=!0,this._state.diffPanelAgentId=t,this.notifySubscribers()}closeRemoteAgentDiffPanel(){this._remoteAgentsClient.closeRemoteAgentDiffPanel(),this._state.isDiffPanelOpen=!1,this._state.diffPanelAgentId=void 0,this.notifySubscribers()}get flagsModel(){return this._flagsModel}_getChatHistory(e){const t=this._agentConversations.get(e);if(!t)return[];const s=this.isAgentRunning(e);return t.exchanges.map(({exchange:n},r)=>{const i=n.request_id.startsWith("pending-");return{seen_state:A.seen,structured_request_nodes:n.request_nodes??[],status:i||r===t.exchanges.length-1&&s?_.sent:_.success,request_message:n.request_message,response_text:i?"":n.response_text,structured_output_nodes:n.response_nodes??[],request_id:n.request_id??`remote-agent-${r}`}})}getCurrentChatHistory(){const e=this.agentSetupLogs;return this.currentAgentId&&!e&&(this.agentSetupLogs={steps:[]},this._stateModel.startStateUpdates({logs:{agentId:this.currentAgentId}})),this._getChatHistory(this.currentAgentId??"")}getToolStates(){var c,g,h;const e=new Map,t=new Set,s=new Map;(c=this.currentConversation)==null||c.exchanges.forEach(u=>{var l,p;(l=u.exchange.response_nodes)==null||l.forEach(m=>{m.tool_use&&t.add(m.tool_use.tool_use_id)}),(p=u.exchange.request_nodes)==null||p.forEach(m=>{m.type===U.TOOL_RESULT&&m.tool_result_node&&s.set(m.tool_result_node.tool_use_id,m.tool_result_node)})});const n=(g=this.currentConversation)==null?void 0:g.exchanges[this.currentConversation.exchanges.length-1];let r=0,i=null;return(h=n==null?void 0:n.exchange.response_nodes)==null||h.forEach(u=>{var l;u.id>r&&(r=u.id,i=(l=u.tool_use)!=null&&l.tool_use_id?u.tool_use.tool_use_id:null)}),t.forEach(u=>{const l=s.get(u);if(l)e.set(u,{phase:l.is_error?R.error:R.completed,result:{isError:l.is_error,text:l.content},requestId:"",toolUseId:u});else{const p=this.isCurrentAgentRunning;u===i?e.set(u,{phase:p?R.running:R.cancelled,requestId:"",toolUseId:u}):e.set(u,{phase:R.cancelled,requestId:"",toolUseId:u})}}),e}getLastToolUseState(){const e=this.getToolStates(),t=[...e.keys()].pop();return e.get(t??"")??{phase:R.unknown}}getToolUseState(e){const t=e;return this.getToolStates().get(t)??{phase:R.completed,requestId:"",toolUseId:e}}async setCurrentAgent(e){this._state.currentAgentId&&this._stateModel.stopStateUpdates({conversation:{agentId:this._state.currentAgentId},logs:{agentId:this._state.currentAgentId}}),this._state.currentAgentId=e,this._state.isCurrentAgentDetailsLoading=!!e,e&&this._agentSetupLogsCache.has(e)?this.agentSetupLogs=this._agentSetupLogsCache.get(e):this.agentSetupLogs=void 0,this.notifySubscribers(),e&&(this._stateModel.startStateUpdates({conversation:{agentId:e},logs:{agentId:e}}),this.preloadDiffExplanation(e))}clearCurrentAgent(){this._state.currentAgentId&&this._stateModel.stopStateUpdates({conversation:{agentId:this._state.currentAgentId},logs:{agentId:this._state.currentAgentId}}),this._state.currentAgentId=void 0,this.agentSetupLogs=void 0,this.notifySubscribers()}async preloadDiffExplanation(e){const t=this._agentConversations.get(e);if(!t||t.exchanges.length===0)return;const s=be(t.exchanges);if(s.length===0)return;const n=`${e}-${this.generateChangedFilesHash(s)}`;if(this._preloadedDiffExplanations.get(n)||s.length>12)return;let r=0;if(s.forEach(i=>{var c,g;r+=(((c=i.old_contents)==null?void 0:c.length)||0)+(((g=i.new_contents)==null?void 0:g.length)||0)}),!(r>512e3))try{const i=await this._diffOpsModel.getDiffExplanation(s,void 0,6e4);if(i&&i.length>0){const c=this.generateChangedFilesHash(s),g=`${e}-${c}`;this._preloadedDiffExplanations.set(g,{explanation:i,changedFiles:s,userPrompt:this.getUserMessagePrecedingTurn(t.exchanges,0),timestamp:Date.now(),lastAccessed:Date.now(),changedFilesHash:c,turnIdx:-1}),this.manageCacheSize()}}catch(i){console.error("Failed to preload diff explanation:",i)}}getUserMessagePrecedingTurn(e,t){return e.length===0||t<0||t>=e.length?"":e[t].exchange.request_message||""}generateChangedFilesHash(e){const t=e.map(s=>{var n,r;return{oldPath:s.old_path,newPath:s.new_path,oldSize:((n=s.old_contents)==null?void 0:n.length)||0,newSize:((r=s.new_contents)==null?void 0:r.length)||0,oldHash:this.simpleHash(s.old_contents||""),newHash:this.simpleHash(s.new_contents||"")}});return this.simpleHash(JSON.stringify(t))}simpleHash(e){let t=0;for(let s=0;s<e.length;s++)t=(t<<5)-t+e.charCodeAt(s),t|=0;return t.toString(36)}manageCacheSize(){if(this._preloadedDiffExplanations.size<=this.maxCacheEntries)return;const e=Array.from(this._preloadedDiffExplanations.entries()).map(([s,n])=>({key:s,value:n,accessTime:n.lastAccessed||n.timestamp})).sort((s,n)=>s.accessTime-n.accessTime);let t=0;for(e.forEach(s=>{const n=JSON.stringify(s.value.explanation).length,r=s.value.changedFiles.reduce((i,c)=>{var g,h;return i+(((g=c.old_contents)==null?void 0:g.length)||0)+(((h=c.new_contents)==null?void 0:h.length)||0)},0);t+=n+r});e.length>0&&(e.length>this.maxCacheEntries||t>this.maxCacheSizeBytes);){const s=e.shift();if(s){this._preloadedDiffExplanations.delete(s.key);const n=JSON.stringify(s.value.explanation).length,r=s.value.changedFiles.reduce((i,c)=>{var g,h;return i+(((g=c.old_contents)==null?void 0:g.length)||0)+(((h=c.new_contents)==null?void 0:h.length)||0)},0);t-=n+r}}}async sendMessage(e,t){const s=this._state.currentAgentId;if(!s)return this._state.error="No active remote agent",this.notifySubscribers(),!1;this._state.isLoading=!0,this._state.error=void 0,this.notifySubscribers();try{const n=this._agentConversations.get(s)||{exchanges:[],lastFetched:new Date},r=(this.getfinalSequenceId(s)||0)+1,i={exchange:{request_message:e,response_text:"",request_id:"pending-"+Date.now(),response_nodes:[],request_nodes:[]},changed_files:[],sequence_id:r};n.exchanges.push(i),this._agentConversations.set(s,n),this._state.currentConversation=n,this.notifySubscribers();const c={request_nodes:[{id:1,type:U.TEXT,text_node:{content:e}}],model_id:t};return await this._remoteAgentsClient.sendRemoteAgentChatRequest(s,c),this._state.currentAgentId&&setTimeout(()=>{this.preloadDiffExplanation(this._state.currentAgentId)},0),await this._stateModel.refreshCurrentAgent(s),await this._stateModel.refreshAgentOverviews(),this.preloadDiffExplanation(s),!0}catch(n){return this._state.error=n instanceof Error?n.message:String(n),this.notifySubscribers(),!1}finally{this._state.isLoading=!1,this.notifySubscribers()}}async interruptAgent(){const e=this._state.currentAgentId;if(!e)return this._state.error="No active remote agent",void this.notifySubscribers();this._state.isLoading=!0,this._state.error=void 0,this.notifySubscribers();try{await this._remoteAgentsClient.interruptRemoteAgent(e),await this._stateModel.refreshCurrentAgent(e),await this._stateModel.refreshAgentOverviews()}catch(t){this._state.error=t instanceof Error?t.message:String(t)}finally{this._state.isLoading=!1,this.notifySubscribers()}}async createRemoteAgent(e,t,s,n,r){var i;if(!e||!e.trim())return this._state.error="Cannot create a remote agent with an empty prompt",void this.notifySubscribers();this.agentSetupLogs=void 0,this._state.isLoading=!0,this._state.error=void 0,this.notifySubscribers();try{const c=await this._remoteAgentsClient.createRemoteAgent(e,t,s,n,r,this._creationMetrics);if(c.data.agentId)return this._initialPrompts.set(c.data.agentId,e),await this.setNotificationEnabled(c.data.agentId,((i=this.newAgentDraft)==null?void 0:i.enableNotification)??!0),await this.setCurrentAgent(c.data.agentId),c.data.agentId;throw new Error("Failed to create remote agent: No agent ID returned")}catch(c){throw this._state.error=c instanceof Error?c.message:String(c),this.notifySubscribers(),c}finally{this._state.isLoading=!1,this.notifySubscribers()}}async createRemoteAgentFromDraft(e,t){var r,i;if(this.setRemoteAgentCreationError(null),this.agentSetupLogs=void 0,!e||!e.trim())return void this.setRemoteAgentCreationError("Cannot create a remote agent with an empty prompt");const s=this._state.newAgentDraft;if(!s)return void this.setRemoteAgentCreationError("No workspace selected. Please select a workspace first.");if(s.isDisabled)return void this.setRemoteAgentCreationError("Cannot create agent with current workspace selection. Please resolve the issues with your workspace selection.");if(!s.commitRef||!s.selectedBranch)return void this.setRemoteAgentCreationError("No workspace selected. Please select a workspace first.");const n={starting_files:s.commitRef};this._state.isLoading=!0,this.notifySubscribers();try{const c=s.isSetupScriptAgent||((r=s.setupScript)==null?void 0:r.isGenerateOption)===!0;let g=c||(i=s.setupScript)==null?void 0:i.content;if(s.setupScript&&!c){const h=(await this.listSetupScripts()).find(u=>u.path===s.setupScript.path);h&&(g=h.content)}try{return await this.createRemoteAgent(e,n,g,c,t)}catch(h){let u="Failed to create remote agent. Please try again.";return h instanceof Error&&(h.message.includes("too large")||h.message.includes("413")?u="Repository or selected files are too large. Please select a smaller repository or branch.":h.message.includes("timeout")||h.message.includes("504")?u="Request timed out. The repository might be too large or the server is busy.":h.message.includes("rate limit")||h.message.includes("429")?u="Rate limit exceeded. Please try again later.":h.message.includes("unauthorized")||h.message.includes("401")?u="Authentication failed. Please check your GitHub credentials.":h.message.includes("not found")||h.message.includes("404")?u="Repository or branch not found. Please check your selection.":h.message.includes("bad request")||h.message.includes("400")?u="Invalid request. Please check your workspace setup and try again.":h.message.length>0&&(u=`Failed to create remote agent: ${h.message}`)),void this.setRemoteAgentCreationError(u)}}finally{this._state.isLoading=!1,this.notifySubscribers()}}async deleteAgent(e,t=!1){if(!(this._chatModel&&!t&&!await this._chatModel.extensionClient.openConfirmationModal({title:"Delete Remote Agent",message:"Are you sure you want to delete this remote agent?",confirmButtonText:"Delete",cancelButtonText:"Cancel"}))){this._state.isLoading=!0,this._state.error=void 0,this.notifySubscribers();try{if(!await this._remoteAgentsClient.deleteRemoteAgent(e))return this._state.error="Failed to delete remote agent",void this.notifySubscribers();this._agentConversations.delete(e),this._agentSetupLogsCache.delete(e),this._state.agentOverviews=this._state.agentOverviews.filter(s=>s.remote_agent_id!==e),this.removeNotificationEnabled(e),this._state.currentAgentId===e&&this.clearCurrentAgent()}catch(s){this._state.error=s instanceof Error?s.message:String(s)}finally{this._state.isLoading=!1,this.notifySubscribers()}}}async sshToRemoteAgent(e){this._state.isLoading=!0,this._state.error=void 0,this.notifySubscribers();try{return e.workspace_status!==ge.workspaceRunning&&(await this._remoteAgentsClient.resumeRemoteAgentWorkspace(e.remote_agent_id),await new Promise(t=>setTimeout(t,5e3))),await this._remoteAgentsClient.sshToRemoteAgent(e.remote_agent_id)}catch(t){return this._state.error=t instanceof Error?t.message:String(t),this.notifySubscribers(),!1}finally{this._state.isLoading=!1,this.notifySubscribers()}}async maybeSendNotifications(e,t){const s=new Map(t.map(r=>[r.remote_agent_id,r])),n=await this._remoteAgentsClient.getRemoteAgentNotificationEnabled(e.map(r=>r.remote_agent_id));e.forEach(r=>{const i=s.get(r.remote_agent_id),c=n[r.remote_agent_id],g=(i==null?void 0:i.status)===M.agentRunning,h=r.status===M.agentIdle||r.status===M.agentFailed,u=r.remote_agent_id!==this._state.currentAgentId,l=this._state.isPanelFocused;c&&g&&h&&(u||!l)&&this._remoteAgentsClient.notifyRemoteAgentReady(r.remote_agent_id)})}async setNotificationEnabled(e,t){await this._remoteAgentsClient.setRemoteAgentNotificationEnabled(e,t),this._state={...this._state,notificationSettings:{...this._state.notificationSettings,[e]:t}},this.notifySubscribers()}async removeNotificationEnabled(e){await this._remoteAgentsClient.deleteRemoteAgentNotificationEnabled(e);const{[e]:t,...s}=this._state.notificationSettings;this._state={...this._state,notificationSettings:s},this.notifySubscribers()}get hasFetchedOnce(){return this._state.hasFetchedOnce}get focusedFilePath(){return this._state.focusedFilePath}setFocusedFilePath(e){this._state.focusedFilePath=e,this.notifySubscribers()}handleMessageFromExtension(e){switch(e.data.type){case d.diffViewFileFocus:return this.setFocusedFilePath(e.data.data.filePath.replace(/^\/+/,"")),!0;case d.showRemoteAgentDiffPanel:return this._state.isDiffPanelOpen=!0,!0;case d.closeRemoteAgentDiffPanel:return this._state.isDiffPanelOpen=!1,!0;default:return!1}}get currentAgentId(){return this._state.currentAgentId}get currentConversation(){return this._agentConversations.get(this._state.currentAgentId??"")??void 0}_getAgentExchanges(e){var t;return((t=this._agentConversations.get(e))==null?void 0:t.exchanges)||[]}get currentExchanges(){const e=this._state.currentAgentId;return e?this._getAgentExchanges(e):[]}get currentStatus(){var t;const e=this._state.currentAgentId;return e&&((t=this._state.agentOverviews.find(s=>s.remote_agent_id===e))==null?void 0:t.status)||M.agentIdle}get currentAgent(){const e=this._state.currentAgentId;return e?this._state.agentOverviews.find(t=>t.remote_agent_id===e):void 0}get agentOverviews(){return this._state.agentOverviews}get isLoading(){return this._state.isLoading}get isCurrentAgentDetailsLoading(){return this._state.isCurrentAgentDetailsLoading}get lastSuccessfulOverviewFetch(){return this._state.lastSuccessfulOverviewFetch}get error(){return this._state.error}get agentThreadsError(){return this._state.agentThreadsError}get agentChatHistoryError(){return this._state.agentChatHistoryError}isAgentRunning(e){const t=this._state.agentOverviews.find(i=>i.remote_agent_id===e),s=!(!t||t.status!==M.agentRunning&&t.status!==M.agentStarting),n=this._getAgentExchanges(e),r=n.length>0&&n[n.length-1].exchange.request_id.startsWith("pending-");return s||r}get isCurrentAgentRunning(){return!!this._state.currentAgentId&&this.isAgentRunning(this._state.currentAgentId)}get maxRemoteAgents(){return this._state.maxRemoteAgents}get maxActiveRemoteAgents(){return this._state.maxActiveRemoteAgents}getInitialPrompt(e){return this._initialPrompts.get(e)}clearInitialPrompt(e){this._initialPrompts.delete(e)}get notificationSettings(){return this._state.notificationSettings}get pinnedAgents(){return this._state.pinnedAgents}getfinalSequenceId(e){var n;const t=this._agentConversations.get(e),s=t==null?void 0:t.exchanges;if(s)return((n=s[s.length-1])==null?void 0:n.sequence_id)??void 0}async listSetupScripts(){try{return(await this._remoteAgentsClient.listSetupScripts()).data.scripts}catch(e){return this._state.error=e instanceof Error?e.message:String(e),this.notifySubscribers(),[]}}async saveSetupScript(e,t,s){try{const n=await this._remoteAgentsClient.saveSetupScript(e,t,s);return n.data.success||(this._state.error=n.data.error||"Failed to save setup script",this.notifySubscribers()),n.data}catch(n){return this._state.error=n instanceof Error?n.message:String(n),this.notifySubscribers(),{success:!1,error:n instanceof Error?n.message:String(n)}}}async deleteSetupScript(e,t){try{const s=await this._remoteAgentsClient.deleteSetupScript(e,t);return s.data.success||(this._state.error=s.data.error||"Failed to delete setup script",this.notifySubscribers()),s.data}catch(s){return this._state.error=s instanceof Error?s.message:String(s),this.notifySubscribers(),{success:!1,error:s instanceof Error?s.message:String(s)}}}async renameSetupScript(e,t,s){try{const n=await this._remoteAgentsClient.renameSetupScript(e,t,s);return n.data.success||(this._state.error=n.data.error||"Failed to rename setup script",this.notifySubscribers()),n.data}catch(n){return this._state.error=n instanceof Error?n.message:String(n),this.notifySubscribers(),{success:!1,error:n instanceof Error?n.message:String(n)}}}get isActive(){return this._state.isActive}setIsActive(e){this._state.isActive=e,e?this._stateModel.startStateUpdates():this._externalRefCount===0?this._stateModel.stopStateUpdates():this._externalRefCount>0&&this._stateModel.startStateUpdates(),this.notifySubscribers()}updateChatConversations(){if(!this._chatModel)return void console.warn("No chat model available to update conversations");const e=Object.values(this._chatModel.conversations),t=e.filter(n=>{var r;return!((r=n.extraData)!=null&&r.isAgentConversation)}),s=e.filter(n=>{var r;return((r=n.extraData)==null?void 0:r.isAgentConversation)===!0});this._state.chatConversations=t,this._state.localAgentConversations=s,this.notifySubscribers()}setExternalRefCount(e){e!==this._externalRefCount&&(this._externalRefCount=e,e!==0||this._state.isActive?e>0&&!this._state.isActive&&this._stateModel.startStateUpdates():this._stateModel.stopStateUpdates())}get isPanelFocused(){return this._state.isPanelFocused}setIsPanelFocused(e){this._state.isPanelFocused=e,this.notifySubscribers()}optimisticallyClearAgentUpdates(e){var s;const t=this._state.agentOverviews.findIndex(n=>n.remote_agent_id===e);if(t!==-1&&this._state.agentOverviews[t].has_updates){const n=[...this._state.agentOverviews];n[t]={...n[t],has_updates:!1},this._state.agentOverviews=n,((s=this._state.currentAgent)==null?void 0:s.remote_agent_id)===e&&(this._state.currentAgent={...this._state.currentAgent,has_updates:!1}),this.notifySubscribers()}}setRemoteAgentCreationError(e){this._state.remoteAgentCreationError=e,this.notifySubscribers()}get isDiffPanelOpen(){return this._state.isDiffPanelOpen}get diffPanelAgentId(){return this._state.diffPanelAgentId}get remoteAgentCreationError(){return this._state.remoteAgentCreationError}setNewAgentDraft(e){this._state.newAgentDraft=e,this.notifySubscribers()}setCreationMetrics(e){this._creationMetrics=e}get creationMetrics(){return this._creationMetrics}refreshAgentChatHistory(e){this._stateModel.refreshCurrentAgent(e)}get newAgentDraft(){return this._state.newAgentDraft}setIsCreatingAgent(e){this._state.isCreatingAgent=e,this.notifySubscribers()}get isCreatingAgent(){return this._state.isCreatingAgent}async showRemoteAgentHomePanel(){await this._remoteAgentsClient.showRemoteAgentHomePanel()}async closeRemoteAgentHomePanel(){await this._remoteAgentsClient.closeRemoteAgentHomePanel()}async saveLastRemoteAgentSetup(e,t,s){try{await this._remoteAgentsClient.saveLastRemoteAgentSetup(e,t,s)}catch(n){console.error("Failed to save last remote agent setup:",n)}}async getLastRemoteAgentSetup(){try{return(await this._remoteAgentsClient.getLastRemoteAgentSetup()).data}catch(e){return console.error("Failed to get last remote agent setup:",e),{lastRemoteAgentGitRepoUrl:null,lastRemoteAgentGitBranch:null,lastRemoteAgentSetupScript:null}}}async loadPinnedAgentsFromStore(){try{const e=await this._remoteAgentsClient.getPinnedAgentsFromStore();this._state.pinnedAgents=e,this.notifySubscribers()}catch(e){console.error("Failed to load pinned agents from store:",e)}}async toggleAgentPinned(e,t){if(!e)return this._state.pinnedAgents;t=t??!1;try{if(this._state.pinnedAgents={...this._state.pinnedAgents,[e]:!t},t){const{[e]:s,...n}=this._state.pinnedAgents;this._state.pinnedAgents=n,await this._remoteAgentsClient.deletePinnedAgentFromStore(e)}else await this._remoteAgentsClient.savePinnedAgentToStore(e,!0);return this.notifySubscribers(),await this._remoteAgentsClient.getPinnedAgentsFromStore()}catch(s){return console.error("Failed to toggle pinned status for remote agent:",s),this._state.pinnedAgents}}async getConversationUrl(e){var g;const t=this._cachedUrls.get(e),s=this._agentConversations.get(e),n=(s==null?void 0:s.exchanges.length)??0;if(t&&s&&t[0]===n)return t[1];const r=this._getChatHistory(e).map(h=>({...h,request_id:h.request_id||"",request_message:h.request_message,response_text:h.response_text||""}));if(r.length===0)throw new Error("No chat history to share");const i=await this._extensionClient.saveChat(e,r,`Remote Agent ${e}`);if(!i.data)throw new Error("Failed to create URL");const c=(g=i.data)==null?void 0:g.url;return c&&this._cachedUrls.set(e,[n,c]),c}async refreshAgentThreads(){this._state.agentThreadsError=void 0,this._state.isLoading=!0,this.notifySubscribers();try{await this._stateModel.refreshAgentOverviews()}catch(e){console.error("Failed to refresh agent threads:",e)}finally{this._state.isLoading=!1,this.notifySubscribers()}}async openDiffInBuffer(e,t,s){await this._remoteAgentsClient.openDiffInBuffer(e,t,s)}async pauseRemoteAgentWorkspace(e){await this._remoteAgentsClient.pauseRemoteAgentWorkspace(e)}async resumeRemoteAgentWorkspace(e){await this._remoteAgentsClient.resumeRemoteAgentWorkspace(e)}async reportRemoteAgentEvent(e){await this._remoteAgentsClient.reportRemoteAgentEvent(e)}setPinnedAgents(e){this._state.pinnedAgents={...e},this.notifySubscribers()}}o(ct,"key","remoteAgentsModel");function Yt(){const a=Ie("chatModel");return a||console.warn("ChatModel not found in context"),a}function es(a){return Me("chatModel",a),a}function dt(a){let e,t,s=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 448 512"},a[0]],n={};for(let r=0;r<s.length;r+=1)n=H(n,s[r]);return{c(){e=B("svg"),t=new xe(!0),this.h()},l(r){e=Ee(r,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var i=Pe(e);t=Fe(i,!0),i.forEach(N),this.h()},h(){t.a=null,X(e,n)},m(r,i){qe(r,e,i),t.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M441 103c9.4 9.4 9.4 24.6 0 33.9L177 401c-9.4 9.4-24.6 9.4-33.9 0L7 265c-9.4-9.4-9.4-24.6 0-33.9s24.6-9.4 33.9 0l119 119L407 103c9.4-9.4 24.6-9.4 33.9 0z"/>',e)},p(r,[i]){X(e,n=ke(s,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 448 512"},1&i&&r[0]]))},i:T,o:T,d(r){r&&N(e)}}}function lt(a,e,t){return a.$$set=s=>{t(0,e=H(H({},e),K(s)))},[e=K(e)]}class ts extends se{constructor(e){super(),ne(this,e,lt,dt,ae,{})}}function gt(a){let e,t;return{c(){e=B("svg"),t=B("path"),w(t,"fill-rule","evenodd"),w(t,"clip-rule","evenodd"),w(t,"d","M12 13C12.5523 13 13 12.5523 13 12V3C13 2.44771 12.5523 2 12 2H3C2.44771 2 2 2.44771 2 3V6.5C2 6.77614 2.22386 7 2.5 7C2.77614 7 3 6.77614 3 6.5V3H12V12H8.5C8.22386 12 8 12.2239 8 12.5C8 12.7761 8.22386 13 8.5 13H12ZM9 6.5C9 6.5001 9 6.50021 9 6.50031V6.50035V9.5C9 9.77614 8.77614 10 8.5 10C8.22386 10 8 9.77614 8 9.5V7.70711L2.85355 12.8536C2.65829 13.0488 2.34171 13.0488 2.14645 12.8536C1.95118 12.6583 1.95118 12.3417 2.14645 12.1464L7.29289 7H5.5C5.22386 7 5 6.77614 5 6.5C5 6.22386 5.22386 6 5.5 6H8.5C8.56779 6 8.63244 6.01349 8.69139 6.03794C8.74949 6.06198 8.80398 6.09744 8.85143 6.14433C8.94251 6.23434 8.9992 6.35909 8.99999 6.49708L8.99999 6.49738"),w(t,"fill","currentColor"),w(e,"class",a[0]),w(e,"width","15"),w(e,"height","15"),w(e,"viewBox","0 0 15 15"),w(e,"fill","none"),w(e,"xmlns","http://www.w3.org/2000/svg")},m(s,n){Te(s,e,n),Le(e,t)},p(s,[n]){1&n&&w(e,"class",s[0])},i:T,o:T,d(s){s&&N(e)}}}function ht(a,e,t){let{class:s=""}=e;return a.$$set=n=>{"class"in n&&t(0,s=n.class)},[s]}class ss extends se{constructor(e){super(),ne(this,e,ht,gt,ae,{class:0})}}export{Qt as $,$e as A,Xe as B,ts as C,Ke as D,_ as E,Ze as F,Nt as G,Vt as H,Ye as I,et as J,tt as K,Ft as L,Ot as M,$t as N,ss as O,Jt as P,zt as Q,ct as R,A as S,R as T,Ht as U,Wt as V,Gt as W,jt as X,Bt as Y,ie as Z,Zt as _,It as a,Et as b,xt as c,Ct as d,Rt as e,bt as f,Yt as g,Ge as h,Dt as i,Mt as j,Pt as k,qt as l,Lt as m,Tt as n,kt as o,Kt as p,Xt as q,wt as r,es as s,We as t,Qe as u,nt as v,At as w,ze as x,Je as y,Ut as z};
