<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quantum Edge - Binary Options Trading Bot</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #4286f4;
            --secondary-color: #ff6384;
            --success-color: #10b981;
            --danger-color: #ef4444;
            --warning-color: #f59e0b;
            --dark-bg: #0f0f1a;
            --medium-bg: #1a1a2e;
            --light-bg: #28293d;
            --text-light: #ffffff;
            --text-medium: #d1d5db;
            --text-dark: #9ca3af;
            --border-color: rgba(66, 134, 244, 0.2);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: transparent;
            color: var(--text-light);
            overflow: hidden;
        }

        .floating-container {
            position: fixed;
            top: 70px;
            left: 10px;
            width: 300px;
            background-color: rgba(15, 15, 26, 0.95);
            border-radius: 10px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.5);
            z-index: 9999999;
            backdrop-filter: blur(5px);
            border: 1px solid var(--border-color);
            transition: transform 0.3s ease, opacity 0.3s ease;
            overflow: hidden;
            max-height: 80vh;
            display: flex;
            flex-direction: column;
            user-select: none;
            touch-action: none;
            margin: 0;
            padding: 0;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 15px;
            background-color: var(--medium-bg);
            border-bottom: 1px solid var(--border-color);
            cursor: grab;
            position: relative;
            overflow: hidden;
        }

        .header::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
            opacity: 0.8;
        }

        .header h1 {
            font-size: 16px;
            font-weight: 600;
            color: var(--primary-color);
            display: flex;
            align-items: center;
        }

        .header h1 i {
            margin-right: 8px;
        }

        .content {
            padding: 15px;
            overflow-y: auto;
            flex: 1;
            scrollbar-width: thin;
            scrollbar-color: var(--primary-color) var(--medium-bg);
        }

        .balance-display {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding: 10px;
            background-color: var(--dark-bg);
            border-radius: 5px;
            border: 1px solid rgba(66, 134, 244, 0.3);
        }

        .balance-label {
            font-weight: bold;
            color: var(--text-medium);
        }

        .balance-value {
            font-weight: bold;
            color: var(--primary-color);
            font-size: 16px;
        }

        .setting-group {
            margin-bottom: 12px;
            background-color: var(--dark-bg);
            padding: 10px;
            border-radius: 5px;
            border: 1px solid rgba(66, 134, 244, 0.2);
        }

        .setting-label {
            font-weight: bold;
            color: var(--text-medium);
            margin-bottom: 5px;
        }

        .setting-value {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .suggestion {
            color: var(--text-dark);
            font-size: 12px;
        }

        .risk-input-container {
            width: 100%;
        }

        .setting-input {
            width: 100%;
            padding: 5px;
            background-color: var(--dark-bg);
            border: 1px solid rgba(66, 134, 244, 0.3);
            color: var(--primary-color);
            border-radius: 4px;
            font-weight: bold;
            text-align: right;
        }

        select.setting-input {
            appearance: none;
            -webkit-appearance: none;
            -moz-appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%234286f4' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 8px center;
            background-size: 12px;
            padding-right: 30px;
        }

        .trading-controls {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }

        .action-btn {
            flex: 1;
            padding: 10px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            color: white;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        #startStopBtn {
            background-color: var(--success-color);
        }

        #startStopBtn.stop {
            background-color: var(--danger-color);
        }

        #pauseResumeBtn {
            background-color: var(--warning-color);
        }

        #pauseResumeBtn.resume {
            background-color: var(--primary-color);
        }

        .action-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .trading-status {
            background-color: var(--dark-bg);
            padding: 10px;
            border-radius: 5px;
            border: 1px solid rgba(66, 134, 244, 0.2);
            margin-top: 10px;
        }

        .trade-mode-buttons {
            display: flex;
            width: 100%;
            gap: 8px;
        }

        .mode-btn {
            flex: 1;
            padding: 8px;
            border: 1px solid var(--primary-color);
            background-color: var(--dark-bg);
            color: var(--primary-color);
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
            font-size: 12px;
            transition: all 0.2s;
        }

        .mode-btn:hover {
            background-color: rgba(66, 134, 244, 0.1);
        }

        .mode-btn.active {
            background-color: var(--primary-color);
            color: white;
        }

        .status-text {
            color: var(--text-light);
            margin-bottom: 5px;
            font-size: 14px;
        }

        .trade-amount {
            color: var(--primary-color);
            font-weight: bold;
            font-size: 12px;
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
                opacity: 1;
            }
            50% {
                transform: scale(1.05);
                opacity: 0.8;
            }
            100% {
                transform: scale(1);
                opacity: 1;
            }
        }

        .pulse-animation {
            animation: pulse 1s ease-in-out infinite;
        }
    </style>
</head>
<body>
    <div class="floating-container" id="floatingContainer">
        <!-- Header with controls -->
        <div class="header">
            <h1><i class="fas fa-gem"></i> Quantum Edge</h1>
        </div>

        <!-- Main content area -->
        <div class="content" id="botContent">
            <div class="balance-display">
                <div class="balance-label">Balance:</div>
                <div class="balance-value">$0.00</div>
            </div>

            <div class="setting-group">
                <div class="setting-label">Risk Capital</div>
                <div class="setting-value">
                    <div class="suggestion">Suggest: $0.00</div>
                    <div class="risk-input-container">
                        <input type="number" id="riskCapitalInput" class="setting-input" value="0.00" min="1" step="1">
                    </div>
                </div>
            </div>

            <div class="setting-group">
                <div class="setting-label">Target Profit</div>
                <div class="setting-value">
                    <div class="suggestion">10%: $0.00</div>
                    <div class="risk-input-container">
                        <input type="number" id="targetProfitInput" class="setting-input" value="0.00" min="1" step="1">
                    </div>
                </div>
            </div>

            <div class="setting-group">
                <div class="setting-label">Trade Amount Mode</div>
                <div class="setting-value">
                    <div class="trade-mode-buttons">
                        <button id="fixedModeBtn" class="mode-btn">Fixed Amount</button>
                        <button id="autoModeBtn" class="mode-btn active">Auto (Martingale)</button>
                    </div>
                </div>
            </div>

            <div class="setting-group" id="fixedAmountGroup">
                <div class="setting-label">Fixed Amount</div>
                <div class="setting-value">
                    <div class="risk-input-container">
                        <input type="number" id="fixedAmountInput" class="setting-input" value="1.01" min="0.01" step="0.01">
                    </div>
                </div>
            </div>

            <div class="setting-group">
                <div class="setting-label">End Price</div>
                <div class="setting-value">
                    <div class="end-price" id="endPriceDisplay">$0.00</div>
                </div>
            </div>

            <div class="trading-controls">
                <button id="startStopBtn" class="action-btn"><i class="fas fa-play"></i> Start</button>
                <button id="pauseResumeBtn" class="action-btn" disabled><i class="fas fa-pause"></i> Pause</button>
            </div>

            <div class="trading-status">
                <div id="tradingStatus" class="status-text">Ready to start</div>
                <div id="currentTradeAmount" class="trade-amount">Next trade: $1.00</div>
            </div>
        </div>
    </div>

    <script src="quantum-edge.js"></script>
</body>
</html>
