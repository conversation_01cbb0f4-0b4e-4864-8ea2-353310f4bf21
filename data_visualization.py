"""
Data Visualization for Binary Options Trading Bot

This module handles visualization of trading data, statistics, and balance history.
"""

import logging
import matplotlib.pyplot as plt
from matplotlib.figure import Figure
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import numpy as np
from datetime import datetime, timedelta

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('data_visualization')

class DataVisualizer:
    """
    Data visualization system for the trading bot that provides charts and statistics.
    """
    
    def __init__(self, money_manager, trading_bot):
        """
        Initialize the data visualization system.
        
        Args:
            money_manager: Money management system instance
            trading_bot: Trading bot instance
        """
        self.money_manager = money_manager
        self.trading_bot = trading_bot
        
        # Matplotlib figure for balance chart
        self.balance_figure = None
        self.balance_canvas = None
        
        logger.info("Data visualization system initialized")
    
    def create_balance_chart(self, frame):
        """
        Create a balance history chart in the given frame.
        
        Args:
            frame: Tkinter frame to place the chart in
            
        Returns:
            FigureCanvasTkAgg: Canvas containing the chart
        """
        # Create figure and axis
        self.balance_figure = Figure(figsize=(8, 4), dpi=100)
        self.balance_figure.set_facecolor('#f0f0f0')
        
        # Add subplot
        balance_ax = self.balance_figure.add_subplot(111)
        balance_ax.set_title('Account Balance History')
        balance_ax.set_xlabel('Trades')
        balance_ax.set_ylabel('Balance')
        balance_ax.grid(True, linestyle='--', alpha=0.7)
        
        # Initial empty plot
        balance_ax.plot([], [], 'b-', label='Balance')
        balance_ax.axhline(y=self.money_manager.initial_balance, color='r', linestyle='--', 
                          label='Initial Balance')
        balance_ax.legend()
        
        # Create canvas
        self.balance_canvas = FigureCanvasTkAgg(self.balance_figure, master=frame)
        self.balance_canvas.draw()
        
        logger.info("Balance chart created")
        
        return self.balance_canvas
    
    def update_balance_chart(self):
        """
        Update the balance history chart with the latest data.
        """
        if self.balance_figure is None or self.balance_canvas is None:
            logger.warning("Cannot update balance chart: chart not created")
            return
        
        # Get trade history
        trades = self.trading_bot.get_trade_history()
        
        if not trades:
            logger.info("No trades to display in balance chart")
            return
        
        # Extract balance history
        balances = [self.money_manager.initial_balance] + [trade['balance'] for trade in trades]
        trade_indices = list(range(len(balances)))
        
        # Clear the figure and redraw
        balance_ax = self.balance_figure.axes[0]
        balance_ax.clear()
        
        # Plot balance history
        balance_ax.plot(trade_indices, balances, 'b-', marker='o', label='Balance')
        balance_ax.axhline(y=self.money_manager.initial_balance, color='r', linestyle='--', 
                          label='Initial Balance')
        
        # Set labels and grid
        balance_ax.set_title('Account Balance History')
        balance_ax.set_xlabel('Trades')
        balance_ax.set_ylabel('Balance')
        balance_ax.grid(True, linestyle='--', alpha=0.7)
        
        # Set y-axis limits with some padding
        min_balance = min(balances)
        max_balance = max(balances)
        padding = (max_balance - min_balance) * 0.1 if max_balance > min_balance else self.money_manager.initial_balance * 0.1
        balance_ax.set_ylim(min_balance - padding, max_balance + padding)
        
        # Add legend
        balance_ax.legend()
        
        # Redraw the canvas
        self.balance_canvas.draw()
        
        logger.info("Balance chart updated")
    
    def create_win_loss_chart(self, frame):
        """
        Create a win/loss pie chart in the given frame.
        
        Args:
            frame: Tkinter frame to place the chart in
            
        Returns:
            FigureCanvasTkAgg: Canvas containing the chart
        """
        # Create figure
        win_loss_figure = Figure(figsize=(4, 4), dpi=100)
        win_loss_figure.set_facecolor('#f0f0f0')
        
        # Add subplot
        win_loss_ax = win_loss_figure.add_subplot(111)
        win_loss_ax.set_title('Win/Loss Ratio')
        
        # Get statistics
        stats = self.money_manager.get_stats()
        wins = stats['winning_trades']
        losses = stats['losing_trades']
        
        # Create pie chart
        if wins + losses > 0:
            labels = ['Wins', 'Losses']
            sizes = [wins, losses]
            colors = ['#4CAF50', '#F44336']
            win_loss_ax.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', 
                           startangle=90, shadow=True)
        else:
            win_loss_ax.text(0.5, 0.5, 'No trades yet', horizontalalignment='center',
                            verticalalignment='center', transform=win_loss_ax.transAxes)
        
        win_loss_ax.axis('equal')  # Equal aspect ratio ensures that pie is drawn as a circle
        
        # Create canvas
        win_loss_canvas = FigureCanvasTkAgg(win_loss_figure, master=frame)
        win_loss_canvas.draw()
        
        logger.info("Win/loss chart created")
        
        return win_loss_canvas
    
    def get_statistics_text(self):
        """
        Get formatted text of current trading statistics.
        
        Returns:
            str: Formatted statistics text
        """
        stats = self.money_manager.get_stats()
        
        text = (
            f"Current Balance: ${stats['balance']:.2f}\n"
            f"Initial Balance: ${stats['initial_balance']:.2f}\n"
            f"Profit/Loss: {stats['profit_percentage']:.2f}%\n\n"
            f"Total Trades: {stats['total_trades']}\n"
            f"Winning Trades: {stats['winning_trades']}\n"
            f"Losing Trades: {stats['losing_trades']}\n"
            f"Win Rate: {stats['win_rate']:.2f}%\n\n"
            f"Consecutive Wins: {stats['consecutive_wins']}\n"
            f"Consecutive Losses: {stats['consecutive_losses']}\n"
        )
        
        return text
