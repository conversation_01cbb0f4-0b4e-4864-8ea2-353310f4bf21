import{S as Q,i as R,s as T,T as V,y as W,z as X,u as _,t as g,B as b,P as x,Q as z,D as y,G as B,c as v,R as I,e as D,f as m,V as F,W as G,X as H,H as S,h as A}from"./SpinnerAugment-BRymMBwV.js";import{n as k,g as q,a as C}from"./file-paths-BcSg4gks.js";const E=t=>({}),P=t=>({}),J=t=>({}),j=t=>({});function w(t){let a,c,n;return{c(){a=z("div"),c=z("div"),n=B(t[3]),v(c,"class","c-filespan__dir-text svelte-sprpft"),v(a,"class","c-filespan__dir svelte-sprpft")},m(e,$){D(e,a,$),m(a,c),m(c,n)},p(e,$){8&$&&S(n,e[3])},d(e){e&&A(a)}}}function K(t){let a,c,n,e,$,h,d,r;const u=t[7].leftIcon,l=x(u,t,t[8],j);let p=!t[2]&&w(t);const i=t[7].rightIcon,o=x(i,t,t[8],P);return{c(){a=z("div"),l&&l.c(),c=y(),n=z("span"),e=B(t[4]),$=y(),p&&p.c(),h=y(),o&&o.c(),v(n,"class","c-filespan__filename svelte-sprpft"),v(a,"class",d=I(`c-filespan ${t[0]}`)+" svelte-sprpft")},m(s,f){D(s,a,f),l&&l.m(a,null),m(a,c),m(a,n),m(n,e),m(a,$),p&&p.m(a,null),m(a,h),o&&o.m(a,null),r=!0},p(s,f){l&&l.p&&(!r||256&f)&&F(l,u,s,s[8],r?H(u,s[8],f,J):G(s[8]),j),(!r||16&f)&&S(e,s[4]),s[2]?p&&(p.d(1),p=null):p?p.p(s,f):(p=w(s),p.c(),p.m(a,h)),o&&o.p&&(!r||256&f)&&F(o,i,s,s[8],r?H(i,s[8],f,E):G(s[8]),P),(!r||1&f&&d!==(d=I(`c-filespan ${s[0]}`)+" svelte-sprpft"))&&v(a,"class",d)},i(s){r||(_(l,s),_(o,s),r=!0)},o(s){g(l,s),g(o,s),r=!1},d(s){s&&A(a),l&&l.d(s),p&&p.d(),o&&o.d(s)}}}function L(t){let a,c;return a=new V({props:{size:t[1],$$slots:{default:[K]},$$scope:{ctx:t}}}),{c(){W(a.$$.fragment)},m(n,e){X(a,n,e),c=!0},p(n,[e]){const $={};2&e&&($.size=n[1]),285&e&&($.$$scope={dirty:e,ctx:n}),a.$set($)},i(n){c||(_(a.$$.fragment,n),c=!0)},o(n){g(a.$$.fragment,n),c=!1},d(n){b(a,n)}}}function M(t,a,c){let n,e,$,{$$slots:h={},$$scope:d}=a,{class:r=""}=a,{filepath:u}=a,{size:l=1}=a,{nopath:p=!1}=a;return t.$$set=i=>{"class"in i&&c(0,r=i.class),"filepath"in i&&c(5,u=i.filepath),"size"in i&&c(1,l=i.size),"nopath"in i&&c(2,p=i.nopath),"$$scope"in i&&c(8,d=i.$$scope)},t.$$.update=()=>{32&t.$$.dirty&&c(6,n=k(u)),64&t.$$.dirty&&c(4,e=q(n)),64&t.$$.dirty&&c(3,$=C(n))},[r,l,p,$,e,u,n,h,d]}class U extends Q{constructor(a){super(),R(this,a,M,L,T,{class:0,filepath:5,size:1,nopath:2})}}export{U as F};
