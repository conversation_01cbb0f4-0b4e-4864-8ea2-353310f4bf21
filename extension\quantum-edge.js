/**
 * Quantum Edge - Binary Options Trading Bot
 *
 * This is a specialized version of the trading bot that focuses exclusively on
 * the Quantum Edge mode with martingale money management system.
 */

// Global variables for balance and trading
let balance = 0;
let initialBalance = 0;
let realBalanceReceived = false;

// Martingale sequence as specified by the user
const MARTINGALE_SEQUENCE = [
    1.01,   // 1st trade
    2.17,   // 2nd trade (after 1 loss)
    4.54,   // 3rd trade (after 2 losses)
    9.47,   // 4th trade (after 3 losses)
    19.76,  // 5th trade (after 4 losses)
    41.224, // 6th trade (after 5 losses)
    86.06,  // 7th trade (after 6 losses)
    179.61, // 8th trade (after 7 losses)
    179.61, // 9th trade (after 8 losses)
    374.89, // 10th trade (after 9 losses)
    782.12, // 11th trade (after 10 losses)
    1630.55, // 12th trade (after 11 losses)
    3400.49, // 13th trade (after 12 losses)
    7095.23  // 14th trade (after 13 losses)
];

// Martingale system variables
let currentTradeAmount = 1.01; // Default to first value in martingale sequence
let initialTradeAmount = 1.01; // Default to first value in martingale sequence
let consecutiveLosses = 0;
let riskCapital = 0;
let targetProfit = 0;
let profitLoss = 0;

// Trade amount mode (fixed or auto/martingale)
let tradeAmountMode = 'auto'; // Default to auto (martingale) mode
let fixedAmount = 1.01; // Default fixed amount (same as first martingale value)

// Trading state variables
let isTrading = false;
let isPaused = false;
let activeTrade = false;
let tradingInterval = null;

// Debug flag - set to true to enable console logging
const DEBUG = true;

// This duplicate sequence definition is removed

/**
 * Debug logging function - only logs if DEBUG is true
 */
function debugLog(...args) {
    if (DEBUG) {
        console.log('[QUANTUM EDGE]', ...args);
    }
}

/**
 * Initialize the interface
 */
function initializeInterface() {
    debugLog('Initializing Quantum Edge interface');

    // Set up event listeners
    document.getElementById('startStopBtn').addEventListener('click', toggleTrading);
    document.getElementById('pauseResumeBtn').addEventListener('click', togglePause);

    // Set up risk capital and target profit inputs
    const riskCapitalInput = document.getElementById('riskCapitalInput');
    const targetProfitInput = document.getElementById('targetProfitInput');

    // Calculate default risk capital and target profit
    riskCapital = balance * 0.5; // 50% of balance
    targetProfit = riskCapital * 0.1; // 10% of risk capital

    // Set initial values
    if (riskCapitalInput) {
        riskCapitalInput.value = riskCapital.toFixed(2);
        riskCapitalInput.addEventListener('change', updateRiskCapital);
    }

    if (targetProfitInput) {
        targetProfitInput.value = targetProfit.toFixed(2);
        targetProfitInput.addEventListener('change', updateTargetProfit);
    }

    // Set up trade amount mode buttons
    const fixedModeBtn = document.getElementById('fixedModeBtn');
    const autoModeBtn = document.getElementById('autoModeBtn');

    if (fixedModeBtn && autoModeBtn) {
        // Set initial active state based on current mode
        if (tradeAmountMode === 'fixed') {
            fixedModeBtn.classList.add('active');
            autoModeBtn.classList.remove('active');
        } else {
            fixedModeBtn.classList.remove('active');
            autoModeBtn.classList.add('active');
        }

        // Add event listeners
        fixedModeBtn.addEventListener('click', () => {
            setTradeAmountMode('fixed');
            fixedModeBtn.classList.add('active');
            autoModeBtn.classList.remove('active');
        });

        autoModeBtn.addEventListener('click', () => {
            setTradeAmountMode('auto');
            fixedModeBtn.classList.remove('active');
            autoModeBtn.classList.add('active');
        });
    }

    // Set up fixed amount input
    const fixedAmountInput = document.getElementById('fixedAmountInput');
    if (fixedAmountInput) {
        fixedAmountInput.value = fixedAmount.toFixed(2);
        fixedAmountInput.addEventListener('change', updateFixedAmount);
    }

    // Show/hide fixed amount input based on mode
    updateFixedAmountVisibility();

    // Update the end price display
    updateEndPriceDisplay();

    // Update the balance display
    updateBalanceDisplay();

    // Update the next trade amount display
    updateTradeAmountDisplay();

    debugLog('Quantum Edge interface initialized');
}

/**
 * Set the trade amount mode (fixed or auto)
 */
function setTradeAmountMode(mode) {
    if (mode !== 'fixed' && mode !== 'auto') {
        console.error('Invalid trade amount mode:', mode);
        return;
    }

    tradeAmountMode = mode;
    debugLog('Trade amount mode updated to:', tradeAmountMode);

    // Update visibility of fixed amount input
    updateFixedAmountVisibility();

    // Reset trade amount based on new mode
    if (tradeAmountMode === 'fixed') {
        currentTradeAmount = fixedAmount;
        initialTradeAmount = fixedAmount;
    } else {
        // For auto mode, start with the first value in the sequence (1.01)
        currentTradeAmount = MARTINGALE_SEQUENCE[0]; // 1.01
        initialTradeAmount = MARTINGALE_SEQUENCE[0]; // 1.01

        // Reset consecutive losses counter when switching to auto mode
        consecutiveLosses = 0;
    }

    // Update the display
    updateTradeAmountDisplay();

    // Show notification
    showNotification(`Trade amount mode set to: ${tradeAmountMode === 'fixed' ? 'Fixed Amount' : 'Auto (Martingale)'}`, 'info');

    // Save settings
    saveSettings();
}

/**
 * Update the fixed amount value
 */
function updateFixedAmount() {
    const fixedAmountInput = document.getElementById('fixedAmountInput');
    if (fixedAmountInput) {
        const newValue = parseFloat(fixedAmountInput.value);
        if (!isNaN(newValue) && newValue > 0) {
            fixedAmount = newValue;
            debugLog('Fixed amount updated to:', fixedAmount);

            // If in fixed mode, update the current trade amount
            if (tradeAmountMode === 'fixed') {
                currentTradeAmount = fixedAmount;
                initialTradeAmount = fixedAmount;
                updateTradeAmountDisplay();
            }

            // Save settings
            saveSettings();
        }
    }
}

/**
 * Show/hide fixed amount input based on mode
 */
function updateFixedAmountVisibility() {
    const fixedAmountGroup = document.getElementById('fixedAmountGroup');
    if (fixedAmountGroup) {
        if (tradeAmountMode === 'fixed') {
            fixedAmountGroup.style.display = 'block';
        } else {
            fixedAmountGroup.style.display = 'none';
        }
    }
}

/**
 * Update the risk capital value
 */
function updateRiskCapital() {
    const riskCapitalInput = document.getElementById('riskCapitalInput');
    const newValue = parseFloat(riskCapitalInput.value);

    if (!isNaN(newValue) && newValue > 0) {
        riskCapital = newValue;
        debugLog('Risk capital updated to:', riskCapital);
        showNotification(`Risk capital updated to $${riskCapital.toFixed(2)}`, 'info');

        // Update end price display if not trading
        if (!isTrading) {
            updateEndPriceDisplay();
        }

        saveSettings();
    }
}

/**
 * Update the target profit value
 */
function updateTargetProfit() {
    const targetProfitInput = document.getElementById('targetProfitInput');
    const newValue = parseFloat(targetProfitInput.value);

    if (!isNaN(newValue) && newValue > 0) {
        targetProfit = newValue;
        debugLog('Target profit updated to:', targetProfit);
        showNotification(`Target profit updated to $${targetProfit.toFixed(2)}`, 'info');

        // Update end price display if not trading
        if (!isTrading) {
            updateEndPriceDisplay();
        }

        saveSettings();
    }
}

/**
 * Update the end price display
 */
function updateEndPriceDisplay() {
    const endPriceDisplay = document.getElementById('endPriceDisplay');
    if (endPriceDisplay) {
        const targetEndBalance = balance + targetProfit;
        endPriceDisplay.textContent = `$${targetEndBalance.toFixed(2)}`;
    }
}

/**
 * Update the balance display
 */
function updateBalanceDisplay() {
    const balanceValue = document.querySelector('.balance-value');
    if (balanceValue) {
        balanceValue.textContent = `$${balance.toFixed(2)}`;
    }
}

/**
 * Update the next trade amount display
 */
function updateTradeAmountDisplay() {
    const currentTradeAmountElement = document.getElementById('currentTradeAmount');
    if (currentTradeAmountElement) {
        currentTradeAmountElement.textContent = `Next trade: $${currentTradeAmount.toFixed(2)}`;
    }
}

/**
 * Toggle trading state (start/stop)
 */
function toggleTrading() {
    const startStopBtn = document.getElementById('startStopBtn');
    const pauseResumeBtn = document.getElementById('pauseResumeBtn');
    const tradingStatus = document.getElementById('tradingStatus');
    const riskCapitalInput = document.getElementById('riskCapitalInput');
    const targetProfitInput = document.getElementById('targetProfitInput');

    if (!isTrading) {
        // Start trading
        isTrading = true;
        isPaused = false;
        startStopBtn.innerHTML = '<i class="fas fa-stop"></i> Stop';
        startStopBtn.classList.add('stop');
        pauseResumeBtn.disabled = false;
        tradingStatus.textContent = 'Trading active';

        // Disable inputs during trading
        if (riskCapitalInput) riskCapitalInput.disabled = true;
        if (targetProfitInput) targetProfitInput.disabled = true;

        // Reset trade amount to initial value
        currentTradeAmount = initialTradeAmount;
        updateTradeAmountDisplay();

        // Reset profit/loss tracking
        profitLoss = 0;
        consecutiveLosses = 0;

        // Update end price display
        updateEndPriceDisplay();

        // Show notification
        showNotification('Quantum Edge trading started', 'success');

        // Start trading cycle
        setTimeout(startTradingCycle, 1000);
    } else {
        // Stop trading
        isTrading = false;
        isPaused = false;
        startStopBtn.innerHTML = '<i class="fas fa-play"></i> Start';
        startStopBtn.classList.remove('stop');
        pauseResumeBtn.disabled = true;
        pauseResumeBtn.innerHTML = '<i class="fas fa-pause"></i> Pause';
        pauseResumeBtn.classList.remove('resume');
        tradingStatus.textContent = 'Trading stopped';

        // Re-enable inputs
        if (riskCapitalInput) riskCapitalInput.disabled = false;
        if (targetProfitInput) targetProfitInput.disabled = false;

        // Clear trading interval
        if (tradingInterval) {
            clearTimeout(tradingInterval);
            tradingInterval = null;
        }

        // Show notification
        showNotification('Quantum Edge trading stopped', 'info');
    }
}

/**
 * Toggle pause state
 */
function togglePause() {
    if (!isTrading) return;

    const pauseResumeBtn = document.getElementById('pauseResumeBtn');
    const tradingStatus = document.getElementById('tradingStatus');

    if (!isPaused) {
        // Pause trading
        isPaused = true;
        pauseResumeBtn.innerHTML = '<i class="fas fa-play"></i> Resume';
        pauseResumeBtn.classList.add('resume');
        tradingStatus.textContent = 'Trading paused';

        // Clear trading interval
        if (tradingInterval) {
            clearTimeout(tradingInterval);
            tradingInterval = null;
        }

        showNotification('Quantum Edge trading paused', 'info');
    } else {
        // Resume trading
        isPaused = false;
        pauseResumeBtn.innerHTML = '<i class="fas fa-pause"></i> Pause';
        pauseResumeBtn.classList.remove('resume');
        tradingStatus.textContent = 'Trading active';

        // Restart trading cycle
        startTradingCycle();

        showNotification('Quantum Edge trading resumed', 'success');
    }
}

/**
 * Start the trading cycle
 */
function startTradingCycle() {
    if (!isTrading || isPaused) {
        debugLog('Not trading or paused. isTrading =', isTrading, 'isPaused =', isPaused);
        return;
    }

    debugLog('Starting trading cycle. isTrading =', isTrading, 'isPaused =', isPaused);

    // Check if there's already an active trade
    if (activeTrade) {
        debugLog('Active trade in progress, cannot place new trade');
        showNotification('Active trade in progress, waiting for completion', 'info');

        // Schedule a retry after 1 second
        setTimeout(() => {
            debugLog('Retrying trading cycle after delay');
            startTradingCycle();
        }, 1000);
        return;
    }

    // Random direction (Buy or Sell)
    const direction = Math.random() < 0.5 ? 'BUY' : 'SELL';
    debugLog('Selected direction:', direction);

    // Place the trade
    debugLog('Placing trade with amount:', currentTradeAmount);

    try {
        // Update the trading status immediately
        const tradingStatus = document.getElementById('tradingStatus');
        if (tradingStatus) {
            tradingStatus.textContent = `Preparing ${direction} trade for $${currentTradeAmount.toFixed(2)}...`;
        }

        // Double-check we're still in trading mode
        if (!isTrading || isPaused) {
            debugLog('Trading stopped, aborting trade');
            return;
        }

        // Double-check there's no active trade
        if (activeTrade) {
            debugLog('Active trade detected, aborting new trade');
            return;
        }

        // Execute the trade
        placeTrade(direction);

        // Update the trading status
        if (tradingStatus) {
            tradingStatus.textContent = `Placed ${direction} trade for $${currentTradeAmount.toFixed(2)}`;
        }

    } catch (error) {
        debugLog('Error in startTradingCycle:', error);

        // Show error notification
        showNotification(`Error placing trade: ${error.message}`, 'error');

        // Schedule a retry after a short delay
        const retryDelay = 5000; // 5 seconds
        debugLog('Scheduling retry in 5 seconds due to error');

        if (tradingInterval) {
            clearTimeout(tradingInterval);
        }

        tradingInterval = setTimeout(startTradingCycle, retryDelay);
    }
}

/**
 * Place a trade
 */
function placeTrade(direction) {
    debugLog('placeTrade called with direction:', direction);

    if (activeTrade) {
        showNotification('Cannot place new trade, active trade in progress', 'error');
        debugLog('Cannot place trade - active trade in progress');
        return;
    }

    // Format single-digit amounts with .00 as requested by user
    let tradeAmount = currentTradeAmount;
    if (tradeAmount < 10) {
        // For single-digit amounts, especially 1, use .00 instead of .01
        if (Math.floor(tradeAmount) === tradeAmount) {
            // For whole numbers, use .00 format
            tradeAmount = parseFloat(tradeAmount.toFixed(2));
            debugLog('Formatted whole number amount to use .00:', tradeAmount);
        } else {
            // For non-whole numbers, just ensure 2 decimal places
            tradeAmount = parseFloat(tradeAmount.toFixed(2));
            debugLog('Formatted single-digit amount to:', tradeAmount);
        }
    }

    // Set activeTrade flag to prevent multiple trades
    activeTrade = true;

    // Log the trade details
    debugLog('Trade details:', {
        direction,
        amount: tradeAmount,
        mode: 'quantum'
    });

    // Create a more visible notification
    const notificationText = `Placing ${direction} trade for $${tradeAmount.toFixed(2)}`;
    showNotification(notificationText, 'info');

    // Send message to content script to execute trade
    window.parent.postMessage({
        action: 'executeTrade',
        direction: direction,
        amount: tradeAmount,
        expiry: 5, // 5 seconds for fast trading
        mode: 'quantum',
        timestamp: Date.now() // Add timestamp to ensure message uniqueness
    }, '*');
}

/**
 * Handle trade result
 */
function handleTradeResult(result) {
    if (!isTrading) return;

    const tradingStatus = document.getElementById('tradingStatus');
    const currentTradeAmountElement = document.getElementById('currentTradeAmount');
    const endPriceDisplay = document.getElementById('endPriceDisplay');

    // Log current state before processing result
    debugLog('Processing trade result:', result);
    debugLog('Current state - profitLoss:', profitLoss, 'currentTradeAmount:', currentTradeAmount);

    if (result === 'WIN') {
        // On win, reset to base amount
        consecutiveLosses = 0;

        // Calculate profit (92% payout)
        const profit = currentTradeAmount * 0.92;
        profitLoss += profit;

        // Reset to base amount after win based on the trade amount mode
        if (tradeAmountMode === 'fixed') {
            // In fixed mode, keep using the fixed amount
            currentTradeAmount = fixedAmount;
            initialTradeAmount = fixedAmount;
        } else {
            // In auto mode, reset to the first value in the martingale sequence (1.01)
            currentTradeAmount = MARTINGALE_SEQUENCE[0]; // 1.01
            initialTradeAmount = MARTINGALE_SEQUENCE[0]; // 1.01
        }

        // Format single-digit amounts with .00 as requested by user
        if (currentTradeAmount < 10) {
            // For single-digit amounts, especially 1, use .00 instead of .01
            if (Math.floor(currentTradeAmount) === currentTradeAmount) {
                // For whole numbers, use .00 format
                currentTradeAmount = parseFloat(currentTradeAmount.toFixed(2));
                debugLog('Formatted whole number amount to use .00:', currentTradeAmount);
            } else {
                // For non-whole numbers, just ensure 2 decimal places
                currentTradeAmount = parseFloat(currentTradeAmount.toFixed(2));
                debugLog('Formatted single-digit amount to:', currentTradeAmount);
            }
        }

        debugLog('WIN - Added profit:', profit, 'New profitLoss:', profitLoss, 'Current balance:', balance);

        if (tradingStatus) {
            tradingStatus.textContent = `WIN (+$${profit.toFixed(2)}) - Next: $${currentTradeAmount.toFixed(2)}`;
        }

        // Get the target end balance from the text content
        const targetEndBalance = parseFloat(endPriceDisplay?.textContent?.replace('$', '') || 0);

        if (balance >= targetEndBalance) {
            // Target profit reached, stop trading
            isTrading = false;
            if (tradingStatus) {
                tradingStatus.textContent = `Target profit reached: $${profitLoss.toFixed(2)}`;
            }

            const startStopBtn = document.getElementById('startStopBtn');
            const pauseResumeBtn = document.getElementById('pauseResumeBtn');
            const riskCapitalInput = document.getElementById('riskCapitalInput');
            const targetProfitInput = document.getElementById('targetProfitInput');

            if (startStopBtn) {
                startStopBtn.innerHTML = '<i class="fas fa-play"></i> Start';
                startStopBtn.classList.remove('stop');
            }

            if (pauseResumeBtn) {
                pauseResumeBtn.disabled = true;
            }

            // Re-enable the inputs
            if (riskCapitalInput) {
                riskCapitalInput.disabled = false;
            }
            if (targetProfitInput) {
                targetProfitInput.disabled = false;
            }

            if (tradingInterval) {
                clearTimeout(tradingInterval);
                tradingInterval = null;
            }

            // Show success notification
            showNotification(`Target profit reached: $${profitLoss.toFixed(2)}`, 'success');

            // Show congratulations popup
            showCongratulationsPopup(profitLoss);

            return;
        }
    } else {
        // On loss
        consecutiveLosses++;
        profitLoss -= currentTradeAmount;

        // Store the previous amount for logging
        const previousAmount = currentTradeAmount;

        if (tradeAmountMode === 'fixed') {
            // In fixed mode, keep using the fixed amount
            currentTradeAmount = fixedAmount;
        } else {
            // In auto mode, use the martingale sequence
            // Get the next amount from the sequence based on consecutive losses
            // If we've gone beyond the sequence, use the last value
            if (consecutiveLosses < MARTINGALE_SEQUENCE.length) {
                currentTradeAmount = MARTINGALE_SEQUENCE[consecutiveLosses];
            } else {
                // If we've gone beyond the sequence, use the last value
                currentTradeAmount = MARTINGALE_SEQUENCE[MARTINGALE_SEQUENCE.length - 1];
            }

            debugLog('Martingale sequence index:', consecutiveLosses, 'New trade amount:', currentTradeAmount);
        }

        // Format single-digit amounts with .00 as requested by user
        if (currentTradeAmount < 10) {
            // For single-digit amounts, especially 1, use .00 instead of .01
            if (Math.floor(currentTradeAmount) === currentTradeAmount) {
                // For whole numbers, use .00 format
                currentTradeAmount = parseFloat(currentTradeAmount.toFixed(2));
                debugLog('Formatted whole number amount to use .00:', currentTradeAmount);
            } else {
                // For non-whole numbers, just ensure 2 decimal places
                currentTradeAmount = parseFloat(currentTradeAmount.toFixed(2));
                debugLog('Formatted single-digit amount to:', currentTradeAmount);
            }
        }

        debugLog('LOSS - Subtracted:', previousAmount, 'New profitLoss:', profitLoss);
        debugLog('Consecutive losses:', consecutiveLosses, 'Next amount:', currentTradeAmount);

        // Check if we've lost more than the risk capital
        const totalLoss = -profitLoss;

        // Get the risk capital from the input field
        const riskCapitalInput = document.getElementById('riskCapitalInput');
        const fixedRiskCapital = parseFloat(riskCapitalInput.value);

        if (totalLoss >= fixedRiskCapital) {
            // Risk limit reached, stop trading
            isTrading = false;
            if (tradingStatus) {
                tradingStatus.textContent = `Risk limit reached. Loss: $${totalLoss.toFixed(2)}`;
            }

            const startStopBtn = document.getElementById('startStopBtn');
            const pauseResumeBtn = document.getElementById('pauseResumeBtn');
            const targetProfitInput = document.getElementById('targetProfitInput');

            if (startStopBtn) {
                startStopBtn.innerHTML = '<i class="fas fa-play"></i> Start';
                startStopBtn.classList.remove('stop');
            }

            if (pauseResumeBtn) {
                pauseResumeBtn.disabled = true;
            }

            // Re-enable the inputs
            if (riskCapitalInput) {
                riskCapitalInput.disabled = false;
            }
            if (targetProfitInput) {
                targetProfitInput.disabled = false;
            }

            if (tradingInterval) {
                clearTimeout(tradingInterval);
                tradingInterval = null;
            }

            showNotification(`⚠️ Risk limit reached. Loss: $${totalLoss.toFixed(2)}`, 'warning');
            return;
        }

        if (tradingStatus) {
            tradingStatus.textContent = `LOSS (-$${previousAmount.toFixed(2)}) - Next: $${currentTradeAmount.toFixed(2)}`;
        }
    }

    // Update the next trade amount display
    if (currentTradeAmountElement) {
        currentTradeAmountElement.textContent = `Next trade: $${currentTradeAmount.toFixed(2)}`;
    }

    // Reset active trade flag
    activeTrade = false;

    // Schedule the next trade with a delay
    if (isTrading && !isPaused) {
        const nextTradeDelay = 2000; // 2 seconds
        debugLog(`Scheduling next trade in ${nextTradeDelay/1000} seconds`);

        if (tradingInterval) {
            clearTimeout(tradingInterval);
        }

        tradingInterval = setTimeout(startTradingCycle, nextTradeDelay);
    }
}

/**
 * Show a congratulations popup when profit target is reached
 */
function showCongratulationsPopup(profit) {
    // Create a popup overlay
    const popupOverlay = document.createElement('div');
    popupOverlay.className = 'popup-overlay';
    popupOverlay.style.position = 'fixed';
    popupOverlay.style.top = '0';
    popupOverlay.style.left = '0';
    popupOverlay.style.width = '100%';
    popupOverlay.style.height = '100%';
    popupOverlay.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
    popupOverlay.style.zIndex = '10000000';
    popupOverlay.style.display = 'flex';
    popupOverlay.style.justifyContent = 'center';
    popupOverlay.style.alignItems = 'center';

    // Create the popup content
    const popupContent = document.createElement('div');
    popupContent.className = 'popup-content';
    popupContent.style.backgroundColor = 'var(--medium-bg)';
    popupContent.style.borderRadius = '10px';
    popupContent.style.padding = '20px';
    popupContent.style.boxShadow = '0 5px 20px rgba(0, 0, 0, 0.5)';
    popupContent.style.textAlign = 'center';
    popupContent.style.maxWidth = '80%';
    popupContent.style.border = '2px solid var(--success-color)';

    // Add congratulations message
    const title = document.createElement('h2');
    title.textContent = 'Congratulations!';
    title.style.color = 'var(--success-color)';
    title.style.marginBottom = '15px';

    const message = document.createElement('p');
    message.innerHTML = `You've reached your profit target!<br>Total profit: <strong>$${profit.toFixed(2)}</strong>`;
    message.style.color = 'var(--text-light)';
    message.style.marginBottom = '20px';

    // Add close button
    const closeButton = document.createElement('button');
    closeButton.textContent = 'Close';
    closeButton.style.padding = '8px 16px';
    closeButton.style.backgroundColor = 'var(--primary-color)';
    closeButton.style.color = 'white';
    closeButton.style.border = 'none';
    closeButton.style.borderRadius = '5px';
    closeButton.style.cursor = 'pointer';

    // Add event listener to close the popup
    closeButton.addEventListener('click', () => {
        document.body.removeChild(popupOverlay);
    });

    // Assemble the popup
    popupContent.appendChild(title);
    popupContent.appendChild(message);
    popupContent.appendChild(closeButton);
    popupOverlay.appendChild(popupContent);

    // Add the popup to the document
    document.body.appendChild(popupOverlay);

    // Also send a message to the parent to show the popup there
    window.parent.postMessage({
        action: 'showCongratulationsPopup',
        profit: profit
    }, '*');
}

/**
 * Show a notification
 */
function showNotification(message, type) {
    // Send message to content script to show notification
    window.parent.postMessage({
        action: 'showNotification',
        message,
        type
    }, '*');
}

/**
 * Save settings to storage
 */
function saveSettings() {
    // Send message to content script to save settings
    window.parent.postMessage({
        action: 'saveSettings',
        settings: {
            balance,
            initialBalance,
            currentMode: 'quantum',
            riskCapital,
            targetProfit,
            profitLoss,
            currentTradeAmount,
            initialTradeAmount,
            consecutiveLosses,
            tradeAmountMode,
            fixedAmount
        }
    }, '*');
}

/**
 * Update balance from real balance
 */
function updateBalanceFromReal(newBalance) {
    if (isNaN(newBalance) || newBalance === null || newBalance === undefined) {
        console.error('Invalid balance value in updateBalanceFromReal:', newBalance);
        return;
    }

    // Update the balance
    balance = newBalance;

    // If this is the first time we're getting a real balance, set the initial balance
    if (!realBalanceReceived) {
        initialBalance = newBalance;
        realBalanceReceived = true;
    }

    // Update the UI
    updateBalanceDisplay();

    // If not trading, update the risk capital and target profit
    if (!isTrading) {
        // Recalculate risk capital and target profit
        riskCapital = balance * 0.5; // 50% of balance
        targetProfit = riskCapital * 0.1; // 10% of risk capital

        // Update the UI
        const riskCapitalInput = document.getElementById('riskCapitalInput');
        const targetProfitInput = document.getElementById('targetProfitInput');

        if (riskCapitalInput) {
            riskCapitalInput.value = riskCapital.toFixed(2);
        }

        if (targetProfitInput) {
            targetProfitInput.value = targetProfit.toFixed(2);
        }

        // Update end price display
        updateEndPriceDisplay();
    }
}

// Listen for messages from the content script
window.addEventListener('message', function(event) {
    try {
        const message = event.data;

        // Skip if message is null or not an object
        if (!message || typeof message !== 'object') {
            return;
        }

        debugLog('Received message:', message);

        if (message.action === 'updateBalance') {
            // Update the balance
            updateBalanceFromReal(message.balance);
        }
        else if (message.action === 'tradeResult') {
            // Reset active trade flag
            activeTrade = false;

            // Handle the trade result
            handleTradeResult(message.result);
        }
        else if (message.action === 'loadSettings') {
            // Load settings from storage
            if (message.settings) {
                const s = message.settings;

                // Update balance
                if (s.balance) {
                    balance = s.balance;
                }

                // Update initial balance
                if (s.initialBalance) {
                    initialBalance = s.initialBalance;
                }

                // Update risk capital
                if (s.riskCapital) {
                    riskCapital = s.riskCapital;
                }

                // Update target profit
                if (s.targetProfit) {
                    targetProfit = s.targetProfit;
                }

                // Update profit/loss
                if (s.profitLoss !== undefined) {
                    profitLoss = s.profitLoss;
                }

                // Update current trade amount
                if (s.currentTradeAmount) {
                    currentTradeAmount = s.currentTradeAmount;
                }

                // Update initial trade amount
                if (s.initialTradeAmount) {
                    initialTradeAmount = s.initialTradeAmount;
                }

                // Update consecutive losses
                if (s.consecutiveLosses !== undefined) {
                    consecutiveLosses = s.consecutiveLosses;
                }

                // Update trade amount mode
                if (s.tradeAmountMode) {
                    tradeAmountMode = s.tradeAmountMode;

                    // Update the UI - set active button
                    const fixedModeBtn = document.getElementById('fixedModeBtn');
                    const autoModeBtn = document.getElementById('autoModeBtn');

                    if (fixedModeBtn && autoModeBtn) {
                        if (tradeAmountMode === 'fixed') {
                            fixedModeBtn.classList.add('active');
                            autoModeBtn.classList.remove('active');
                        } else {
                            fixedModeBtn.classList.remove('active');
                            autoModeBtn.classList.add('active');
                        }
                    }

                    // Update visibility of fixed amount input
                    updateFixedAmountVisibility();
                }

                // Update fixed amount
                if (s.fixedAmount) {
                    fixedAmount = s.fixedAmount;

                    // Update the UI
                    const fixedAmountInput = document.getElementById('fixedAmountInput');
                    if (fixedAmountInput) {
                        fixedAmountInput.value = fixedAmount.toFixed(2);
                    }
                }

                // Update UI
                updateBalanceDisplay();
                updateEndPriceDisplay();
                updateTradeAmountDisplay();

                debugLog('Settings loaded:', s);
            }
        }
    } catch (error) {
        console.error('Error processing message:', error);
    }
});

// Initialize the interface when the document is loaded
document.addEventListener('DOMContentLoaded', function() {
    debugLog('DOM content loaded, initializing interface');
    initializeInterface();
});