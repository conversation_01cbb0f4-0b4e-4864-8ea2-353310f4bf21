:root {
    --primary-color: #4286f4;
    --secondary-color: #ff6384;
    --success-color: #10b981;
    --danger-color: #ef4444;
    --warning-color: #f59e0b;
    --dark-bg: #0f0f1a;
    --medium-bg: #1a1a2e;
    --light-bg: #28293d;
    --text-light: #ffffff;
    --text-medium: #d1d5db;
    --text-dark: #9ca3af;
    --border-color: rgba(66, 134, 244, 0.2);
    --pocket-option-blue: #0056b3;
    --pocket-option-dark: #1e2a3b;
    --pocket-option-light: #2c3e50;
}

body {
    font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0;
    padding: 0;
    background: linear-gradient(135deg, var(--pocket-option-dark) 0%, var(--dark-bg) 100%);
    color: var(--text-light);
    width: 500px;
    max-height: 600px;
    overflow-y: auto;
}

.container {
    padding: 15px;
}

header {
    text-align: center;
    margin-bottom: 20px;
    padding: 15px;
    background-color: var(--pocket-option-dark);
    border-bottom: 2px solid var(--primary-color);
}

h1 {
    margin: 0;
    font-size: 22px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
}

h1 i {
    margin-right: 10px;
    color: var(--primary-color);
}

.subtitle {
    margin-top: 5px;
    color: var(--text-medium);
    font-size: 14px;
}

.connection-panel {
    background-color: var(--pocket-option-light);
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border: 1px solid var(--border-color);
}

.status-indicator {
    color: var(--text-medium);
    font-size: 16px;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.status-indicator.connected {
    color: var(--success-color);
}

.status-indicator i {
    font-size: 18px;
}

.connect-btn {
    background-color: var(--pocket-option-blue);
    color: white;
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s;
    font-size: 16px;
    border: none;
    width: 100%;
    max-width: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.connect-btn:hover {
    background-color: #0069d9;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.mode-selection {
    margin-bottom: 20px;
}

.mode-selection h2 {
    font-size: 18px;
    margin-bottom: 15px;
    color: var(--text-light);
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 8px;
}

.mode-card {
    background-color: var(--pocket-option-light);
    border-radius: 8px;
    margin-bottom: 15px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border: 1px solid var(--border-color);
    transition: all 0.3s;
}

.mode-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
}

.mode-header {
    background-color: var(--pocket-option-dark);
    padding: 12px 15px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.mode-header i {
    font-size: 20px;
    color: var(--secondary-color);
}

.mode-header h3 {
    margin: 0;
    font-size: 16px;
    color: var(--text-light);
}

.mode-content {
    padding: 15px;
}

.win-rate {
    display: inline-block;
    background-color: var(--success-color);
    color: white;
    padding: 4px 10px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 10px;
}

.mode-content p {
    margin: 0;
    color: var(--text-medium);
    font-size: 14px;
    line-height: 1.5;
}

.info-panel {
    background-color: var(--pocket-option-light);
    border-radius: 8px;
    padding: 15px;
    display: flex;
    flex-direction: column;
    gap: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border: 1px solid var(--border-color);
}

.info-item {
    display: flex;
    align-items: center;
    gap: 10px;
}

.info-item i {
    font-size: 20px;
    color: var(--primary-color);
}

.info-item p {
    margin: 0;
    color: var(--text-medium);
    font-size: 14px;
}

.debug-container {
    display: flex;
    justify-content: center;
    margin-top: 10px;
}

.debug-btn {
    background-color: var(--success-color);
    color: white;
    padding: 12px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s;
    font-size: 15px;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    width: 100%;
    max-width: 300px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.debug-btn:hover {
    background-color: #0ca876;
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
}

button i {
    margin-right: 5px;
}

.toast-notification {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%) translateY(100%);
    background-color: var(--pocket-option-dark);
    color: var(--text-light);
    padding: 12px 20px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    gap: 12px;
    z-index: 9999;
    opacity: 0;
    transition: transform 0.3s ease, opacity 0.3s ease;
    min-width: 300px;
    max-width: 90%;
    border-left: 4px solid var(--primary-color);
}

.toast-notification.success {
    border-left-color: var(--success-color);
}

.toast-notification.error {
    border-left-color: var(--danger-color);
}

.toast-notification.warning {
    border-left-color: var(--warning-color);
}

.toast-icon {
    font-size: 20px;
}

.toast-notification.success .toast-icon {
    color: var(--success-color);
}

.toast-notification.error .toast-icon {
    color: var(--danger-color);
}

.toast-notification.info .toast-icon {
    color: var(--primary-color);
}

.toast-notification.warning .toast-icon {
    color: var(--warning-color);
}

.toast-message {
    font-size: 14px;
    flex: 1;
}

footer {
    text-align: center;
    margin-top: 20px;
    padding: 10px;
    color: var(--text-dark);
    font-size: 12px;
    border-top: 1px solid var(--border-color);
}
