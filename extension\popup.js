// Initialize variables
let balance = 1000;
let initialBalance = 1000;
let riskPerTrade = 0.02;
let strategy = 'kelly';
let autoInterval = 10;
let isAutoTrading = false;
let activeTrade = false;
let tradeTimeout;
let currentMode = 'quantum'; // Default mode: quantum edge
let payoutPercentage = 85; // Default payout percentage
let currentCurrencyPair = 'EUR/USD';
let isConnected = false;
let currentTrade = null;

// Trading statistics
let totalTrades = 0;
let winningTrades = 0;
let losingTrades = 0;
let consecutiveWins = 0;
let consecutiveLosses = 0;

// Trade history
let tradeHistory = [];

// DOM elements
const balanceElement = document.getElementById('balance');
const balanceChangeElement = document.getElementById('balance-change');
const riskSlider = document.getElementById('riskPerTrade');
const riskValue = document.getElementById('riskValue');
const strategySelect = document.getElementById('strategy');
const expirySelect = document.getElementById('expiryTime');
const intervalSlider = document.getElementById('autoInterval');
const intervalValue = document.getElementById('intervalValue');
const placeTradeBtn = document.getElementById('placeTrade');
const autoTradingBtn = document.getElementById('autoTrading');
const tradeInfoElement = document.getElementById('tradeInfo');
const notificationsElement = document.getElementById('notificationsList');
const winRateElement = document.getElementById('winRate');
const profitLossElement = document.getElementById('profitLoss');
const totalTradesElement = document.getElementById('totalTrades');
const consecutiveWinsElement = document.getElementById('consecutiveWins');
const connectionStatusElement = document.getElementById('connection-status');
const connectBtn = document.getElementById('connectBtn');
const floatingModeBtn = document.getElementById('floatingModeBtn');
const debugBtn = document.getElementById('debugBtn');
const neuralPulseBtn = document.getElementById('neuralPulseBtn');

// Mode cards
const quantumModeCard = document.getElementById('mode-quantum');
const phoenixModeCard = document.getElementById('mode-phoenix');
const neuralModeCard = document.getElementById('mode-neural');

// Event listeners - only for elements that exist in our simplified interface
connectBtn.addEventListener('click', connectToPocketOption);
debugBtn.addEventListener('click', debugPocketOption);
neuralPulseBtn.addEventListener('click', testNeuralPulse);

// Mode selection event listeners
quantumModeCard.addEventListener('click', () => setMode('quantum'));
phoenixModeCard.addEventListener('click', () => setMode('phoenix'));
neuralModeCard.addEventListener('click', () => setMode('neural'));

// Functions
function setMode(mode) {
    currentMode = mode;

    // Update UI - add border to selected mode
    quantumModeCard.style.borderColor = mode === 'quantum' ? '#ff6384' : 'rgba(66, 134, 244, 0.2)';
    phoenixModeCard.style.borderColor = mode === 'phoenix' ? '#ff6384' : 'rgba(66, 134, 244, 0.2)';
    neuralModeCard.style.borderColor = mode === 'neural' ? '#ff6384' : 'rgba(66, 134, 244, 0.2)';

    // Add shadow to selected mode
    quantumModeCard.style.boxShadow = mode === 'quantum' ? '0 0 15px rgba(255, 99, 132, 0.5)' : '0 4px 6px rgba(0, 0, 0, 0.1)';
    phoenixModeCard.style.boxShadow = mode === 'phoenix' ? '0 0 15px rgba(255, 99, 132, 0.5)' : '0 4px 6px rgba(0, 0, 0, 0.1)';
    neuralModeCard.style.boxShadow = mode === 'neural' ? '0 0 15px rgba(255, 99, 132, 0.5)' : '0 4px 6px rgba(0, 0, 0, 0.1)';

    addNotification(`Selected ${mode.toUpperCase()} mode - KOS Bot`, 'info');
    saveSettings();
}

function updateExpiryOptions(times) {
    // Clear existing options
    expirySelect.innerHTML = '';

    // Add new options
    times.forEach(time => {
        const option = document.createElement('option');
        option.value = time;

        if (time < 60) {
            option.textContent = `${time} seconds`;
        } else if (time < 3600) {
            option.textContent = `${time / 60} minute${time === 60 ? '' : 's'}`;
        } else {
            option.textContent = `${time / 3600} hour${time === 3600 ? '' : 's'}`;
        }

        expirySelect.appendChild(option);
    });
}

function placeTrade() {
    if (activeTrade) {
        addNotification('Cannot place new trade, active trade in progress', 'info');
        return;
    }

    if (!isConnected) {
        addNotification('Please connect to Pocket Option first', 'error');
        return;
    }

    // Calculate trade amount based on strategy and mode
    const tradeAmount = calculatePositionSize();

    // Generate direction (buy/sell)
    const direction = determineTradeDirection();

    // Get expiry time from select
    const expiry = parseInt(expirySelect.value);

    // Set active trade
    activeTrade = true;
    const startTime = new Date();
    const endTime = new Date(startTime.getTime() + expiry * 1000);

    // Store current trade details
    currentTrade = {
        direction,
        amount: tradeAmount,
        expiry,
        startTime,
        endTime
    };

    // Update trade info
    updateTradeInfo(direction, tradeAmount, expiry, startTime, endTime);

    // Add notification
    addNotification(`New trade: ${currentCurrencyPair} - ${direction} for $${tradeAmount.toFixed(2)} with ${formatTime(expiry)} expiry`, 'info');

    // Execute trade on Pocket Option
    executeTrade(direction, tradeAmount, expiry);

    // If auto trading, set timeout for automatic result detection
    if (isAutoTrading) {
        tradeTimeout = setTimeout(() => {
            // This will be replaced by actual trade outcome detection
            const winRate = getWinRateForCurrentMode();
            const outcome = Math.random() < winRate;
            handleTradeResult(outcome ? 'WIN' : 'LOSS');
        }, expiry * 1000);
    }
}

function executeTrade(direction, amount, expiry) {
    // Send message to content script to execute trade on Pocket Option
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
        chrome.tabs.sendMessage(tabs[0].id, {
            action: "executeTrade",
            data: {
                direction,
                amount,
                expiry,
                mode: currentMode
            }
        });
    });
}

function formatTime(seconds) {
    if (seconds < 60) {
        return `${seconds}s`;
    } else if (seconds < 3600) {
        return `${seconds / 60}m`;
    } else {
        return `${seconds / 3600}h`;
    }
}

function getWinRateForCurrentMode() {
    switch (currentMode) {
        case 'quantum': return 0.65;  // 65% win rate
        case 'phoenix': return 0.75;  // 75% win rate
        case 'neural': return 0.90;   // 90% win rate
        default: return 0.55;
    }
}

function determineTradeDirection() {
    // In a real system, this would use actual analysis
    // For now, we'll use random with slight bias based on mode

    if (currentMode === 'neural') {
        // Neural mode has higher accuracy
        return Math.random() < 0.55 ? 'BUY' : 'SELL';
    } else {
        return Math.random() < 0.5 ? 'BUY' : 'SELL';
    }
}

function calculatePositionSize() {
    let positionSize;

    if (currentMode === 'phoenix') {
        // Masaniello system
        const baseSize = balance * riskPerTrade;

        if (consecutiveLosses > 0) {
            // Increase size after losses to recover
            positionSize = baseSize * (1 + (consecutiveLosses * 0.5));
        } else {
            positionSize = baseSize;
        }
    }
    else if (currentMode === 'neural') {
        // AI mode - more aggressive due to higher win rate
        positionSize = balance * (riskPerTrade * 1.5);
    }
    else {
        // Quantum mode - uses selected strategy
        if (strategy === 'kelly') {
            // Modified Kelly Criterion
            const winRate = getWinRateForCurrentMode();
            const payoutRatio = payoutPercentage / 100;

            const p = winRate;
            const b = payoutRatio;

            // Kelly percentage
            const kellyPercentage = (p * (1 + b) - 1) / b;

            // Apply safety factor (1/4 Kelly)
            const safeKelly = kellyPercentage * 0.25;

            // Cap at maximum risk per trade
            positionSize = Math.min(safeKelly, riskPerTrade) * balance;
        }
        else if (strategy === 'anti-martingale') {
            // Anti-martingale approach
            const baseSize = balance * riskPerTrade;

            if (consecutiveWins > 0) {
                // Increase by 50% for each consecutive win, up to 3 wins
                const multiplier = Math.min(1 + (0.5 * consecutiveWins), 2.5);
                positionSize = baseSize * multiplier;
            }
            else if (consecutiveLosses > 0) {
                // Decrease by 30% for each consecutive loss
                const multiplier = Math.max(1 - (0.3 * consecutiveLosses), 0.4);
                positionSize = baseSize * multiplier;
            }
            else {
                positionSize = baseSize;
            }
        }
        else {
            // Fixed percentage
            positionSize = balance * riskPerTrade;
        }
    }

    return Math.round(positionSize * 100) / 100; // Round to 2 decimal places
}

function handleTradeResult(result) {
    if (!activeTrade) return;

    // Clear any pending timeout
    if (tradeTimeout) {
        clearTimeout(tradeTimeout);
        tradeTimeout = null;
    }

    const tradeAmount = currentTrade.amount;
    const direction = currentTrade.direction;

    // Update balance
    if (result === 'WIN') {
        const profit = tradeAmount * (payoutPercentage / 100);
        balance += profit;
        winningTrades++;
        consecutiveWins++;
        consecutiveLosses = 0;
        addNotification(`Trade ${result}: ${currentCurrencyPair} - ${direction} +$${profit.toFixed(2)} | Balance: $${balance.toFixed(2)}`, 'success');
    } else {
        balance -= tradeAmount;
        losingTrades++;
        consecutiveLosses++;
        consecutiveWins = 0;
        addNotification(`Trade ${result}: ${currentCurrencyPair} - ${direction} -$${tradeAmount.toFixed(2)} | Balance: $${balance.toFixed(2)}`, 'error');
    }

    totalTrades++;

    // Update trade history
    tradeHistory.push({
        direction,
        amount: tradeAmount,
        result,
        balance
    });

    // Reset active trade
    activeTrade = false;
    currentTrade = null;

    // Update UI
    updateBalance();
    updateTradeInfo();
    updateStatistics();
    saveSettings();

    // Continue auto trading if enabled
    if (isAutoTrading) {
        setTimeout(() => {
            if (!activeTrade) {
                placeTrade();
            }
        }, autoInterval * 1000);
    }
}

function toggleAutoTrading() {
    isAutoTrading = !isAutoTrading;

    if (isAutoTrading) {
        autoTradingBtn.innerHTML = '<i class="fas fa-stop-circle"></i> Stop Auto Trading';
        autoTradingBtn.classList.remove('secondary-btn');
        autoTradingBtn.classList.add('primary-btn');

        // Start first trade immediately
        if (!activeTrade && isConnected) {
            placeTrade();
        } else if (!isConnected) {
            addNotification('Please connect to Pocket Option first', 'error');
            isAutoTrading = false;
            return;
        }

        addNotification(`Auto trading started with ${autoInterval}s interval`, 'info');
    } else {
        autoTradingBtn.innerHTML = '<i class="fas fa-robot"></i> Start Auto Trading';
        autoTradingBtn.classList.remove('primary-btn');
        autoTradingBtn.classList.add('secondary-btn');

        // Clear auto trading interval
        if (tradeTimeout) {
            clearTimeout(tradeTimeout);
            tradeTimeout = null;
        }

        addNotification('Auto trading stopped', 'info');
    }

    saveSettings();
}

function updateBalance() {
    balanceElement.textContent = `Balance: $${balance.toFixed(2)}`;

    // Update balance change percentage
    const changePercentage = ((balance - initialBalance) / initialBalance) * 100;
    balanceChangeElement.textContent = `${changePercentage >= 0 ? '+' : ''}${changePercentage.toFixed(2)}%`;

    if (changePercentage >= 0) {
        balanceChangeElement.classList.remove('negative');
    } else {
        balanceChangeElement.classList.add('negative');
    }
}

function updateTradeInfo(direction, amount, expiry, startTime, endTime) {
    if (!activeTrade) {
        tradeInfoElement.textContent = 'No active trade';
        return;
    }

    // Calculate remaining time
    const now = new Date();
    const remaining = Math.max(0, Math.round((endTime - now) / 1000));

    // Calculate potential profit
    const potentialProfit = amount * (payoutPercentage / 100);

    tradeInfoElement.innerHTML = `
        <p><strong>Asset:</strong> ${currentCurrencyPair}</p>
        <p><strong>Direction:</strong> <span style="color: ${direction === 'BUY' ? '#10b981' : '#ef4444'}">${direction}</span></p>
        <p><strong>Amount:</strong> $${amount.toFixed(2)}</p>
        <p><strong>Potential Profit:</strong> $${potentialProfit.toFixed(2)}</p>
        <p><strong>Time Remaining:</strong> ${formatTime(remaining)}</p>
    `;

    // Update remaining time every second
    if (remaining > 0) {
        setTimeout(() => {
            if (activeTrade) {
                updateTradeInfo(direction, amount, expiry, startTime, endTime);
            }
        }, 1000);
    }
}

function addNotification(message, type) {
    // Create a toast notification that appears at the bottom of the popup
    const toast = document.createElement('div');
    toast.className = `toast-notification ${type}`;
    toast.innerHTML = `
        <div class="toast-icon">
            <i class="${type === 'success' ? 'fas fa-check-circle' :
                      type === 'error' ? 'fas fa-exclamation-circle' :
                      'fas fa-info-circle'}"></i>
        </div>
        <div class="toast-message">${message}</div>
    `;

    document.body.appendChild(toast);

    // Animate in
    setTimeout(() => {
        toast.style.transform = 'translateY(0)';
        toast.style.opacity = '1';
    }, 10);

    // Remove after 3 seconds
    setTimeout(() => {
        toast.style.transform = 'translateY(100%)';
        toast.style.opacity = '0';

        // Remove from DOM after animation completes
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }, 3000);

    // Also add to the notifications list if it exists
    if (notificationsElement) {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;

        const timestamp = new Date().toLocaleTimeString();
        notification.textContent = `[${timestamp}] ${message}`;

        notificationsElement.prepend(notification);

        // Limit to 5 notifications
        if (notificationsElement.children.length > 5) {
            notificationsElement.removeChild(notificationsElement.lastChild);
        }
    }
}

function updateStatistics() {
    // Calculate win rate
    const winRate = totalTrades > 0 ? (winningTrades / totalTrades) * 100 : 0;

    // Calculate profit/loss percentage
    const profitLossPercentage = ((balance - initialBalance) / initialBalance) * 100;

    // Update UI
    winRateElement.textContent = `${winRate.toFixed(1)}%`;
    profitLossElement.textContent = `${profitLossPercentage.toFixed(1)}%`;
    profitLossElement.className = 'stat-value ' + (profitLossPercentage >= 0 ? 'win' : 'loss');
    totalTradesElement.textContent = totalTrades;
    consecutiveWinsElement.textContent = consecutiveWins;
}

function connectToPocketOption() {
    // Update UI to show connecting state
    connectBtn.textContent = 'Connecting...';
    connectBtn.disabled = true;

    // Check if we're on the Pocket Option site
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
        if (!tabs || tabs.length === 0) {
            // No active tab found
            connectBtn.textContent = 'Connect to Pocket Option';
            connectBtn.disabled = false;
            addNotification('Error: No active tab found', 'error');
            return;
        }

        const currentUrl = tabs[0].url;

        if (currentUrl.includes('pocketoption.com')) {
            // We're already on Pocket Option, just connect
            try {
                chrome.tabs.sendMessage(tabs[0].id, {action: 'connect'}, function(response) {
                    // Check for error
                    if (chrome.runtime.lastError) {
                        console.error('Error sending message:', chrome.runtime.lastError);
                        connectBtn.textContent = 'Connect to Pocket Option';
                        connectBtn.disabled = false;
                        addNotification('Error connecting: Content script not loaded. Please refresh the page and try again.', 'error');
                        return;
                    }

                    if (response && response.success) {
                        // Connection successful
                        isConnected = true;
                        connectionStatusElement.innerHTML = '<i class="fas fa-plug"></i> <span>Connected to Pocket Option</span>';
                        connectionStatusElement.classList.add('connected');
                        connectBtn.textContent = 'Connected';
                        connectBtn.style.backgroundColor = '#10b981';
                        connectBtn.disabled = true;

                        // Update balance if available
                        if (response.balance) {
                            balance = response.balance;
                            initialBalance = response.balance;
                        }

                        addNotification('Connected to Pocket Option successfully', 'success');

                        // Automatically open floating interface
                        setTimeout(() => {
                            try {
                                chrome.tabs.sendMessage(tabs[0].id, {action: 'openFloatingInterface'}, function(floatingResponse) {
                                    // Check for error
                                    if (chrome.runtime.lastError) {
                                        console.error('Error opening floating interface:', chrome.runtime.lastError);
                                        addNotification('Error opening floating interface. Please try again.', 'error');
                                        return;
                                    }

                                    // Close the popup window
                                    window.close();
                                });
                            } catch (error) {
                                console.error('Error sending openFloatingInterface message:', error);
                                addNotification('Error opening floating interface', 'error');
                            }
                        }, 1000);
                    } else {
                        // Connection failed
                        connectBtn.textContent = 'Connect to Pocket Option';
                        connectBtn.disabled = false;
                        addNotification('Failed to connect to Pocket Option', 'error');
                    }
                });
            } catch (error) {
                console.error('Error sending connect message:', error);
                connectBtn.textContent = 'Connect to Pocket Option';
                connectBtn.disabled = false;
                addNotification('Error connecting to Pocket Option: ' + error.message, 'error');
            }
        } else {
            // Set flag that we're navigating from the extension
            try {
                chrome.storage.local.set({navigatingFromExtension: true}, function() {
                    if (chrome.runtime.lastError) {
                        console.error('Error setting navigatingFromExtension flag:', chrome.runtime.lastError);
                        addNotification('Error setting navigation flag', 'error');
                        return;
                    }

                    // Navigate to Pocket Option
                    try {
                        chrome.tabs.update({url: 'https://pocketoption.com/en/cabinet/demo-quick-high-low/'});

                        // Show instructions
                        addNotification('Navigating to Pocket Option. Please wait...', 'info');

                        // Close the popup after a short delay
                        setTimeout(() => {
                            window.close();
                        }, 2000);
                    } catch (error) {
                        console.error('Error navigating to Pocket Option:', error);
                        connectBtn.textContent = 'Connect to Pocket Option';
                        connectBtn.disabled = false;
                        addNotification('Error navigating to Pocket Option: ' + error.message, 'error');
                    }
                });
            } catch (error) {
                console.error('Error setting storage:', error);
                connectBtn.textContent = 'Connect to Pocket Option';
                connectBtn.disabled = false;
                addNotification('Error setting storage: ' + error.message, 'error');
            }
        }
    });
}

function openFloatingMode() {
    // Check if we're on the Pocket Option site
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
        if (!tabs || tabs.length === 0) {
            // No active tab found
            addNotification('Error: No active tab found', 'error');
            return;
        }

        const currentUrl = tabs[0].url;

        if (currentUrl.includes('pocketoption.com')) {
            // Send message to content script to open floating interface
            try {
                chrome.tabs.sendMessage(tabs[0].id, {action: 'openFloatingInterface'}, function(response) {
                    // Check for error
                    if (chrome.runtime.lastError) {
                        console.error('Error opening floating interface:', chrome.runtime.lastError);
                        addNotification('Error: Content script not loaded. Please refresh the page and try again.', 'error');
                        return;
                    }

                    if (response && response.success) {
                        addNotification('Floating mode activated', 'success');
                        // Close the popup
                        window.close();
                    } else {
                        addNotification('Failed to activate floating mode', 'error');
                    }
                });
            } catch (error) {
                console.error('Error sending openFloatingInterface message:', error);
                addNotification('Error activating floating mode: ' + error.message, 'error');
            }
        } else {
            // Navigate to Pocket Option first
            addNotification('Please connect to Pocket Option first', 'warning');
            connectToPocketOption();
        }
    });
}

function debugPocketOption() {
    // Check if we're on the Pocket Option site
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
        if (!tabs || tabs.length === 0) {
            // No active tab found
            addNotification('Error: No active tab found', 'error');
            return;
        }

        const currentUrl = tabs[0].url;

        if (currentUrl.includes('pocketoption.com')) {
            // Send message to content script to run debug function
            try {
                chrome.tabs.sendMessage(tabs[0].id, {action: 'debugPocketOption'}, function(response) {
                    // Check for error
                    if (chrome.runtime.lastError) {
                        console.error('Error sending debug message:', chrome.runtime.lastError);

                        // If the message fails, try injecting the content script manually
                        try {
                            chrome.scripting.executeScript({
                                target: { tabId: tabs[0].id },
                                function: () => {
                                    console.log('Executing debug script directly');

                                    // Create a simple floating element to test DOM manipulation
                                    const testElement = document.createElement('div');
                                    testElement.id = 'botTestElement';
                                    testElement.style.position = 'fixed';
                                    testElement.style.top = '10px';
                                    testElement.style.left = '10px';
                                    testElement.style.width = '100px';
                                    testElement.style.height = '100px';
                                    testElement.style.backgroundColor = 'red';
                                    testElement.style.zIndex = '999999';
                                    testElement.style.borderRadius = '50%';
                                    testElement.textContent = 'Test';
                                    testElement.style.color = 'white';
                                    testElement.style.display = 'flex';
                                    testElement.style.alignItems = 'center';
                                    testElement.style.justifyContent = 'center';

                                    document.body.appendChild(testElement);

                                    // Remove after 5 seconds
                                    setTimeout(() => {
                                        if (testElement.parentNode) {
                                            testElement.parentNode.removeChild(testElement);
                                        }
                                    }, 5000);

                                    return true;
                                }
                            }).then(results => {
                                console.log('Debug script executed:', results);
                                addNotification('Direct debug script executed', 'success');
                            }).catch(error => {
                                console.error('Error executing debug script:', error);
                                addNotification('Error executing debug script: ' + error.message, 'error');
                            });
                        } catch (scriptError) {
                            console.error('Error executing script:', scriptError);
                            addNotification('Error executing script: ' + scriptError.message, 'error');
                        }
                        return;
                    }

                    if (response && response.success) {
                        addNotification('Debug function executed - check console', 'success');
                    } else {
                        addNotification('Debug function failed', 'error');
                    }
                });
            } catch (error) {
                console.error('Error sending debug message:', error);
                addNotification('Error debugging: ' + error.message, 'error');
            }
        } else {
            // Navigate to Pocket Option first
            addNotification('Please connect to Pocket Option first', 'warning');
            connectToPocketOption();
        }
    });
}

function establishConnection() {
    // Send message to content script to establish connection
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
        if (!tabs || tabs.length === 0) {
            // No active tab found
            addNotification('Error: No active tab found', 'error');
            return;
        }

        try {
            chrome.tabs.sendMessage(tabs[0].id, {action: "connect"}, function(response) {
                // Check for error
                if (chrome.runtime.lastError) {
                    console.error('Error establishing connection:', chrome.runtime.lastError);
                    connectBtn.textContent = 'Connect to Pocket Option';
                    connectBtn.disabled = false;
                    addNotification('Error connecting: Content script not loaded. Please refresh the page and try again.', 'error');
                    return;
                }

                if (response && response.success) {
                    isConnected = true;
                    connectionStatusElement.innerHTML = '<i class="fas fa-plug"></i> <span>Connected to Pocket Option</span>';
                    connectionStatusElement.classList.add('connected');
                    connectBtn.textContent = 'Connected';
                    connectBtn.style.backgroundColor = '#10b981';
                    connectBtn.disabled = true;

                    // Update balance from Pocket Option if available
                    if (response.balance) {
                        balance = response.balance;
                        initialBalance = response.balance;
                        updateBalance();
                    }

                    addNotification('Connected to Pocket Option successfully', 'success');
                } else {
                    connectBtn.textContent = 'Connect to Pocket Option';
                    connectBtn.disabled = false;
                    addNotification('Failed to connect to Pocket Option', 'error');
                }
            });
        } catch (error) {
            console.error('Error sending connect message:', error);
            connectBtn.textContent = 'Connect to Pocket Option';
            connectBtn.disabled = false;
            addNotification('Error connecting: ' + error.message, 'error');
        }
    });
}

function saveSettings() {
    const settings = {
        balance,
        initialBalance,
        riskPerTrade,
        strategy,
        autoInterval,
        currentMode,
        payoutPercentage,
        isAutoTrading,
        tradeHistory,
        totalTrades,
        winningTrades,
        losingTrades,
        consecutiveWins,
        consecutiveLosses,
        // Add Quantum Edge specific settings
        quantumSettings: {
            useMartingale: true // Enable martingale by default
        }
    };

    chrome.storage.local.set({settings});
}

function loadSettings() {
    chrome.storage.local.get('settings', function(data) {
        if (data.settings) {
            const s = data.settings;

            // Store settings in memory
            balance = s.balance || balance;
            initialBalance = s.initialBalance || initialBalance;
            riskPerTrade = s.riskPerTrade || riskPerTrade;
            strategy = s.strategy || strategy;
            autoInterval = s.autoInterval || autoInterval;
            currentMode = s.currentMode || currentMode;
            payoutPercentage = s.payoutPercentage || payoutPercentage;
            tradeHistory = s.tradeHistory || [];
            totalTrades = s.totalTrades || 0;
            winningTrades = s.winningTrades || 0;
            losingTrades = s.losingTrades || 0;
            consecutiveWins = s.consecutiveWins || 0;
            consecutiveLosses = s.consecutiveLosses || 0;

            // Load Quantum Edge specific settings
            if (!s.quantumSettings) {
                // If quantumSettings doesn't exist, create it with default values
                s.quantumSettings = {
                    useMartingale: true // Enable martingale by default
                };
            }

            // Update mode selection UI
            setMode(currentMode);

            addNotification('Settings loaded', 'info');
        }
    });
}

// Listen for messages from content script
chrome.runtime.onMessage.addListener(function(message, _sender, sendResponse) {
    console.log('Received message in popup:', message);

    if (message.action === "connectionEstablished") {
        isConnected = true;
        connectionStatusElement.innerHTML = '<i class="fas fa-plug"></i> <span>Connected to Pocket Option</span>';
        connectionStatusElement.classList.add('connected');
        connectBtn.textContent = 'Connected';
        connectBtn.style.backgroundColor = '#10b981';
        connectBtn.disabled = true;

        if (message.balance) {
            balance = message.balance;
            initialBalance = message.balance;
            updateBalance(); // Make sure to update the UI
        }

        addNotification('Connected to Pocket Option successfully', 'success');
        sendResponse({success: true});

        // Automatically open floating interface
        setTimeout(() => {
            chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
                if (!tabs || tabs.length === 0) {
                    console.error('No active tab found');
                    addNotification('Error: No active tab found', 'error');
                    return;
                }

                try {
                    chrome.tabs.sendMessage(tabs[0].id, {action: 'openFloatingInterface'}, function(response) {
                        // Check for error
                        if (chrome.runtime.lastError) {
                            console.error('Error opening floating interface:', chrome.runtime.lastError);
                            addNotification('Error opening floating interface. Please try again.', 'error');
                            return;
                        }

                        // Close the popup window
                        window.close();
                    });
                } catch (error) {
                    console.error('Error sending openFloatingInterface message:', error);
                    addNotification('Error opening floating interface: ' + error.message, 'error');
                }
            });
        }, 1000);
    } else if (message.action === "updateBalance") {
        if (message.balance) {
            balance = message.balance;
            updateBalance();
            addNotification(`Balance updated: $${balance.toFixed(2)}`, 'info');
        }
    }

    return true;
});

// Function to test Neural Pulse interface directly
function testNeuralPulse() {
    // Show notification
    addNotification('Opening Neural Pulse test interface...', 'info');

    // Open Neural Pulse in a new tab for testing
    chrome.tabs.create({ url: chrome.runtime.getURL('neural-pulse.html') }, function(tab) {
        console.log('Neural Pulse test interface opened in tab:', tab.id);

        // Close the popup
        setTimeout(() => {
            window.close();
        }, 1000);
    });
}

// Initialize UI
setMode('quantum'); // Set default mode
loadSettings();

// Add initial notification
addNotification('Binary Options Trading Bot initialized', 'info');
