import{S as C,i as z,s as y,Q as N,c as b,a2 as P,e as O,f as R,a4 as M,n as w,h as f,a6 as W,a7 as u,a as l,b as k,I as L,J as I,K as B,L as A,d as $,M as T,g as F,j as p,y as X,R as Y,z as j,Y as D,u as q,t as K,B as J,Z as V,ae as Q,P as U,V as Z,W as G,X as ee}from"./SpinnerAugment-BRymMBwV.js";import{B as se,a as oe}from"./github-DCBOV_oD.js";import{C as te}from"./IconButtonAugment-5yqT_m78.js";function ne(o){if(window.augmentPerformance=window.augmentPerformance||{},window.augmentPerformance.initialized)return;window.augmentPerformance.initialized=!0;let e=0,s=performance.now(),a=60;const c=[];let t=0;const n=o.lowFramerateThreshold,r=o.slowInpThreshold;if(requestAnimationFrame(function d(v){const m=v-s;if(e++,m>1e3){a=1e3*e/m,e=0,s=v,c.push(a),c.length>10&&c.shift();const x=c.reduce((g,h)=>g+h,0)/c.length;a<n&&(console.error(`[Augment Performance] Slow framerate detected: ${a.toFixed(1)} fps`),console.error(`[Augment Performance] Avg framerate detected: ${x.toFixed(1)} fps`))}requestAnimationFrame(d)}),PerformanceObserver.supportedEntryTypes.includes("event"))try{new PerformanceObserver(d=>{(v=>{const m=v.getEntries().filter(h=>"interactionId"in h&&"duration"in h&&h.startTime>0);if(m.length===0)return;m.sort((h,i)=>i.duration-h.duration);const x=Math.floor(.98*m.length),g=m[Math.min(x,m.length-1)].duration;if(g>r){console.error(`[Augment Performance] Slow INP detected: ${g.toFixed(1)} ms`);const h=m[0];h&&"target"in h&&console.error("[Augment Performance] Slow interaction target:",h.target,h),g>t&&(t=g)}})(d)}).observe({entryTypes:["event","first-input"],buffered:!0}),window.webVitals!==void 0&&window.webVitals.onINP&&window.webVitals.onINP(d=>{d.value>r&&console.error(`[Augment Performance] Slow INP detected via web-vitals: ${d.value.toFixed(1)} ms`)})}catch(d){console.error("[Augment Performance] Error setting up INP monitoring:",d)}else console.warn("[Augment Performance] PerformanceObserver not supported for INP monitoring");window.augmentPerformance.getFramerate=()=>a,window.augmentPerformance.getWorstINP=()=>t}const H=16,S=200;var E;function ae(o){let e,s,a,c,t;return{c(){e=N("label"),s=N("input"),b(s,"type","checkbox"),b(s,"class","c-toggle-input svelte-mrzmei"),s.disabled=o[1],b(s,"aria-label",o[3]),b(s,"role","switch"),P(s,"disabled",o[1]),b(e,"class",a="c-toggle-track c-toggle-track-size--"+o[2]+" svelte-mrzmei"),P(e,"checked",o[0]),P(e,"disabled",o[1])},m(n,r){O(n,e,r),R(e,s),s.checked=o[0],c||(t=[M(s,"change",o[6]),M(s,"keydown",o[4]),M(e,"change",o[5])],c=!0)},p(n,[r]){2&r&&(s.disabled=n[1]),8&r&&b(s,"aria-label",n[3]),1&r&&(s.checked=n[0]),2&r&&P(s,"disabled",n[1]),4&r&&a!==(a="c-toggle-track c-toggle-track-size--"+n[2]+" svelte-mrzmei")&&b(e,"class",a),5&r&&P(e,"checked",n[0]),6&r&&P(e,"disabled",n[1])},i:w,o:w,d(n){n&&f(e),c=!1,W(t)}}}function ce(o,e,s){let{checked:a=!1}=e,{disabled:c=!1}=e,{size:t=2}=e,{ariaLabel:n}=e;return o.$$set=r=>{"checked"in r&&s(0,a=r.checked),"disabled"in r&&s(1,c=r.disabled),"size"in r&&s(2,t=r.size),"ariaLabel"in r&&s(3,n=r.ariaLabel)},[a,c,t,n,function(r){c||r.key!=="Enter"&&r.key!==" "||(r.preventDefault(),s(0,a=!a))},function(r){u.call(this,o,r)},function(){a=this.checked,s(0,a)}]}var _;(E={enabled:((_=window.augmentFlags)==null?void 0:_.enablePerformanceMonitoring)??!1,lowFramerateThreshold:H,slowInpThreshold:S}).enabled&&ne({lowFramerateThreshold:E.lowFramerateThreshold||H,slowInpThreshold:E.slowInpThreshold||S});class Ce extends C{constructor(e){super(),z(this,e,ce,ae,y,{checked:0,disabled:1,size:2,ariaLabel:3})}}function re(o){let e,s,a=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 448 512"},o[0]],c={};for(let t=0;t<a.length;t+=1)c=l(c,a[t]);return{c(){e=k("svg"),s=new L(!0),this.h()},l(t){e=I(t,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var n=B(e);s=A(n,!0),n.forEach(f),this.h()},h(){s.a=null,$(e,c)},m(t,n){T(t,e,n),s.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M304 128a80 80 0 1 0-160 0 80 80 0 1 0 160 0m-208 0a128 128 0 1 1 256 0 128 128 0 1 1-256 0M49.3 464h349.5c-8.9-63.3-63.3-112-129-112h-91.4c-65.7 0-120.1 48.7-129 112zM0 482.3C0 383.8 79.8 304 178.3 304h91.4c98.5 0 178.3 79.8 178.3 178.3 0 16.4-13.3 29.7-29.7 29.7H29.7C13.3 512 0 498.7 0 482.3"/>',e)},p(t,[n]){$(e,c=F(a,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 448 512"},1&n&&t[0]]))},i:w,o:w,d(t){t&&f(e)}}}function ie(o,e,s){return o.$$set=a=>{s(0,e=l(l({},e),p(a)))},[e=p(e)]}class ze extends C{constructor(e){super(),z(this,e,ie,re,y,{})}}function le(o){let e,s,a=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 448 512"},o[0]],c={};for(let t=0;t<a.length;t+=1)c=l(c,a[t]);return{c(){e=k("svg"),s=new L(!0),this.h()},l(t){e=I(t,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var n=B(e);s=A(n,!0),n.forEach(f),this.h()},h(){s.a=null,$(e,c)},m(t,n){T(t,e,n),s.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M248 72c0-13.3-10.7-24-24-24s-24 10.7-24 24v160H40c-13.3 0-24 10.7-24 24s10.7 24 24 24h160v160c0 13.3 10.7 24 24 24s24-10.7 24-24V280h160c13.3 0 24-10.7 24-24s-10.7-24-24-24H248z"/>',e)},p(t,[n]){$(e,c=F(a,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 448 512"},1&n&&t[0]]))},i:w,o:w,d(t){t&&f(e)}}}function de(o,e,s){return o.$$set=a=>{s(0,e=l(l({},e),p(a)))},[e=p(e)]}class ye extends C{constructor(e){super(),z(this,e,de,le,y,{})}}function he(o){let e;const s=o[7].default,a=U(s,o,o[17],null);return{c(){a&&a.c()},m(c,t){a&&a.m(c,t),e=!0},p(c,t){a&&a.p&&(!e||131072&t)&&Z(a,s,c,c[17],e?ee(s,c[17],t,null):G(c[17]),null)},i(c){e||(q(a,c),e=!0)},o(c){K(a,c),e=!1},d(c){a&&a.d(c)}}}function me(o){let e,s,a;const c=[{size:o[6]},{variant:ue},{color:o[0]},{highContrast:o[1]},{disabled:o[2]},{class:`c-badge-icon-btn__base-btn ${o[4]}`},o[3]];let t={$$slots:{default:[he]},$$scope:{ctx:o}};for(let n=0;n<c.length;n+=1)t=l(t,c[n]);return s=new te({props:t}),s.$on("click",o[8]),s.$on("keyup",o[9]),s.$on("keydown",o[10]),s.$on("mousedown",o[11]),s.$on("mouseover",o[12]),s.$on("focus",o[13]),s.$on("mouseleave",o[14]),s.$on("blur",o[15]),s.$on("contextmenu",o[16]),{c(){e=N("div"),X(s.$$.fragment),b(e,"class",Y(`c-badge-icon-btn c-badge-icon-btn--${o[5].variant} c-badge-icon-btn--size-${o[6]}`)+" svelte-1im94um")},m(n,r){O(n,e,r),j(s,e,null),a=!0},p(n,[r]){const d=95&r?F(c,[64&r&&{size:n[6]},0,1&r&&{color:n[0]},2&r&&{highContrast:n[1]},4&r&&{disabled:n[2]},16&r&&{class:`c-badge-icon-btn__base-btn ${n[4]}`},8&r&&D(n[3])]):{};131072&r&&(d.$$scope={dirty:r,ctx:n}),s.$set(d)},i(n){a||(q(s.$$.fragment,n),a=!0)},o(n){K(s.$$.fragment,n),a=!1},d(n){n&&f(e),J(s)}}}let ue="ghost";function we(o,e){return typeof o=="string"&&["accent","neutral","error","success","warning","info"].includes(o)?o:e}function fe(o,e,s){let a,c;const t=["color","highContrast","disabled"];let n=V(e,t),{$$slots:r={},$$scope:d}=e;const v=Q(se.CONTEXT_KEY);let{color:m=we(v.color,"neutral")}=e,{highContrast:x=!1}=e,{disabled:g=!1}=e,h=v.size;return o.$$set=i=>{e=l(l({},e),p(i)),s(18,n=V(e,t)),"color"in i&&s(0,m=i.color),"highContrast"in i&&s(1,x=i.highContrast),"disabled"in i&&s(2,g=i.disabled),"$$scope"in i&&s(17,d=i.$$scope)},o.$$.update=()=>{s(4,{class:a,...c}=n,a,(s(3,c),s(18,n)))},[m,x,g,c,a,v,h,r,function(i){u.call(this,o,i)},function(i){u.call(this,o,i)},function(i){u.call(this,o,i)},function(i){u.call(this,o,i)},function(i){u.call(this,o,i)},function(i){u.call(this,o,i)},function(i){u.call(this,o,i)},function(i){u.call(this,o,i)},function(i){u.call(this,o,i)},d]}const Fe={Root:oe,IconButton:class extends C{constructor(o){super(),z(this,o,fe,me,y,{color:0,highContrast:1,disabled:2})}}};function ge(o){let e,s,a=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 384 512"},o[0]],c={};for(let t=0;t<a.length;t+=1)c=l(c,a[t]);return{c(){e=k("svg"),s=new L(!0),this.h()},l(t){e=I(t,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var n=B(e);s=A(n,!0),n.forEach(f),this.h()},h(){s.a=null,$(e,c)},m(t,n){T(t,e,n),s.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M320 464c8.8 0 16-7.2 16-16V160h-80c-17.7 0-32-14.3-32-32V48H64c-8.8 0-16 7.2-16 16v384c0 8.8 7.2 16 16 16zM0 64C0 28.7 28.7 0 64 0h165.5c17 0 33.3 6.7 45.3 18.7l90.5 90.5c12 12 18.7 28.3 18.7 45.3V448c0 35.3-28.7 64-64 64H64c-35.3 0-64-28.7-64-64z"/>',e)},p(t,[n]){$(e,c=F(a,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 384 512"},1&n&&t[0]]))},i:w,o:w,d(t){t&&f(e)}}}function pe(o,e,s){return o.$$set=a=>{s(0,e=l(l({},e),p(a)))},[e=p(e)]}class ke extends C{constructor(e){super(),z(this,e,pe,ge,y,{})}}function ve(o){let e,s,a=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 576 512"},o[0]],c={};for(let t=0;t<a.length;t+=1)c=l(c,a[t]);return{c(){e=k("svg"),s=new L(!0),this.h()},l(t){e=I(t,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var n=B(e);s=A(n,!0),n.forEach(f),this.h()},h(){s.a=null,$(e,c)},m(t,n){T(t,e,n),s.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M288 0c-8.5 0-17 1.7-24.8 5.1L53.9 94.8C40.6 100.5 32 113.5 32 128s8.6 27.5 21.9 33.2l209.3 89.7c7.8 3.4 16.3 5.1 24.8 5.1s17-1.7 24.8-5.1l209.3-89.7c13.3-5.7 21.9-18.8 21.9-33.2s-8.6-27.5-21.9-33.2L312.8 5.1C305 1.7 296.5 0 288 0m-5.9 49.2c1.9-.8 3.9-1.2 5.9-1.2s4 .4 5.9 1.2L477.7 128l-183.8 78.8c-1.9.8-3.9 1.2-5.9 1.2s-4-.4-5.9-1.2L98.3 128zM53.9 222.8C40.6 228.5 32 241.5 32 256s8.6 27.5 21.9 33.2l209.3 89.7c7.8 3.4 16.3 5.1 24.8 5.1s17-1.7 24.8-5.1l209.3-89.7c13.3-5.7 21.9-18.8 21.9-33.2s-8.6-27.5-21.9-33.2l-31.2-13.4-60.9 26.1 47.7 20.5-183.8 78.8c-1.9.8-3.9 1.2-5.9 1.2s-4-.4-5.9-1.2L98.3 256l47.7-20.5-60.9-26.1zm0 128C40.6 356.5 32 369.5 32 384s8.6 27.5 21.9 33.2l209.3 89.7c7.8 3.4 16.3 5.1 24.8 5.1s17-1.7 24.8-5.1l209.3-89.7c13.3-5.7 21.9-18.8 21.9-33.2s-8.6-27.5-21.9-33.2l-31.2-13.4-60.9 26.1 47.7 20.5-183.8 78.8c-1.9.8-3.9 1.2-5.9 1.2s-4-.4-5.9-1.2L98.3 384l47.7-20.5-60.9-26.1z"/>',e)},p(t,[n]){$(e,c=F(a,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 576 512"},1&n&&t[0]]))},i:w,o:w,d(t){t&&f(e)}}}function be(o,e,s){return o.$$set=a=>{s(0,e=l(l({},e),p(a)))},[e=p(e)]}class Le extends C{constructor(e){super(),z(this,e,be,ve,y,{})}}export{Fe as B,ke as F,Le as L,ye as P,Ce as T,ze as U};
